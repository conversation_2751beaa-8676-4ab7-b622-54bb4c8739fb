<template>
  <div>
    <a-modal v-model:visible="visible" :width="1100" :closable="false" :footer="null">
      <div class="header"><span class="title">一键飞行</span><close-outlined @click="close" style="color: #ffffff" /></div>
      <div class="content">
        <marsWork @onload="onload" :options="options"> </marsWork>
        <div class="operation">
          <div class="op-left">
            <a-tooltip title="目标点飞行" placement="bottom" :overlayStyle="{ fontSize: '12px' }">
              <div class="op-Button theTarget" @click="setPopupType(1)">
                <img :src="jobImgUrl('目标点')" />
              </div>
            </a-tooltip>
            <a-tooltip title="选择航线" placement="bottom" :overlayStyle="{ fontSize: '12px' }">
              <div class="op-Button task" @click="setPopupType(2)">
                <img :src="svg('航线')" />
              </div>
            </a-tooltip>
            <a-tooltip title="一键起飞" placement="bottom" :overlayStyle="{ fontSize: '12px' }">
              <div class="op-Button takeoff" @click="setPopupType(3)">
                <img :src="jobImgUrl('起飞')" />
              </div>
            </a-tooltip>
            <a-tooltip title="设置" placement="bottom" :overlayStyle="{ fontSize: '12px' }">
              <div class="op-Button Settings" @click="setPopupType(4)">
                <img :src="jobImgUrl('设置')" />
              </div>
            </a-tooltip>
            <div class="op-select">
              <a-select
                ref="select"
                class="select-box"
                style="width: 176px"
                v-model:value="selectDevice"
                :options="deviceList"
                @change="onSelectChange"
                labelInValue
                :fieldNames="{ label: 'deviceName', value: 'deviceId' }"
                @focus="onSelectFocus"
              >
                <template #suffixIcon><caret-down-outlined style="color: #949595" /></template>
              </a-select>
            </div>
            <a-button type="primary" :disabled="disabledButton" :loading="loading" @click="carryOut">执行</a-button>
          </div>
          <div class="op-right">
            <div class="remoteControl" :class="{ disabled: isDisabled }" v-if="hasPermission('system:user:remotecontrol')" @click="navToRemote">
              <img :src="jobImgUrl('远程控制')" />
              <span class="text">远程</span>
            </div>
            <!-- <div class="op-Button">
              <span class="title">经度</span>
              <a-input class="op-input" v-model:value="latlng[0]" />
            </div>
            <div class="op-Button">
              <span class="title">纬度</span>
              <a-input class="op-input" v-model:value="latlng[1]" />
            </div> -->
          </div>
        </div>
        <div class="Select-box">
          <targetPointSetting
            ref="targetPointSettingRef"
            :TaskConf="TaskConf"
            :selectDevice="selectDevice"
            :latlng="latlng"
            :altitudeHigh="altitudeHigh"
            v-show="popupType == 1"
            @refreshPoint="refreshPoint"
          ></targetPointSetting>

          <task
            ref="taskRef"
            @plotCourse="plotCourse"
            :TaskConf="TaskConf"
            :selectDevice="selectDevice"
            v-show="popupType == 2"
            :sysOrgCodeList="sysOrgCodeList"
          ></task>

          <takeOffSetup ref="takeOffSetupRef" :TaskConf="TaskConf" :selectDevice="selectDevice" v-show="popupType == 3"></takeOffSetup>

          <globalSetting
            :TaskConf="TaskConf"
            :selectDevice="selectDevice"
            ref="globalSettingRef"
            v-show="popupType == 4"
            @setTakeoffConfig="setTakeoffConfig"
            @setTaskConfig="setTaskConfig"
          >
          </globalSetting>
        </div>
      </div>
    </a-modal>
  </div>
</template>
<script lang="ts" setup>
  import { ref, onMounted, nextTick } from 'vue';
  import { CloseOutlined } from '@ant-design/icons-vue';
  import { message } from 'ant-design-vue';
  import { getIconUrl } from '/@/utils';
  import marsWork from '@/views/components/mars-work/mars-map.vue';
  import globalSetting from './globalSetting.vue';
  import targetPointSetting from './targetPointSetting.vue';
  import takeOffSetup from './takeOffSetup.vue';
  import task from './task.vue';
  import * as mars3d from 'mars3d';
  import * as Cesium from 'mars3d-cesium';
  import { taskApi } from './data.api';
  import { useRouter, useRoute } from 'vue-router';
  import { usePermission } from '/@/hooks/web/usePermission';
  import { CaretDownOutlined } from '@ant-design/icons-vue';
  const { hasPermission } = usePermission();

  let visible = ref<boolean>(false);
  let popupType = ref<number>(0);
  let deviceList = ref([]); // 设备列表
  let selectDevice = ref({}); // 当前选择的设备
  const targetPointSettingRef = ref();
  const TaskConf = ref({});
  const globalSettingRef = ref();
  const taskRef = ref();
  const takeOffSetupRef = ref();
  let disabledButton = ref(true);
  const router = useRouter();
  let isDisabled = ref(false);

  let Map = null;
  let graphicLayer = null;
  let currentMarker = null; // 用来存储当前的标记
  let latlng = ref([]); // 经纬度
  let altitudeHigh = ref(); //海拔高
  let graphic = null; // 存储当前绘制的航线
  let airportIcon = null; // 存储机场的图标
  let sysOrgCodeList = ref();
  const showModal = async (orcCode) => {
    visible.value = !visible.value;
    sysOrgCodeList.value = orcCode;
    if (visible.value && Object.keys(selectDevice.value).length === 0) {
      await getDeviceList();
    }
    // await nextTick();
    if (visible.value) {
      await getFastTaskConf();
      setCameraView();
    }
  };
  // 获取图片路径
  const jobImgUrl = (name: string, suffix = 'svg') => {
    return getIconUrl(name, 'icons/FastTask/', suffix);
  };
  const svg = (name) => {
    return `/mapView/${name}.svg`; //拼接文件路径
  };
  const options = {
    control: {
      homeButton: false,
      baseLayerPicker: false,
      sceneModePicker: false,
      navigationHelpButton: false,
      showRenderLoopErrors: false,
      contextmenu: {
        hasDefault: false,
      },
      mouseDownView: false,
      zoom: false,
      compass: false,
      locationBar: false,
    },
  };

  const getDeviceList = async () => {
    // 调用API获取设备列表
    const res = await taskApi.deviceQuery({ deviceType: 3, pageNo: 1, pageSize: 200, sysOrgCode: sysOrgCodeList.value });
    // 过滤出状态不为5的设备并赋值给deviceList.value

    // 这里的'5'代表机场离线
    // deviceList.value = res.records;
    deviceList.value = res.records.filter((item) => item.state !== '5' && item.state == 0); // 过滤出状态不为5的设备  并且为空闲的机场
    if (Object.keys(selectDevice.value).length === 0 && deviceList.value.length > 0) {
      selectDevice.value = {
        disabled: undefined,
        key: deviceList.value[0].deviceId,
        label: deviceList.value[0].deviceName,
        option: deviceList.value[0],
        originLabel: deviceList.value[0].deviceName,
        value: deviceList.value[0].deviceId,
      };

      setCameraView();
    }

    if (deviceList.value.length > 0) {
      isDisabled.value = false;
    } else {
      isDisabled.value = true;
    }

    console.log(deviceList.value, 'deviceList');
  };

  const getFastTaskConf = async () => {
    const res = await taskApi.fastTaskConf({ sysOrgCode: selectDevice.value?.option?.sysOrgCode });
    TaskConf.value = res;
    globalSettingRef.value.setConfiguration(res);
    console.log(globalSettingRef.value);
  };

  const onSelectFocus = () => {
    getDeviceList();
    if (![0, 4].includes(popupType.value)) {
      disabledButton.value = false;
    }
  };

  const onSelectChange = (event) => {
    console.log(event);
    setCameraView();
  };

  const setTakeoffConfig = (event) => {
    console.log(event);
    Object.assign(TaskConf.value?.takeoffConfig, event);
  };

  const setTaskConfig = (event) => {
    console.log(event);
    Object.assign(TaskConf.value?.taskConfig, event);
  };

  const setPopupType = (type: number) => {
    // 如果selectDevice.value为空没有选择执行设备，则直接返回
    if (Object.keys(selectDevice.value).length === 0) {
      message.warning('请选择执行设备');
      return;
    }
    popupType.value = popupType.value === type ? 0 : type;
    if (type == 4 || popupType.value == 0) {
      disabledButton.value = true;
    } else {
      disabledButton.value = false;
    }

    if (popupType.value == 2) {
      taskRef.value.getWaylineQuery();
    }

    if (popupType.value != 1 && currentMarker) {
      Map.graphicLayer.removeGraphic(currentMarker); // 移除之前的标点
    }

    if (popupType.value != 2 && graphic) {
      Map.graphicLayer.removeGraphic(graphic); // 移除之前的标点
      taskRef.value.routeDeletion();
    }
  };

  const closePopup = () => {
    popupType.value = 0;
    disabledButton.value = true;
  };

  const close = () => {
    visible.value = false;
  };

  let loading = ref<Boolean>(false);

  const carryOut = () => {
    const actionMap = {
      1: () => targetPointSettingRef.value.carryOut(popupType),
      2: () => taskRef.value.carryOut(popupType),
      3: () => takeOffSetupRef.value.carryOut(popupType),
    };

    if (actionMap[popupType.value]) {
      actionMap[popupType.value]();
      UpdateConfig();
      loading.value = true;
      disabledButton.value = true;
      setTimeout(() => {
        loading.value = false;
      }, 1500); // 1000 毫秒内只会执行一次
    }
  };

  const UpdateConfig = () => {
    if (!TaskConf.value || typeof TaskConf.value.takeoffConfig !== 'object' || typeof TaskConf.value.taskConfig !== 'object') {
      console.error('TaskConf is not correctly initialized.');
      return;
    }
    const params = {
      projectId: localStorage.getItem('Project_Id'),
      sysOrgCode: selectDevice.value?.option?.sysOrgCode,
      takeoffConfig: {
        ...TaskConf.value.takeoffConfig,
      },
      taskConfig: {
        ...TaskConf.value.taskConfig,
      },
    };

    taskApi.confUpdate(params);
  };

  const navToRemote = () => {
    router.push({
      path: '/remoteOverTwo',
      query: {
        deviceId: selectDevice.value.option?.deviceId,
        deviceSn: selectDevice.value.option?.deviceSn,
        droneId: selectDevice.value.option?.deviceList[0].deviceId,
        aircraftDeviceId: selectDevice.value.option?.deviceList[0].id,
      },
    });
  };

  const onload = (map) => {
    Map = map;
    graphicLayer = new mars3d.layer.GraphicLayer();
    map.addLayer(graphicLayer);
    map.on(mars3d.EventType.click, async (event) => {
      console.log(event);
      if (popupType.value != 1) {
        return;
      }
      // 获取点击的笛卡尔坐标
      const cartesian = event.cartesian;
      if (!cartesian) {
        console.log('无效的点击位置');
        return;
      }

      // 将笛卡尔坐标转换为经纬度
      const cartographic = Cesium.Cartographic.fromCartesian(cartesian);
      const longitude = Cesium.Math.toDegrees(cartographic.longitude);
      const latitude = Cesium.Math.toDegrees(cartographic.latitude);

      // 输出经纬度
      console.log('点击的经度:', longitude);
      console.log('点击的纬度:', latitude);
      latlng.value = [longitude, latitude];

      if (currentMarker) {
        map.graphicLayer.removeGraphic(currentMarker); // 移除之前的标点
      }

      // 添加标点
      currentMarker = new mars3d.graphic.PointEntity({
        position: [longitude, latitude, 0], // 位置使用经纬度
        style: {
          color: '#317aff',
          pixelSize: 10,
          clampToGround: true,
        },
      });

      map.graphicLayer.addGraphic(currentMarker);

      const res = await mars3d.PointUtil.getSurfaceHeight(map.scene, [longitude, latitude], { has3dtiles: false });
      console.log(res, 'res/*/*/--898');
      altitudeHigh.value = res.height.toFixed(2);
    });

    setCameraView();
  };

  const plotCourse = (positions) => {
    if (graphic) {
      Map.graphicLayer.removeGraphic(graphic); // 移除之前的标点
    }
    graphic = new mars3d.graphic.PolylineEntity({
      positions: positions,
      style: {
        width: 3,
        color: '#3388ff',
        clampToGround: true,
      },
      flyTo: true,
      flyToOptions: {
        scale: 3.5,
        duration: 2,
      },
    });
    Map.graphicLayer.addGraphic(graphic);
  };

  const refreshPoint = (formState) => {
    // 确保formState包含目标经纬度
    if (formState.targetLongitude && formState.targetLatitude) {
      // 如果存在当前标记，则移除
      if (currentMarker) {
        Map.graphicLayer.removeGraphic(currentMarker);
      }

      // 添加新的标点
      currentMarker = new mars3d.graphic.PointEntity({
        position: [formState.targetLongitude, formState.targetLatitude, 0],
        style: {
          color: '#317aff',
          pixelSize: 10,
          clampToGround: true,
        },
      });

      Map.graphicLayer.addGraphic(currentMarker);
    } else {
      console.warn('目标经纬度不完整，无法刷新点。');
    }
  };

  const setCameraView = () => {
    let str = JSON.parse(localStorage.getItem('latitudeLongitude'));
    console.log(str[0], str[1], '项目经纬度');
    Map.setCameraView(
      {
        lng: selectDevice.value?.option?.longitude || str[0],
        lat: selectDevice.value?.option?.latitude || str[1],
        alt: 5000,
      },
      {
        duration: 0.1,
      }
    );

    airfieldLocation();
  };

  const airfieldLocation = () => {
    if (airportIcon) {
      Map.graphicLayer.removeGraphic(airportIcon); // 移除之前的标点
    }
    airportIcon = new mars3d.graphic.BillboardEntity({
      position: new mars3d.LngLatPoint(selectDevice.value?.option?.longitude || 0, selectDevice.value?.option?.latitude || 0, 0),
      style: {
        image: jobImgUrl('机场'),
        clampToGround: true,
      },
    });

    Map.graphicLayer.addGraphic(airportIcon);
  };
  defineExpose({
    showModal,
  });
  onMounted(async () => {
    // await
    // getDeviceList()
  });
</script>
<style lang="less" scoped>
  .disabled {
    pointer-events: none; /* 禁止点击 */
    opacity: 0.5; /* 置灰效果 */
    background-color: #d3d3d3; /* 也可以使用背景颜色来加强置灰效果 */
  }

  div {
    .header {
      display: flex;
      background-color: #171b1e;
      align-items: center;
      justify-content: space-between;
      padding: 13px 19px;
      .title {
        font-size: 16px;
        color: #ffffff;
      }
    }
    .content {
      width: 100%;
      height: 720px;
      position: relative;
      background-color: #282c2f;
      padding: 8px;
      .operation {
        position: absolute;
        top: 0;
        padding: 13px 30px;
        display: flex;
        justify-content: space-between;
        width: 100%;

        .op-left {
          display: flex;
          gap: 12px;
          :deep(.is-disabled) {
            background: #b2b2b2 !important;
            color: #ffffff !important;
            border: 1px solid #b2b2b2 !important;
          }
          .op-Button {
            width: 32px;
            height: 32px;
            border-radius: 4px;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            img {
            }
          }
          .theTarget {
            // background: rgba(237, 144, 97, 0.5);
            background-color: #d17648;
            border: 1px solid #ed9061;
          }
          .theTarget:hover {
            background-color: #df8252;
          }
          .op-Button {
            img {
            }
          }
          .task {
            // background: rgba(97, 143, 237, 0.5);
            background-color: #477ae5;
            border: 1px solid #618fed;
          }
          .task:hover {
            background-color: #4e86f8;
          }
          .op-Button {
            img {
            }
          }
          .takeoff {
            // background: rgba(97, 129, 237, 0.5);
            background-color: #4367e3;
            border: 1px solid #618fed;
          }
          .takeoff:hover {
            background-color: #5279fa;
          }
          .op-Button {
            img {
            }
          }
          .Settings {
            // background: rgba(97, 206, 237, 0.5);
            background-color: #42b0d0;
            border: 1px solid #61ceed;
          }
          .Settings:hover {
            background-color: #47bbdd;
          }
          .op-select {
            .select-box {
              width: 176px;
              height: 32px;
              background: #212121;
              border-radius: 2px;
              border: 1px solid rgba(255, 255, 255, 0.3);
              :deep(.ant-select-selector) {
                background: rgba(35, 37, 38, 0.03);
                border-radius: 2px;
                border: 1px solid rgba(255, 255, 255, 0.2);
              }
              :deep(.ant-select-selection-item) {
                color: #ffffff;
              }
              :deep(.ant-select-suffix) {
                color: #ffffff;
              }
            }
          }
        }
        .op-right {
          display: flex;
          align-items: center;
          justify-content: center;
          .remoteControl {
            width: 61px;
            height: 32px;
            // background: rgba(49, 99, 255, 0.8);
            background-color: #3163ff;
            border-radius: 2px;
            border: 1px solid #3163ff;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            gap: 3px;
            .text {
              font-weight: 500;
              font-size: 12px;
              color: #ffffff;
            }
          }
          gap: 22px;
          .op-Button {
            display: flex;
            align-items: center;
            gap: 11px;
            .title {
              font-weight: 400;
              font-size: 14px;
              color: #ffffff;
              width: 28px;
              height: 20px;
            }
            .op-input {
              width: 110px;
              height: 32px;
              background: rgba(0, 0, 0, 0.5);
              border-radius: 2px;
              border: 1px solid rgba(255, 255, 255, 0.3);
              color: #ffffff;
            }
          }
        }
      }
      .Select-box {
        position: absolute;
        top: 67px;
        left: 29px;
      }
    }
  }
</style>
