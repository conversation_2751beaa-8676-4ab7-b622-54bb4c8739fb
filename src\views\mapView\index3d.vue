<template>
  <div class="Container Row">
    <div class="Row Aside" style="z-index: 99999999" v-show="!waylineListref.isShowEditor">
      <div id="drawer" class="Col" :class="{ Hidden: !drawerRef.visible }">
        <div class="Row Tabs">
          <a-button :class="{ Focus: drawerRef.showType == 0 }" @click="drawerRef.showList(0)">设备</a-button>
          <!-- <a-button :class="{ Focus: drawerRef.showType == 1 }" @click="drawerRef.showList(1)">航线</a-button> -->
          <!-- 新的 -->
          <a-button :class="{ Focus: drawerRef.showType == 1 }" @click="waylineListref.changShowType()">航线</a-button>
          <!-- 新的 -->
          <a-button :class="{ Focus: drawerRef.showType == 2 }" @click="drawerRef.showList(2)">标记</a-button>
        </div>
        <sidebar_deviceList
          :drawerRef="drawerRef"
          :deviceUtil="deviceUtil"
          :deviceStatus="deviceStatus"
          :class="{ Hidden: drawerRef.showType != 0 }"
        ></sidebar_deviceList>
        <!-- <sidebar_waylineList
          :drawerRef="drawerRef"
          :waylineEditor="waylineEditor"
          :deviceUtil="deviceUtil"
          :class="{ Hidden: drawerRef.showType != 1 }"
        ></sidebar_waylineList> -->
        <a-modal v-model:visible="waylineEditor.uploadRtPopupVisible" okText="上传" title="上传航线" @ok="waylineEditor.readyForUpload()">
          <div style="padding: 35px;display: flex;gap:10px;  flex-direction: column;">
            <!-- <a-tree-select
              v-model:value="waylineEditor.uploadDepartment"
              show-search
              tree-default-expand-all
              style="width: 33.33%"
              :dropdown-style="{ width: 'auto' }"
              :tree-data="drawerRef.myDepart"
              :field-names="{children:'children', label:'departName', value: 'orgCode' }"
              :dropdownMatchSelectWidth="false"
              placeholder="请选择部门"
              >
                <template #title="{ value: val, departName }" style="width: 500px;">
                  <div >{{ departName }}</div>
                </template>
            </a-tree-select> -->
            <div style="display: flex; gap:25px;">
              <a-select
              v-model:value="waylineEditor.uploadDepartment"
              placeholder="请选择部门"
              style="width: 100%"
              >
                <a-select-option v-for="item in drawerRef.myDepart" :value="item.orgCode">{{ item.departName }}</a-select-option>
              </a-select>
              <a-button type="primary" :size="size" @click="waylineEditor.showUpload()">
                <template #icon>
                  <DownloadOutlined />
                </template>
                选择文件
              </a-button>
            </div>

            <div v-show="waylineEditor?.selectedFile" style="white-space: nowrap; text-overflow: ellipsis; overflow: hidden; width: 200px;display: flex;flex-wrap: wrap;align-content: center;">
              <!-- {{ waylineEditor?.selectedFile?.target?.files?.[0]?.name }} -->
              <div v-for="item in waylineEditor?.selectedFile?.target?.files" style="margin-right: 2px;">
                {{ item.name }}
              </div>

            </div>
          </div>
        </a-modal>
        <!-- 新的 -->
        <sidebar_waylineListData
          :waylineEditor="waylineEditor"
          :drawerRef="drawerRef"
          :deviceUtil="deviceUtil"
          :waylineListref="waylineListref"
          :class="{ Hidden: drawerRef.showType != 1 }"
        ></sidebar_waylineListData>
        <sidebar_markerList :drawerRef="drawerRef" :markerEditor="markerEditor" :class="{ Hidden: drawerRef.showType != 2 }"></sidebar_markerList>
      </div>
      <span @click="drawerRef.toggle()"
        ><img
          :src="svg((drawerRef.visible ? '收起' : '展开') + drawerRef.hover)"
          :key="drawerRef.visible"
          @mouseenter="drawerRef.hover = 1"
          @mouseleave="drawerRef.hover = 0"
      /></span>
    </div>
    <!-- 航线保存，退出 -->
    <wayline_editHeader v-if="waylineListref.isShowEditor" :waylineListref="waylineListref" :mars3d="mars3d" :mapUtil="mapUtil"></wayline_editHeader>
    <!-- 航线动作 -->
    <add_action_list v-if="waylineListref.isShowEditor" :waylineListref="waylineListref"></add_action_list>
    <waypoint_list v-if="waylineListref.isShowEditor" :waylineListref="waylineListref" :mapUtil="mapUtil"></waypoint_list>
    <div id="backscreen" class="Flex Row">
      <input id="filechooser" type="file" multiple="multiple" accept=".kmz" @change="waylineEditor.handleFileChange"/>
      <!-- @change="waylineEditor.upload" -->
      <div id="map" class="Flex Swap"></div>

      <img
        class="Remote"
        :src="svg('远程-normal')"
        @click="remote.showConfirm = true"
        v-if="
          drawerRef.showType == 0 &&
          drawerRef.deviceList.focus >= 0 &&
          deviceStatus.deviceType == 3 &&
          deviceStatus.droneSn &&
          !deviceStatus.fullVideo &&
          hasPermission('system:user:remotecontrol') &&
          drawerRef.myDepartListArr.includes(drawerRef.deptFilterCriteria.departmentName)
        "
      />
      <!-- && -->
      <!-- hasPermission('system:user:remotecontrol') -->
      <!-- <img id="mapmode" :src="svg('3D切换' + mapUtil.maptype3d2d.value)" @click="mapUtil.toggleMode()" v-if="!deviceStatus.fullVideo" /> -->
      <img id="layer-tree-btn" :src="svg('layerInfo')" @click="showLayerTree()" v-if="!deviceStatus.fullVideo" />
      <div
        id="recognitionBox"
        v-if="drawerRef.showType == 0 && drawerRef.deviceList.focus >= 0 && hasPermission('system:GIS:AI')"
        @click="aiRecognitionRef.close()"
      >
        <img :src="svg('AITwo')" style="width: 23px" alt="" />
        <div class="dot" v-if="hasUnreadval"></div>
      </div>
      <div class="TurnAiListBox" v-if="TurnAiList">
        <div class="list" v-for="item in TurnAiList">{{ item.sceneName }}</div>
      </div>

      <layerTree :mapUtil="mapUtil" :drawerRef="drawerRef" v-show="layerTreeBoolean" ref="layerInfoChild"></layerTree>

      <pin_markTools :markerEditor="markerEditor" v-if="drawerRef.showType == 2 && drawerRef.myDepartListArr.includes(drawerRef.deptFilterCriteria.departmentName)"></pin_markTools>

      <statebar_deviceState
        :deviceStatus="deviceStatus"
        :deviceUtil="deviceUtil"
        v-if="drawerRef.showType == 0 && drawerRef.deviceList.focus >= 0 && isShowCamera"
      ></statebar_deviceState>

      <!-- <statebar_waylineState :waylineEditor="waylineEditor" v-if="drawerRef.showType == 1 && (waylineEditor.data || {}).wpmz"></statebar_waylineState> -->
      <statebar_waylineState
        :waylineListref="waylineListref"
        v-if="
          drawerRef.showType == 1 &&
          waylineListref.waylinePoints.positions.length > 0 &&
          !waylineListref.isShowEditor &&
          waylineListref.waylineListData.length > 0
        "
      ></statebar_waylineState>
      <div
        class="takeOffPoint"
        v-if="
          drawerRef.showType == 1 && waylineListref.isShowEditor && waylineListref.take_off_point.length == 0 && waylineListref.takeOffPointIsShow
        "
        >点击地图设置参考起飞点</div
      >
    </div>
    <sidebar_waylinePointConfig
      :waylineEditor="waylineEditor"
      v-if="
        drawerRef.showType == 1 && waylineEditor.data && waylineEditor.isShowEditor && waylineEditor.data.wpmz && waylineEditor.pointsIndex.end > 0
      "
    ></sidebar_waylinePointConfig>

    <aiSetup
      ref="aiSetupRef"
      @TurnAi="TurnAi"
      style="position: absolute; top: 44%; left: 59%; height: 300px; transform: translate(-50%, -50%)"
    ></aiSetup>
    <aiRecognition
      ref="aiRecognitionRef"
      @hasUnread="hasUnread"
      :data="deviceStatus"
      style="position: absolute; right: 7em; top: 6em; z-index: 10"
    ></aiRecognition>
    <!-- 新的航线 -->
    <model_Popup v-if="waylineListref.visible" :waylineListref="waylineListref"></model_Popup>
    <waypoint_action v-if="waylineListref.isShowEditor" :waylineListref="waylineListref"></waypoint_action>
    <!-- end -->
  </div>

  <pop_remoteConfirm :remote="remote" v-if="remote.showConfirm && deviceStatus.deviceType == 3"></pop_remoteConfirm>
  <div
    v-if="drawerRef.showType == 1 && waylineListref.tabBar && !waylineListref.isShowEditor && waylineListref.waylineListData.length > 0"
    class="tabBar"
    :style="
      drawerRef.visible && waylineListref.tabBarCount == 5
        ? { left: '419px', height: '160px' }
        : drawerRef.visible && waylineListref.tabBarCount == 2
        ? { left: '419px', height: '64px' }
        : !drawerRef.visible && waylineListref.tabBarCount == 2
        ? { left: '54px', height: '64px' }
        : { left: '54px' }
    "
  >
    <div
      :class="['tabBar-item', waylineListref.tabBarIndex == index ? 'tabBar-item-bg' : '']"
      v-for="(item, index) in waylineListref.tabBarCount"
      :key="item"
      @click="waylineListref.changTabBarIndex(index)"
      >{{ item }}</div
    >
  </div>
</template>
<script name="flyManage-mapView-index3d" setup>
import axios from 'axios';
import { reactive, ref, computed, watch, onMounted, onUnmounted, defineComponent, nextTick, toRaw } from 'vue';
import { useRouter, useRoute } from 'vue-router';
// import AMapLoader from "@amap/amap-jsapi-loader";
import * as mars3d from 'mars3d';
import * as Cesium from 'mars3d-cesium';
import defaultMapConfig from './map3d.config.json';

import coordtransform from 'coordtransform';

import Jessibuca from '@/views/components/Jessibuca/index.vue';

import { waylineApi } from '../wayline/data.api2';
import { waylineApiV2 } from '../wayline/data.api3';
import { markerApi } from './marker.api';
import { deviceApi } from '../equipManage/deviceManageList/data.api';
import { FullLivingApi } from '../homePage/data.api';
import { mapViewApi } from './mapView.api';
import { mapPhoto } from '../achieveDisplay/data.api';
import { onWebSocket, offWebSocket, useMyWebSocket } from '@/hooks/web/useWebSocket.ts';
import { instructPost, modeEnter } from '@/views/remoteOver/data.api';

import AgoraRTC from 'agora-rtc-sdk-ng';
import { useMessage } from '/@/hooks/web/useMessage';

import layerTree from './layerTree.vue';
import pin_markTools from './pin_markTools.vue';
import pop_remoteConfirm from './pop_remoteConfirm.vue';
import sidebar_deviceList from './sidebar_deviceList.vue';
import sidebar_waylineList from './sidebar_waylineList.vue';
import sidebar_waylineListData from './sidebar_waylineListData.vue';
import sidebar_markerList from './sidebar_markerList.vue';
import sidebar_waylineConfig from './sidebar_waylineConfig.vue';
import sidebar_waylinePointConfig from './sidebar_waylinePointConfig.vue';
import statebar_deviceState from './statebar_deviceState.vue';
import statebar_waylineState from './statebar_waylineState.vue';
import picturePreview from './picturePreview.vue';
import { VuePannellum } from '/@/components/VuePannellum';
import { initVue3Popup } from '@/views/components/mars-work/file-util';
import aiSetup from '@/views/components/aiSetup/index.vue';
import aiRecognition from '@/views/components/aiRecognition/index.vue';
import { color } from 'echarts';
import { aiAlgorithmManagementApi } from '@/views/components/aiSetup/data.api';
import { usePermission } from '/@/hooks/web/usePermission';
import { message } from 'ant-design-vue';
import model_Popup from './modelPopup.vue';
import waypoint_action from './waypoint_action.vue';
import waypoint_list from './waypoint_list.vue';
import wayline_editHeader from './wayline_editHeader.vue';
import add_action_list from './add_action_list.vue';

import { integratedData, wayLineDoneInfo } from './integratedData.ts';
import { decomposeData } from './decomposeData.ts';
import { facetayLine } from './facetayLine.ts';
import { useBasemapConfig } from '@/hooks/web/useBasemapConfig';
const { configMap } = useBasemapConfig();
const { hasPermission } = usePermission();
const { createMessage } = useMessage();
import { DownloadOutlined, } from '@ant-design/icons-vue';
var pointEditData = null;
// 新的航线数据方法
const waylineListref = ref({
  routeName:'',
  facetayLineData: [],
  facetayLinePointData: [],
  useGlobalHeight: [], // 记录每个点是否使用全局高度，是否跟随航线
  timer: null,
  isSave: true,
  takeOffPointIsShow: true, // 起飞点窗体是否显示
  isShowText: true, // 起飞点重设和取消
  isShowAddPoint: false,
  wayLineId: '',
  isShowWaylineEditor: false, // 航线是编辑还是新增
  wayLineCreateDTO: {
    droneInfo: {
      droneEnumValue: '', //飞行器机型主类型
      droneSubEnumValue: '', //飞行器机型子类型
    },
    payloadInfos: [
      {
        payloadEnumValue: '', //负载机型主类型
        payloadSubEnumValue: '', //
        payloadPositionIndex: '', //负载挂载位置
      },
    ],
    templateTypes: '0',
    wayLineJsonObject: {},
  },
  waylineSetting: {},
  actionlrIndex: 0,
  waylineListPop: {}, // 航线弹窗数据
  isShowEditor: false, // 是否隐藏tabs，显示航线航点的弹窗
  visibleEdit: true, // 弹窗是否是编辑，隐藏航线类型
  focus: 0, // 航线列表选中高亮下标
  waylineListData: [], // 航线列表数据
  visible: false,
  waylinePoints: {
    positions: [],
  }, // 航线航点列表数据
  waylineActionList: [], // 航线动作列表数据
  climb: 'vertical', //incline
  // tabs切换
  isTake_off_point: false,
  pointEditData: null,
  take_off_point: [], //起飞点坐标
  take_off_point_List: [], //起飞点graphic列表
  offPointePolyline: [], // 起飞点连线
  offPointEarthandearthlineList: [],
  floorOffPointList: [],
  waylinePolyline: [], // 航线数据
  pointsList: [], // 航点
  earthandearthlineList: [],
  floorPointList: [],
  DistanceMeasure: null, // 距离测量
  distanceStr: '0', // 总长度
  durationStr: '0', // 总时长
  waypointIndex: -1, // 当前选中航点列表下标
  aboveGroundLevelHT: 0, // 记录点击航点时的相对地面高度的差值,
  changHT: false,
  formatDistance: function (distance) {
    if (distance.includes('公里')) {
      return Number(distance.replace('公里', '')) * 1000;
    } else if (distance.includes('米')) {
      return Number(distance.replace('米', ''));
    } else {
      return 0; // 默认返回0，处理未知单位
    }
  },
  formatDuration: function (duration) {
    var minute = duration / 60,
      second = duration % 60;
    var durationStr = '';
    if (minute > 1) {
      durationStr += parseInt(minute) + ' min ';
    }
    if (second > 0) {
      durationStr += parseInt(second) + ' s';
    }
    return durationStr || '-';
  },
  actionTime: function (dataList) {
    console.log(dataList);
    let time = 0;
    dataList.forEach((item) => {
      item.forEach((val) => {
        switch (val.actionType) {
          case '1':
          case '2':
            time = time + 1;
            break;
          case '7':
            time = time + val.time;
            break;
          case '8':
          case '9':
          case '10':
          case '11':
            time = time + 3;
            break;
          case '12':
            time = time + 50;
            break;

          default:
            break;
        }
      });
    });
    return time;
  },
  changShowType: async function () {
    // waylineListref.value.waylineListData = [];
    if (deviceStatus.value.fullVideo) {
      deviceUtil.fullScreenVideo(deviceStatus.value.fullVideo); //切换设备前先切换地图显示
    }
    drawerRef.value.showType = 1;
    mapUtil.maplayer.stopDraw();
    mapUtil.maplayer.stopEditing();
    mapUtil.historyTrack = {}; // 清空历史轨迹
    mapUtil.maplayer.clear(true); // 清空地图
    await waylineApiV2.webList({ pageSize: 100, pageNo: 1, order: 'desc', column: 'createTime', sysOrgCode: drawerRef.value.deptFilterCriteria.departmentName, name:waylineListref.value.routeName }).then(function (res) {
      waylineListref.value.waylineListData = res.records;

      // waylineListref.value.waylineListData.push(...res.records);
      if (waylineListref.value.waylineListData.length > 0) {
        waylineListref.value.selectWayline(waylineListref.value.focus, waylineListref.value.waylineListData[waylineListref.value.focus]);
      }
    });
  },
  wayLineJsonObject: {},
  tabBar: false,
  tabBarIndex: 0,
  tabBarCount: 5,
  showTow: false,
  changTabBarIndex: function (index) {
    mapUtil.maplayer.stopDraw();
    mapUtil.maplayer.stopEditing();
    mapUtil.historyTrack = {}; // 清空历史轨迹
    mapUtil.maplayer.clear(true); // 清空地图
    waylineListref.value.clearData();
    waylineListref.value.tabBarIndex = index;
    let kmzData = null;
    if (waylineListref.value.showTow) {
      kmzData = facetayLine(waylineListref.value.wayLineJsonObject, '1', index);
    } else {
      kmzData = facetayLine(waylineListref.value.wayLineJsonObject, '2', index);
    }
    waylineListref.value.facetayLineData = kmzData.facetayLinePointArr;
    waylineListref.value.facetayLinePointData = kmzData.facetayLinePointWaylines;
    waylineListref.value.drawLine(waylineListref.value.facetayLinePointData, 2);
    waylineListref.value.facetayWayline(waylineListref.value.facetayLineData);
    // if (waylineListref.value.facetayLineData.length > 0) {
    //   mapUtil.map.setCameraView({ lng: waylineListref.value.facetayLineData[0][0], lat: waylineListref.value.facetayLineData[0][1] });
    // }
  },
  // 航线列表选中
  selectWayline: function (index, item) {
    waylineListref.value.tabBar = false;
    waylineListref.value.tabBarIndex = 0;
    (waylineListref.value.showTow = false), (waylineListref.value.distanceStr = '0');
    // drawerRef.value.showType = 1;
    mapUtil.maplayer.stopDraw();
    mapUtil.maplayer.stopEditing();
    mapUtil.historyTrack = {}; // 清空历史轨迹
    mapUtil.maplayer.clear(true); // 清空地图
    waylineListref.value.focus = index;
    waylineListref.value.wayLineId = item.id;
    waylineListref.value.clearData();
    waylineApiV2.webQueryById({ id: item.id, sysOrgCode: drawerRef.value.deptFilterCriteria.departmentName, }).then((res) => {
      if (JSON.stringify(res.wayLineJsonObject) == '{}') return;
      waylineListref.value.wayLineJsonObject = res.wayLineJsonObject;
      // console.log(waylineListref.value.isShowWaylineEditor);
      // 正射 // 倾斜
      if (item.templateTypes == '1' || item.templateTypes == '2') {
        let kmzData = null;
        if (item.templateTypes == '1') {
          kmzData = facetayLine(res.wayLineJsonObject, '1', 0);
          if (kmzData.showTow) {
            waylineListref.value.tabBar = true;
            waylineListref.value.showTow = true;
            waylineListref.value.tabBarCount = 2;
          }
        } else {
          waylineListref.value.tabBar = true;
          kmzData = facetayLine(res.wayLineJsonObject, '2', 0);
          waylineListref.value.tabBarCount = 5;
        }
        waylineListref.value.facetayLineData = kmzData.facetayLinePointArr;
        waylineListref.value.facetayLinePointData = kmzData.facetayLinePointWaylines;
        waylineListref.value.drawLine(waylineListref.value.facetayLinePointData, 2);
        waylineListref.value.facetayWayline(waylineListref.value.facetayLineData);
        if (waylineListref.value.facetayLineData.length > 0) {
          mapUtil.map.setCameraView({ lng: waylineListref.value.facetayLineData[0][0], lat: waylineListref.value.facetayLineData[0][1] });
        }
        return;
      }

      // 航点航线
      if (item.templateTypes == '0') {
        const kmzData = decomposeData(res.wayLineJsonObject);
        console.log('取出的动作', kmzData);
        waylineListref.value.useGlobalHeight = kmzData.useGlobalHeight;
        console.log('解析的是否跟随航线高度', waylineListref.value.useGlobalHeight);
        waylineListref.value.waylineListPop = kmzData.waylineListPop;
        waylineListref.value.waylineListPop.name = res.name;
        waylineListref.value.waylineSetting = kmzData.waylineSetting;
        waylineListref.value.waylineActionList = kmzData.waylineActionList;
        // 判断航线高度模式是否为aboveGroundLevel 需要去获取当前地面海拔和目前海拔的高度差用来回显航线在地图上的高度
        waylineListref.value.waylinePoints.positions = kmzData.positions;
        waylineListref.value.take_off_point = []; //先清空起飞点
        if (kmzData.take_off_point.length > 0) {
          waylineListref.value.take_off_point[0] = [kmzData.take_off_point[1], kmzData.take_off_point[0], kmzData.take_off_point[2]];
          console.log('拿到的起飞点', waylineListref.value.take_off_point[0]);
          // 开始计算和渲染起飞点
          waylineListref.value.offPoint(waylineListref.value.take_off_point[0]);
          waylineListref.value.offPointEarthandearthline(waylineListref.value.take_off_point[0]);
          waylineListref.value.floorOffPoint(waylineListref.value.take_off_point[0]);
          if (waylineListref.value.waylinePoints.positions.length == 0) {
            waylineListref.value.take_off_point.push([
              waylineListref.value.take_off_point[0][0],
              waylineListref.value.take_off_point[0][1],
              waylineListref.value.take_off_point[0][2] + waylineListref.value.waylineSetting.takeOffSecurityHeight,
            ]);
          } else {
            if (
              waylineListref.value.waylineSetting.takeOffSecurityHeight + waylineListref.value.take_off_point[0][2] <=
              waylineListref.value.waylinePoints.positions[0][2]
            ) {
              // 如果小于 则需要判断是垂直爬升还是倾斜爬升
              if (waylineListref.value.waylineSetting.flyToWaylineMode == 'safely') {
                // 垂直爬升会垂直爬升到航线高度，在飞向第一个航点
                // waylineListref.value.take_off_point.splice(1,1);
                waylineListref.value.take_off_point[1] = [
                  waylineListref.value.take_off_point[0][0],
                  waylineListref.value.take_off_point[0][1],
                  waylineListref.value.waylinePoints.positions[0][2],
                ];
                waylineListref.value.take_off_point[2] = [
                  waylineListref.value.waylinePoints.positions[0][0],
                  waylineListref.value.waylinePoints.positions[0][1],
                  waylineListref.value.waylinePoints.positions[0][2],
                ];
                // waylineListref.value.offPointLine( waylineListref.value.take_off_point);
              } else {
                // 倾斜爬升会升至安全起飞高度，直接往第一个航点的高度斜着爬升
                waylineListref.value.take_off_point[1] = [
                  waylineListref.value.take_off_point[0][0],
                  waylineListref.value.take_off_point[0][1],
                  waylineListref.value.take_off_point[0][2] + waylineListref.value.waylineSetting.takeOffSecurityHeight,
                ];
                waylineListref.value.take_off_point[2] = [
                  waylineListref.value.waylinePoints.positions[0][0],
                  waylineListref.value.waylinePoints.positions[0][1],
                  waylineListref.value.waylinePoints.positions[0][2],
                ];
              }
            } else {
              waylineListref.value.take_off_point.push([
                waylineListref.value.take_off_point[0][0],
                waylineListref.value.take_off_point[0][1],
                waylineListref.value.take_off_point[0][2] + waylineListref.value.waylineSetting.takeOffSecurityHeight,
              ]);
              waylineListref.value.take_off_point.push([
                waylineListref.value.waylinePoints.positions[0][0],
                waylineListref.value.waylinePoints.positions[0][1],
                waylineListref.value.take_off_point[0][2] + waylineListref.value.waylineSetting.takeOffSecurityHeight,
              ]);
              waylineListref.value.take_off_point.push([
                waylineListref.value.waylinePoints.positions[0][0],
                waylineListref.value.waylinePoints.positions[0][1],
                waylineListref.value.waylinePoints.positions[0][2],
              ]);
            }
          }
          waylineListref.value.offPointLine(waylineListref.value.take_off_point);
        }

        waylineListref.value.drawLine(waylineListref.value.waylinePoints.positions);
        waylineListref.value.earthandearthline(waylineListref.value.waylinePoints.positions);
        // waylineListref.value.earthandearthline(waylineListref.value.waylinePoints.positions);
        waylineListref.value.waylinePoints.positions.forEach((item, index) => {
          let mapOpsition = item.map((obj) => [item[0], item[1], item[2]]);
          // 航点
          waylineListref.value.airlinePoint(mapOpsition[0], index + 1);
          let mapOpsitionB = item.map((obj) => [item[0], item[1]]);
          // 地上的小白点
          waylineListref.value.floorPoint(mapOpsitionB[0]);
        });
        // waylineListref.value.addFrustumPrimitive(waylineListref.value.waylinePoints.positions[0])
        if (waylineListref.value.waylinePoints.positions.length > 0) {
          mapUtil.map.setCameraView({
            lng: waylineListref.value.waylinePoints.positions[0][0],
            lat: waylineListref.value.waylinePoints.positions[0][1],
          });
        }
      }

      // if (waylineListref.value.waylineSetting.altitudeModel == 'aboveGroundLevel' && Array.isArray(kmzData.positions)) {
      //   let pointsListPromises = kmzData.positions.map(item => {
      //     return mars3d.PointUtil.getSurfaceHeight(mapUtil.map.scene, item).then(result => {
      //       return [item[0], item[1], Number(result.height.toFixed(1)) + item[2]];
      //     });
      //   });

      //   Promise.all(pointsListPromises).then(pointsList => {
      //     // console.log('1212121212121', pointsList);

      //     waylineListref.value.waylinePoints.positions = pointsList;
      //     waylineListref.value.drawLine(waylineListref.value.waylinePoints.positions);
      //     waylineListref.value.earthandearthline(waylineListref.value.waylinePoints.positions);
      //     // waylineListref.value.earthandearthline(waylineListref.value.waylinePoints.positions);
      //     waylineListref.value.waylinePoints.positions.forEach((item,index)=>{
      //       let mapOpsition = item.map((obj) => [item[0], item[1], item[2]]);
      //       // 航点
      //       waylineListref.value.airlinePoint(mapOpsition[0],index+1);
      //       let mapOpsitionB = item.map((obj) => [item[0], item[1]]);
      //       // 地上的小白点
      //       waylineListref.value.floorPoint(mapOpsitionB[0]);
      //     });
      //     if (waylineListref.value.waylinePoints.positions.length > 0) {
      //       mapUtil.map.setCameraView({ lng: waylineListref.value.waylinePoints.positions[0][0], lat: waylineListref.value.waylinePoints.positions[0][1] });

      //     }
      //   })
      //   .catch(error => {
      //     console.error('Error fetching surface heights:', error);
      //   });
      // } else if  (waylineListref.value.waylineSetting.altitudeModel == 'EGM96') {
      //   waylineListref.value.waylinePoints.positions = kmzData.positions;
      //   waylineListref.value.drawLine(waylineListref.value.waylinePoints.positions);
      //   waylineListref.value.earthandearthline(waylineListref.value.waylinePoints.positions);
      //   // waylineListref.value.earthandearthline(waylineListref.value.waylinePoints.positions);
      //   waylineListref.value.waylinePoints.positions.forEach((item,index)=>{
      //     let mapOpsition = item.map((obj) => [item[0], item[1], item[2]]);
      //     // 航点
      //     waylineListref.value.airlinePoint(mapOpsition[0],index+1);
      //     let mapOpsitionB = item.map((obj) => [item[0], item[1]]);
      //     // 地上的小白点
      //     waylineListref.value.floorPoint(mapOpsitionB[0]);
      //   });
      //   if (waylineListref.value.waylinePoints.positions.length > 0) {
      //     mapUtil.map.setCameraView({ lng: waylineListref.value.waylinePoints.positions[0][0], lat: waylineListref.value.waylinePoints.positions[0][1] });

      //   }
      // } else {
      //   console.log('相对起飞点高度');
      //   waylineListref.value.waylinePoints.positions = kmzData.positions;
      //   waylineListref.value.drawLine(waylineListref.value.waylinePoints.positions);
      //   waylineListref.value.earthandearthline(waylineListref.value.waylinePoints.positions);
      //   // waylineListref.value.earthandearthline(waylineListref.value.waylinePoints.positions);
      //   waylineListref.value.waylinePoints.positions.forEach((item,index)=>{
      //     let mapOpsition = item.map((obj) => [item[0], item[1], item[2]]);
      //     // 航点
      //     waylineListref.value.airlinePoint(mapOpsition[0],index+1);
      //     let mapOpsitionB = item.map((obj) => [item[0], item[1]]);
      //     // 地上的小白点
      //     waylineListref.value.floorPoint(mapOpsitionB[0]);
      //   });
      //   if (waylineListref.value.waylinePoints.positions.length > 0) {
      //     mapUtil.map.setCameraView({ lng: waylineListref.value.waylinePoints.positions[0][0], lat: waylineListref.value.waylinePoints.positions[0][1] });

      //   }
      // }
    });
  },
  // 地图起飞点
  takeOffPoint: function () {
    waylineListref.value.isShowText = false;
    if (pointEditData != null) {
      mapUtil.map.removeThing(pointEditData, true);
      pointEditData = null;
    }
    mapUtil.maplayer.startDraw({
      type: 'billboard',
      style: {
        clampToGround: true,
        image: svg('takeOffPoint0'),
        horizontalOrigin: mars3d.Cesium.HorizontalOrigin.CENTER,
        verticalOrigin: mars3d.Cesium.VerticalOrigin.BOTTOM,
      },
      success: function (graphic) {
        waylineListref.value.isShowText = true;
        if (waylineListref.value.frustumPrimitive.length > 0) {
          mapUtil.maplayer.removeGraphic(waylineListref.value.frustumPrimitive[0]);
        }
        mapUtil.maplayer.removeGraphic(waylineListref.value.take_off_point_List[0]);
        mapUtil.maplayer.removeGraphic(waylineListref.value.offPointePolyline[0]);
        mapUtil.maplayer.removeGraphic(waylineListref.value.offPointEarthandearthlineList[0]);
        mapUtil.maplayer.removeGraphic(waylineListref.value.floorOffPointList[0]);
        waylineListref.value.take_off_point = [];
        const graphicObj = JSON.parse(JSON.stringify(graphic));
        console.log('起飞点位置', graphicObj.position);

        graphic.remove();
        waylineListref.value.take_off_point[0] = graphicObj.position;
        waylineListref.value.take_off_point.push([
          graphicObj.position[0],
          graphicObj.position[1],
          graphicObj.position[2] + waylineListref.value.waylineSetting.takeOffSecurityHeight,
        ]);
        waylineListref.value.offPoint(waylineListref.value.take_off_point[0]);
        waylineListref.value.offPointEarthandearthline(waylineListref.value.take_off_point[0]);
        waylineListref.value.floorOffPoint(waylineListref.value.take_off_point[0]);
        // 对这块进行方法封装，用于删除航点使用
        // 当没有起飞点时，起飞点连线逻辑
        if (waylineListref.value.waylinePoints.positions.length == 0) {
          waylineListref.value.offPointLine(waylineListref.value.take_off_point);
        } else {
          // 当有航点时，重设起飞点连线逻辑
          if (pointEditData != null) {
            mapUtil.map.removeThing(pointEditData, true);
            pointEditData = null;
          }
          // 拿到第一个航点，进行起飞点线的绘制
          // 先判断安全起飞高度是否大于航线高度 如果大于，则会先垂直爬升到安全高度，以安全起飞的高度飞行到第一个航点位置，再下降到第一个航点高度
          if (waylineListref.value.waylineSetting.altitudeModel == 'relativeToStartPoint') {
            if (waylineListref.value.waylineSetting.takeOffSecurityHeight > waylineListref.value.waylineSetting.globalHeight) {
              // 如果安全起飞高度大于了航线高度，则会先垂直爬升到安全高度，水平飞行到第一个航点位置，再下降到第一个航点高度
              waylineListref.value.take_off_point.splice(1, 1);
              waylineListref.value.take_off_point.push([
                waylineListref.value.take_off_point[0][0],
                waylineListref.value.take_off_point[0][1],
                waylineListref.value.take_off_point[0][2] + waylineListref.value.waylineSetting.takeOffSecurityHeight,
              ]);
              waylineListref.value.take_off_point.push([
                waylineListref.value.waylinePoints.positions[0][0],
                waylineListref.value.waylinePoints.positions[0][1],
                waylineListref.value.take_off_point[0][2] + waylineListref.value.waylineSetting.takeOffSecurityHeight,
              ]);
              waylineListref.value.take_off_point.push([
                waylineListref.value.waylinePoints.positions[0][0],
                waylineListref.value.waylinePoints.positions[0][1],
                waylineListref.value.take_off_point[0][2] + waylineListref.value.waylineSetting.globalHeight,
              ]);
              waylineListref.value.offPointLine(waylineListref.value.take_off_point);
              waylineListref.value.waylinePoints.positions.forEach((item, index) => {
                item[2] = waylineListref.value.take_off_point[0][2] + waylineListref.value.waylineSetting.globalHeight;
                waylineListref.value.pointsList[index].setCallbackPosition([item[0], item[1], item[2]]);
              });
              waylineListref.value.drawLine(waylineListref.value.waylinePoints.positions);
              waylineListref.value.earthandearthline(waylineListref.value.waylinePoints.positions);
            } else {
              // 如果小于 则需要判断是垂直爬升还是倾斜爬升
              if (waylineListref.value.waylineSetting.flyToWaylineMode == 'safely') {
                // 垂直爬升会垂直爬升到航线高度，在飞向第一个航点
                waylineListref.value.take_off_point.splice(1, 1);
                waylineListref.value.take_off_point.push([
                  waylineListref.value.take_off_point[0][0],
                  waylineListref.value.take_off_point[0][1],
                  waylineListref.value.take_off_point[0][2] + waylineListref.value.waylineSetting.globalHeight,
                ]);
                waylineListref.value.take_off_point.push([
                  waylineListref.value.waylinePoints.positions[0][0],
                  waylineListref.value.waylinePoints.positions[0][1],
                  waylineListref.value.take_off_point[0][2] + waylineListref.value.waylineSetting.globalHeight,
                ]);
                waylineListref.value.offPointLine(waylineListref.value.take_off_point);
                waylineListref.value.waylinePoints.positions.forEach((item, index) => {
                  item[2] = waylineListref.value.take_off_point[0][2] + waylineListref.value.waylineSetting.globalHeight;
                  waylineListref.value.pointsList[index].setCallbackPosition([item[0], item[1], item[2]]);
                });
                waylineListref.value.drawLine(waylineListref.value.waylinePoints.positions);
                waylineListref.value.earthandearthline(waylineListref.value.waylinePoints.positions);
              } else {
                // 倾斜爬升会升至安全起飞高度，直接往第一个航点的高度斜着爬升
                waylineListref.value.take_off_point.push([
                  waylineListref.value.waylinePoints.positions[0][0],
                  waylineListref.value.waylinePoints.positions[0][1],
                  waylineListref.value.take_off_point[0][2] + waylineListref.value.waylineSetting.globalHeight,
                ]);
                waylineListref.value.offPointLine(waylineListref.value.take_off_point);
                waylineListref.value.waylinePoints.positions.forEach((item, index) => {
                  item[2] = waylineListref.value.take_off_point[0][2] + waylineListref.value.waylineSetting.globalHeight;
                  waylineListref.value.pointsList[index].setCallbackPosition([item[0], item[1], item[2]]);
                });
                waylineListref.value.drawLine(waylineListref.value.waylinePoints.positions);
                waylineListref.value.earthandearthline(waylineListref.value.waylinePoints.positions);
              }
            }
          }
          if (waylineListref.value.waylineSetting.altitudeModel == 'EGM96') {
            if (
              waylineListref.value.waylineSetting.takeOffSecurityHeight + waylineListref.value.take_off_point[0][2] >
              waylineListref.value.waylinePoints.positions[0][2]
            ) {
              // 如果安全起飞高度大于了航线高度，则会先垂直爬升到安全高度，水平飞行到第一个航点位置，再下降到第一个航点高度
              waylineListref.value.take_off_point.splice(1, 1);
              waylineListref.value.take_off_point.push([
                waylineListref.value.take_off_point[0][0],
                waylineListref.value.take_off_point[0][1],
                waylineListref.value.take_off_point[0][2] + waylineListref.value.waylineSetting.takeOffSecurityHeight,
              ]);
              waylineListref.value.take_off_point.push([
                waylineListref.value.waylinePoints.positions[0][0],
                waylineListref.value.waylinePoints.positions[0][1],
                waylineListref.value.take_off_point[0][2] + waylineListref.value.waylineSetting.takeOffSecurityHeight,
              ]);
              waylineListref.value.take_off_point.push([
                waylineListref.value.waylinePoints.positions[0][0],
                waylineListref.value.waylinePoints.positions[0][1],
                waylineListref.value.waylinePoints.positions[0][2],
              ]);
              waylineListref.value.offPointLine(waylineListref.value.take_off_point);
            } else {
              // 如果小于 则需要判断是垂直爬升还是倾斜爬升
              if (waylineListref.value.waylineSetting.flyToWaylineMode == 'safely') {
                // 垂直爬升会垂直爬升到航线高度，在飞向第一个航点
                waylineListref.value.take_off_point.splice(1, 1);
                waylineListref.value.take_off_point.push([
                  waylineListref.value.take_off_point[0][0],
                  waylineListref.value.take_off_point[0][1],
                  waylineListref.value.waylinePoints.positions[0][2],
                ]);
                waylineListref.value.take_off_point.push([
                  waylineListref.value.waylinePoints.positions[0][0],
                  waylineListref.value.waylinePoints.positions[0][1],
                  waylineListref.value.waylinePoints.positions[0][2],
                ]);
                waylineListref.value.offPointLine(waylineListref.value.take_off_point);
              } else {
                // 倾斜爬升会升至安全起飞高度，直接往第一个航点的高度斜着爬升
                waylineListref.value.take_off_point.push([
                  waylineListref.value.waylinePoints.positions[0][0],
                  waylineListref.value.waylinePoints.positions[0][1],
                  waylineListref.value.waylinePoints.positions[0][2],
                ]);
                waylineListref.value.offPointLine(waylineListref.value.take_off_point);
              }
            }
          }
          // 相对地形
          if (waylineListref.value.waylineSetting.altitudeModel == 'aboveGroundLevel') {
            if (waylineListref.value.waylineSetting.takeOffSecurityHeight > waylineListref.value.waylineSetting.globalHeight) {
              // 如果安全起飞高度大于了航线高度，则会先垂直爬升到安全高度，水平飞行到第一个航点位置，再下降到第一个航点高度
              waylineListref.value.take_off_point.splice(1, 1);
              waylineListref.value.take_off_point.push([
                waylineListref.value.take_off_point[0][0],
                waylineListref.value.take_off_point[0][1],
                waylineListref.value.take_off_point[0][2] + waylineListref.value.waylineSetting.takeOffSecurityHeight,
              ]);
              waylineListref.value.take_off_point.push([
                waylineListref.value.waylinePoints.positions[0][0],
                waylineListref.value.waylinePoints.positions[0][1],
                waylineListref.value.take_off_point[0][2] + waylineListref.value.waylineSetting.takeOffSecurityHeight,
              ]);
              waylineListref.value.take_off_point.push([
                waylineListref.value.waylinePoints.positions[0][0],
                waylineListref.value.waylinePoints.positions[0][1],
                waylineListref.value.waylinePoints.positions[0][2],
              ]);
              waylineListref.value.offPointLine(waylineListref.value.take_off_point);
            } else {
              // 如果小于 则需要判断是垂直爬升还是倾斜爬升
              if (waylineListref.value.waylineSetting.flyToWaylineMode == 'safely') {
                // 垂直爬升会垂直爬升到航线高度，在飞向第一个航点
                waylineListref.value.take_off_point.splice(1, 1);
                waylineListref.value.take_off_point.push([
                  waylineListref.value.take_off_point[0][0],
                  waylineListref.value.take_off_point[0][1],
                  waylineListref.value.waylinePoints.positions[0][2],
                ]);
                waylineListref.value.take_off_point.push([
                  waylineListref.value.waylinePoints.positions[0][0],
                  waylineListref.value.waylinePoints.positions[0][1],
                  waylineListref.value.waylinePoints.positions[0][2],
                ]);
                waylineListref.value.offPointLine(waylineListref.value.take_off_point);
              } else {
                // 倾斜爬升会升至安全起飞高度，直接往第一个航点的高度斜着爬升
                waylineListref.value.take_off_point.push([
                  waylineListref.value.waylinePoints.positions[0][0],
                  waylineListref.value.waylinePoints.positions[0][1],
                  waylineListref.value.waylinePoints.positions[0][2],
                ]);
                waylineListref.value.offPointLine(waylineListref.value.take_off_point);
              }
            }
          }
        }
      },
    });
  },
  // 起飞点
  offPoint: function (points) {
    const offPointBillboard = new mars3d.graphic.BillboardEntity({
      position: points,
      style: {
        // clampToGround: true,
        image: svg('起飞点'),
        scale: 0.6,
        horizontalOrigin: mars3d.Cesium.HorizontalOrigin.CENTER,
        verticalOrigin: mars3d.Cesium.VerticalOrigin.BOTTOM,
        // heightReference: mars3d.Cesium.HeightReference.CLAMP_TO_GROUND,
        label: {
          // text: `${item[2]}m`,
          color: '#ffffff',
          font_size: 12,
          background: true,
          backgroundColor: '#000000',
          backgroundOpacity: 0.6,
          pixelOffsetY: 10,
        },
      },
    });
    waylineListref.value.take_off_point_List[0] = offPointBillboard;
    mapUtil.maplayer.addGraphic(offPointBillboard);
    waylineListref.value.rightClickMenuOffPoint(offPointBillboard);
  },
  offPointEarthandearthline: async function (points) {
    let res = await mars3d.PointUtil.getSurfaceHeight(mapUtil.map.scene, [points[0], points[1]], { has3dtiles: false });
    const polylineXPoints = [
      [points[0], points[1], res.height],
      [points[0], points[1], points[2]],
    ];
    const polylineX = new mars3d.graphic.PolylineEntity({
      positions: polylineXPoints,
      style: {
        // clampToGround: true,
        width: 1,
        color: '#FEFEFE',
        horizontalOrigin: mars3d.Cesium.HorizontalOrigin.CENTER,
        verticalOrigin: mars3d.Cesium.VerticalOrigin.BOTTOM,
      },
    });
    waylineListref.value.offPointEarthandearthlineList[0] = polylineX;
    mapUtil.maplayer.addGraphic(polylineX);
  },
  floorOffPoint: function (points) {
    const waylineBillboardB = new mars3d.graphic.BillboardEntity({
      position: points,
      style: {
        clampToGround: true,
        image: svg('球'),
        scale: 1.2,
        horizontalOrigin: mars3d.Cesium.HorizontalOrigin.CENTER,
        verticalOrigin: mars3d.Cesium.VerticalOrigin.BOTTOM,
      },
    });
    waylineListref.value.floorOffPointList[0] = waylineBillboardB;
    mapUtil.maplayer.addGraphic(waylineBillboardB);
  },
  rightClickMenuOffPoint: function (pointMaker) {
    pointMaker
      .off()
      .on(mars3d.EventType.dblClick, function (e) {
        if (pointEditData != null) {
          mapUtil.map.removeThing(pointEditData, true);
          pointEditData = null;
        }

        // mapUtil.maplayer.startEditing(pointMaker);
        pointEditData = new mars3d.thing.MatrixMove({
          position: pointMaker.position,
        });
        // console.log('1111111111',pointEditData);

        mapUtil.map.addThing(pointEditData);

        pointEditData.on(mars3d.EventType.change, (event) => {
          pointMaker.position = event.position;
        });
        pointEditData.on(mars3d.EventType.stop, async (event) => {
          waylineListref.value.addFrustumPrimitive(waylineListref.value.waylinePoints.positions[waylineListref.value.waypointIndex]); // 更新四锥体位置
        });
      })
      .on(mars3d.EventType.updatePosition, async function (e) {
        waylineListref.value.take_off_point[0] = [e.target.point.lng, e.target.point.lat, e.target.point.alt];
        if (waylineListref.value.waylinePoints.positions.length == 0) {
          waylineListref.value.take_off_point[1] = [
            e.target.point.lng,
            e.target.point.lat,
            e.target.point.alt + waylineListref.value.waylineSetting.takeOffSecurityHeight,
          ];
        } else {
          // 只保留起飞点，其余的删掉，防止渲染重复线条问题
          waylineListref.value.take_off_point = waylineListref.value.take_off_point.slice(0, 1);
          if (waylineListref.value.waylineSetting.altitudeModel == 'relativeToStartPoint') {
            if (waylineListref.value.waylineSetting.takeOffSecurityHeight > waylineListref.value.waylineSetting.globalHeight) {
              // 如果安全起飞高度大于了航线高度，则会先垂直爬升到安全高度，水平飞行到第一个航点位置，再下降到第一个航点高度
              // waylineListref.value.take_off_point.splice(1,1);
              waylineListref.value.take_off_point[1] = [
                waylineListref.value.take_off_point[0][0],
                waylineListref.value.take_off_point[0][1],
                waylineListref.value.take_off_point[0][2] + waylineListref.value.waylineSetting.takeOffSecurityHeight,
              ];
              waylineListref.value.take_off_point[2] = [
                waylineListref.value.waylinePoints.positions[0][0],
                waylineListref.value.waylinePoints.positions[0][1],
                waylineListref.value.take_off_point[0][2] + waylineListref.value.waylineSetting.takeOffSecurityHeight,
              ];
              waylineListref.value.take_off_point[3] = [
                waylineListref.value.waylinePoints.positions[0][0],
                waylineListref.value.waylinePoints.positions[0][1],
                waylineListref.value.take_off_point[0][2] + waylineListref.value.waylineSetting.globalHeight,
              ];
              // waylineListref.value.offPointLine( waylineListref.value.take_off_point);
              waylineListref.value.waylinePoints.positions.forEach((item, index) => {
                item[2] = waylineListref.value.take_off_point[0][2] + waylineListref.value.waylineSetting.globalHeight;
                waylineListref.value.pointsList[index].setCallbackPosition([item[0], item[1], item[2]]);
              });
              waylineListref.value.drawLine(waylineListref.value.waylinePoints.positions);
              waylineListref.value.earthandearthline(waylineListref.value.waylinePoints.positions);
            } else {
              // 如果小于 则需要判断是垂直爬升还是倾斜爬升
              if (waylineListref.value.waylineSetting.flyToWaylineMode == 'safely') {
                // 垂直爬升会垂直爬升到航线高度，在飞向第一个航点
                // waylineListref.value.take_off_point.splice(1,1);
                waylineListref.value.take_off_point[1] = [
                  waylineListref.value.take_off_point[0][0],
                  waylineListref.value.take_off_point[0][1],
                  waylineListref.value.take_off_point[0][2] + waylineListref.value.waylineSetting.globalHeight,
                ];
                waylineListref.value.take_off_point[2] = [
                  waylineListref.value.waylinePoints.positions[0][0],
                  waylineListref.value.waylinePoints.positions[0][1],
                  waylineListref.value.take_off_point[0][2] + waylineListref.value.waylineSetting.globalHeight,
                ];
                // waylineListref.value.offPointLine( waylineListref.value.take_off_point);
                waylineListref.value.waylinePoints.positions.forEach((item, index) => {
                  item[2] = waylineListref.value.take_off_point[0][2] + waylineListref.value.waylineSetting.globalHeight;
                  waylineListref.value.pointsList[index].setCallbackPosition([item[0], item[1], item[2]]);
                });
                waylineListref.value.drawLine(waylineListref.value.waylinePoints.positions);
                waylineListref.value.earthandearthline(waylineListref.value.waylinePoints.positions);
              } else {
                // 倾斜爬升会升至安全起飞高度，直接往第一个航点的高度斜着爬升
                waylineListref.value.take_off_point[1] = [
                  waylineListref.value.take_off_point[0][0],
                  waylineListref.value.take_off_point[0][1],
                  waylineListref.value.take_off_point[0][2] + waylineListref.value.waylineSetting.takeOffSecurityHeight,
                ];
                waylineListref.value.take_off_point[2] = [
                  waylineListref.value.waylinePoints.positions[0][0],
                  waylineListref.value.waylinePoints.positions[0][1],
                  waylineListref.value.take_off_point[0][2] + waylineListref.value.waylineSetting.globalHeight,
                ];
                // waylineListref.value.offPointLine( waylineListref.value.take_off_point);
                waylineListref.value.waylinePoints.positions.forEach((item, index) => {
                  item[2] = waylineListref.value.take_off_point[0][2] + waylineListref.value.waylineSetting.globalHeight;
                  waylineListref.value.pointsList[index].setCallbackPosition([item[0], item[1], item[2]]);
                });
                waylineListref.value.drawLine(waylineListref.value.waylinePoints.positions);
                waylineListref.value.earthandearthline(waylineListref.value.waylinePoints.positions);
              }
            }
          }
          if (waylineListref.value.waylineSetting.altitudeModel == 'EGM96') {
            if (
              waylineListref.value.waylineSetting.takeOffSecurityHeight + waylineListref.value.take_off_point[0][2] >
              waylineListref.value.waylinePoints.positions[0][2]
            ) {
              // 如果安全起飞高度大于了航线高度，则会先垂直爬升到安全高度，水平飞行到第一个航点位置，再下降到第一个航点高度
              // waylineListref.value.take_off_point.splice(1,1);
              waylineListref.value.take_off_point[1] = [
                waylineListref.value.take_off_point[0][0],
                waylineListref.value.take_off_point[0][1],
                waylineListref.value.take_off_point[0][2] + waylineListref.value.waylineSetting.takeOffSecurityHeight,
              ];
              waylineListref.value.take_off_point[2] = [
                waylineListref.value.waylinePoints.positions[0][0],
                waylineListref.value.waylinePoints.positions[0][1],
                waylineListref.value.take_off_point[0][2] + waylineListref.value.waylineSetting.takeOffSecurityHeight,
              ];
              waylineListref.value.take_off_point[3] = [
                waylineListref.value.waylinePoints.positions[0][0],
                waylineListref.value.waylinePoints.positions[0][1],
                waylineListref.value.waylinePoints.positions[0][2],
              ];
              // waylineListref.value.offPointLine( waylineListref.value.take_off_point);
              // waylineListref.value.waylinePoints.positions.forEach((item, index)=>{
              //   item[2] = waylineListref.value.take_off_point[0][2] + waylineListref.value.waylineSetting.globalHeight;
              //   waylineListref.value.pointsList[index].setCallbackPosition([item[0], item[1], item[2]]);
              // })
              // waylineListref.value.drawLine(waylineListref.value.waylinePoints.positions);
              // waylineListref.value.earthandearthline(waylineListref.value.waylinePoints.positions);
            } else {
              // 如果小于 则需要判断是垂直爬升还是倾斜爬升
              if (waylineListref.value.waylineSetting.flyToWaylineMode == 'safely') {
                // 垂直爬升会垂直爬升到航线高度，在飞向第一个航点
                // waylineListref.value.take_off_point.splice(1,1);
                waylineListref.value.take_off_point[1] = [
                  waylineListref.value.take_off_point[0][0],
                  waylineListref.value.take_off_point[0][1],
                  waylineListref.value.waylinePoints.positions[0][2],
                ];
                waylineListref.value.take_off_point[2] = [
                  waylineListref.value.waylinePoints.positions[0][0],
                  waylineListref.value.waylinePoints.positions[0][1],
                  waylineListref.value.waylinePoints.positions[0][2],
                ];
                // waylineListref.value.offPointLine( waylineListref.value.take_off_point);
                // waylineListref.value.waylinePoints.positions.forEach((item, index)=>{
                //   item[2] = waylineListref.value.take_off_point[0][2] + waylineListref.value.waylineSetting.globalHeight;
                //   waylineListref.value.pointsList[index].setCallbackPosition([item[0], item[1], item[2]]);
                // })
                // waylineListref.value.drawLine(waylineListref.value.waylinePoints.positions);
                // waylineListref.value.earthandearthline(waylineListref.value.waylinePoints.positions);
              } else {
                // 倾斜爬升会升至安全起飞高度，直接往第一个航点的高度斜着爬升
                waylineListref.value.take_off_point[1] = [
                  waylineListref.value.take_off_point[0][0],
                  waylineListref.value.take_off_point[0][1],
                  waylineListref.value.take_off_point[0][2] + waylineListref.value.waylineSetting.takeOffSecurityHeight,
                ];
                waylineListref.value.take_off_point[2] = [
                  waylineListref.value.waylinePoints.positions[0][0],
                  waylineListref.value.waylinePoints.positions[0][1],
                  waylineListref.value.waylinePoints.positions[0][2],
                ];
                // waylineListref.value.offPointLine( waylineListref.value.take_off_point);
                // waylineListref.value.waylinePoints.positions.forEach((item, index)=>{
                //   item[2] = waylineListref.value.take_off_point[0][2] + waylineListref.value.waylineSetting.globalHeight;
                //   waylineListref.value.pointsList[index].setCallbackPosition([item[0], item[1], item[2]]);
                // })
                // waylineListref.value.drawLine(waylineListref.value.waylinePoints.positions);
                // waylineListref.value.earthandearthline(waylineListref.value.waylinePoints.positions);
              }
            }
          }
          if (waylineListref.value.waylineSetting.altitudeModel == 'aboveGroundLevel') {
            if (
              waylineListref.value.waylineSetting.takeOffSecurityHeight + waylineListref.value.take_off_point[0][2] >
              waylineListref.value.waylinePoints.positions[0][2]
            ) {
              // 如果安全起飞高度大于了航线高度，则会先垂直爬升到安全高度，水平飞行到第一个航点位置，再下降到第一个航点高度
              waylineListref.value.take_off_point[1] = [
                waylineListref.value.take_off_point[0][0],
                waylineListref.value.take_off_point[0][1],
                waylineListref.value.take_off_point[0][2] + waylineListref.value.waylineSetting.takeOffSecurityHeight,
              ];
              waylineListref.value.take_off_point[2] = [
                waylineListref.value.waylinePoints.positions[0][0],
                waylineListref.value.waylinePoints.positions[0][1],
                waylineListref.value.take_off_point[0][2] + waylineListref.value.waylineSetting.takeOffSecurityHeight,
              ];
              waylineListref.value.take_off_point[3] = [
                waylineListref.value.waylinePoints.positions[0][0],
                waylineListref.value.waylinePoints.positions[0][1],
                waylineListref.value.waylinePoints.positions[0][2],
              ];
            } else {
              // 如果小于 则需要判断是垂直爬升还是倾斜爬升
              if (waylineListref.value.waylineSetting.flyToWaylineMode == 'safely') {
                // 垂直爬升会垂直爬升到航线高度，在飞向第一个航点
                // waylineListref.value.take_off_point.splice(1,1);
                waylineListref.value.take_off_point[1] = [
                  waylineListref.value.take_off_point[0][0],
                  waylineListref.value.take_off_point[0][1],
                  waylineListref.value.waylinePoints.positions[0][2],
                ];
                waylineListref.value.take_off_point[2] = [
                  waylineListref.value.waylinePoints.positions[0][0],
                  waylineListref.value.waylinePoints.positions[0][1],
                  waylineListref.value.waylinePoints.positions[0][2],
                ];
              } else {
                // 倾斜爬升会升至安全起飞高度，直接往第一个航点的高度斜着爬升
                waylineListref.value.take_off_point[1] = [
                  waylineListref.value.take_off_point[0][0],
                  waylineListref.value.take_off_point[0][1],
                  waylineListref.value.take_off_point[0][2] + waylineListref.value.waylineSetting.takeOffSecurityHeight,
                ];
                waylineListref.value.take_off_point[2] = [
                  waylineListref.value.waylinePoints.positions[0][0],
                  waylineListref.value.waylinePoints.positions[0][1],
                  waylineListref.value.waylinePoints.positions[0][2],
                ];
              }
            }
          }
        }

        waylineListref.value.offPointLine(waylineListref.value.take_off_point);
        let res = await mars3d.PointUtil.getSurfaceHeight(mapUtil.map.scene, [e.target.point.lng, e.target.point.lat], { has3dtiles: false });
        waylineListref.value.offPointEarthandearthlineList[0]?.setCallbackPositions([
          [e.target.point.lng, e.target.point.lat, res.height],
          [e.target.point.lng, e.target.point.lat, e.target.point.alt],
        ]);
        waylineListref.value.floorOffPointList[0]?.setCallbackPosition([e.target.point.lng, e.target.point.lat, 0]);
        // pointMaker.position = e.target.position;
      })
      .on(mars3d.EventType.leftUp, function (e) {
        // mapUtil.maplayer.stopEditing();
      })
      .on(mars3d.EventType.click, function (e) {
        mapUtil.map.removeThing(pointEditData, true);

        pointEditData = null;
      })
      .on(mars3d.EventType.contextMenuOpen, function (e) {
        mapUtil.map.removeThing(pointEditData, true);
        pointEditData = null;
      });
  },
  // 起飞点连线
  offPointLine: function (points) {
    if (points.length > 1) {
      const offPointPolylineEntity = new mars3d.graphic.PolylineEntity({
        positions: points,
        style: {
          width: 5,
          color: '#00D590',
          horizontalOrigin: mars3d.Cesium.HorizontalOrigin.CENTER,
          verticalOrigin: mars3d.Cesium.VerticalOrigin.BOTTOM,
        },
      });
      mapUtil.maplayer.removeGraphic(waylineListref.value.offPointePolyline[0]);
      waylineListref.value.offPointePolyline = [];
      waylineListref.value.offPointePolyline[0] = offPointPolylineEntity;
      mapUtil.maplayer.addGraphic(offPointPolylineEntity);
    }
  },

  // 打点逻辑
  startDrawWayline: function (index) {
    // console.log(index);
    if (index > 99) {
      return message.warning('航点个数须控制100以内！');
    }

    mapUtil.maplayer.startDraw({
      type: 'billboard',
      style: {
        clampToGround: true,
        image: svg('' + index),
        horizontalOrigin: mars3d.Cesium.HorizontalOrigin.CENTER,
        verticalOrigin: mars3d.Cesium.VerticalOrigin.BOTTOM,
        // heightReference: mars3d.Cesium.HeightReference.CLAMP_TO_GROUND,
      },
      success: async function (graphic) {
        const graphicObj = JSON.parse(JSON.stringify(graphic));
        graphic.remove();
        console.log('打点的位置信息', graphicObj.position);
        waylineListref.value.useGlobalHeight.push(true);
        // mars3d.PointUtil.getSurfaceHeight(mapUtil.map.scene, graphicObj.position).then(res=>{
        //   console.log('转换的海拔高度', res);
        // })
        // 当前打点高度代表其点地面的海拔高度值，如果为相对起飞点高度，则直接加上起飞点的海拔高度值
        let mapOpsition = [
          [
            graphicObj.position[0],
            graphicObj.position[1],
            waylineListref.value.waylineSetting.altitudeModel == 'aboveGroundLevel'
              ? graphicObj.position[2] + waylineListref.value.waylineSetting.globalHeight
              : waylineListref.value.waylineSetting.altitudeModel == 'relativeToStartPoint'
              ? waylineListref.value.take_off_point[0][2] + waylineListref.value.waylineSetting.globalHeight
              : waylineListref.value.waylineSetting.globalHeight,
          ],
        ];
        // waylineListref.value.addFrustumPrimitive(mapOpsition[0]); // 四锥体
        // 相对起飞点
        if (waylineListref.value.waylineSetting.altitudeModel == 'relativeToStartPoint') {
          if (waylineListref.value.waylineSetting.takeOffSecurityHeight > waylineListref.value.waylineSetting.globalHeight && index == 1) {
            waylineListref.value.take_off_point.splice(1, 1);
            waylineListref.value.take_off_point.push([
              waylineListref.value.take_off_point[0][0],
              waylineListref.value.take_off_point[0][1],
              waylineListref.value.take_off_point[0][2] + waylineListref.value.waylineSetting.takeOffSecurityHeight,
            ]);
            waylineListref.value.take_off_point.push([
              graphicObj.position[0],
              graphicObj.position[1],
              waylineListref.value.take_off_point[0][2] + waylineListref.value.waylineSetting.takeOffSecurityHeight,
            ]);
            waylineListref.value.take_off_point.push([
              graphicObj.position[0],
              graphicObj.position[1],
              waylineListref.value.take_off_point[0][2] + waylineListref.value.waylineSetting.globalHeight,
            ]);
          } else {
            if (waylineListref.value.waylineSetting.flyToWaylineMode == 'safely' && index == 1) {
              waylineListref.value.take_off_point.splice(1, 1);
              waylineListref.value.take_off_point.push([
                waylineListref.value.take_off_point[0][0],
                waylineListref.value.take_off_point[0][1],
                waylineListref.value.take_off_point[0][2] + waylineListref.value.waylineSetting.globalHeight,
              ]);
              waylineListref.value.take_off_point.push([
                graphicObj.position[0],
                graphicObj.position[1],
                waylineListref.value.take_off_point[0][2] + waylineListref.value.waylineSetting.globalHeight,
              ]);
            } else if (waylineListref.value.waylineSetting.flyToWaylineMode == 'pointToPoint' && index == 1) {
              waylineListref.value.take_off_point.push([
                graphicObj.position[0],
                graphicObj.position[1],
                waylineListref.value.take_off_point[0][2] + waylineListref.value.waylineSetting.globalHeight,
              ]);
            }
          }
        }
        // 海拔 需要计算第一个航点的高度差 和安全起飞高度对比
        if (waylineListref.value.waylineSetting.altitudeModel == 'EGM96' && waylineListref.value.take_off_point.length > 0) {
          // const res = await mars3d.PointUtil.getSurfaceHeight(mapUtil.map.scene, [e.target.point.lng, e.target.point.lat]);
          console.log('高度差', waylineListref.value.waylineSetting.globalHeight - graphicObj.position[2]);

          if (
            waylineListref.value.waylineSetting.takeOffSecurityHeight + waylineListref.value.take_off_point[0][2] >
              waylineListref.value.waylineSetting.globalHeight &&
            index == 1
          ) {
            waylineListref.value.take_off_point.splice(1, 1);
            waylineListref.value.take_off_point.push([
              waylineListref.value.take_off_point[0][0],
              waylineListref.value.take_off_point[0][1],
              waylineListref.value.take_off_point[0][2] + waylineListref.value.waylineSetting.takeOffSecurityHeight,
            ]);
            waylineListref.value.take_off_point.push([
              graphicObj.position[0],
              graphicObj.position[1],
              waylineListref.value.take_off_point[0][2] + waylineListref.value.waylineSetting.takeOffSecurityHeight,
            ]);
            waylineListref.value.take_off_point.push([
              graphicObj.position[0],
              graphicObj.position[1],
              waylineListref.value.waylineSetting.globalHeight,
            ]);
          } else {
            if (waylineListref.value.waylineSetting.flyToWaylineMode == 'safely' && index == 1) {
              waylineListref.value.take_off_point.splice(1, 1);
              waylineListref.value.take_off_point.push([
                waylineListref.value.take_off_point[0][0],
                waylineListref.value.take_off_point[0][1],
                waylineListref.value.waylineSetting.globalHeight,
              ]);
              waylineListref.value.take_off_point.push([
                graphicObj.position[0],
                graphicObj.position[1],
                waylineListref.value.waylineSetting.globalHeight,
              ]);
            } else if (waylineListref.value.waylineSetting.flyToWaylineMode == 'pointToPoint' && index == 1) {
              waylineListref.value.take_off_point.push([
                graphicObj.position[0],
                graphicObj.position[1],
                waylineListref.value.waylineSetting.globalHeight,
              ]);
            }
          }
        }
        // 相对地形
        if (waylineListref.value.waylineSetting.altitudeModel == 'aboveGroundLevel' && waylineListref.value.take_off_point.length > 0) {
          // const res = await mars3d.PointUtil.getSurfaceHeight(mapUtil.map.scene, [e.target.point.lng, e.target.point.lat]);
          // console.log('高度差', waylineListref.value.waylineSetting.globalHeight - graphicObj.position[2]);

          if (waylineListref.value.waylineSetting.takeOffSecurityHeight > waylineListref.value.waylineSetting.globalHeight && index == 1) {
            waylineListref.value.take_off_point.splice(1, 1);
            waylineListref.value.take_off_point.push([
              waylineListref.value.take_off_point[0][0],
              waylineListref.value.take_off_point[0][1],
              waylineListref.value.take_off_point[0][2] + waylineListref.value.waylineSetting.takeOffSecurityHeight,
            ]);
            waylineListref.value.take_off_point.push([
              graphicObj.position[0],
              graphicObj.position[1],
              waylineListref.value.take_off_point[0][2] + waylineListref.value.waylineSetting.takeOffSecurityHeight,
            ]);
            waylineListref.value.take_off_point.push([graphicObj.position[0], graphicObj.position[1], mapOpsition[0][2]]);
          } else {
            if (waylineListref.value.waylineSetting.flyToWaylineMode == 'safely' && index == 1) {
              waylineListref.value.take_off_point.splice(1, 1);
              waylineListref.value.take_off_point.push([
                waylineListref.value.take_off_point[0][0],
                waylineListref.value.take_off_point[0][1],
                mapOpsition[0][2],
              ]);
              waylineListref.value.take_off_point.push([graphicObj.position[0], graphicObj.position[1], mapOpsition[0][2]]);
            } else if (waylineListref.value.waylineSetting.flyToWaylineMode == 'pointToPoint' && index == 1) {
              waylineListref.value.take_off_point.push([graphicObj.position[0], graphicObj.position[1], mapOpsition[0][2]]);
            }
          }
        }
        waylineListref.value.offPointLine(waylineListref.value.take_off_point);

        let mapOpsitionB = [[graphicObj.position[0], graphicObj.position[1]]];
        // 地上的小白点
        waylineListref.value.floorPoint(mapOpsitionB[0]);
        // 航点
        waylineListref.value.airlinePoint(mapOpsition[0], index);
        // 把航线位置信息存储起来
        waylineListref.value.waylinePoints.positions.push(mapOpsition[0]);
        waylineListref.value.waypointIndex = waylineListref.value.waylinePoints.positions.length - 1;
        waylineListref.value.waylineActionList.push([]);

        // 绘制线段
        waylineListref.value.drawLine(waylineListref.value.waylinePoints.positions);
        // 天上和地上的连线
        waylineListref.value.earthandearthline(waylineListref.value.waylinePoints.positions);
        // 继续打下一个点
        waylineListref.value.startDrawWayline(index + 1);
      },
    });
  },
  // 航点
  airlinePoint: function (points, index) {
    const waylineBillboard = new mars3d.graphic.BillboardEntity({
      position: points,
      style: {
        clampToGround: index == 0 ? true : false,
        image: svg(index == 0 ? 'takeOffPoint0' : '' + index),
        horizontalOrigin: mars3d.Cesium.HorizontalOrigin.CENTER,
        verticalOrigin: mars3d.Cesium.VerticalOrigin.BOTTOM,
        label: {
          // text: `${item[2]}m`,
          color: '#ffffff',
          font_size: 12,
          background: true,
          backgroundColor: '#000000',
          backgroundOpacity: 0.6,
          pixelOffsetY: 10,
        },
      },
    });
    waylineListref.value.pointsList.push(waylineBillboard);
    mapUtil.maplayer.addGraphic(waylineBillboard);
    waylineListref.value.rightClickMenu(waylineBillboard);
  },
  // 测量
  measureFun: function (points) {
    waylineListref.value.DistanceMeasure = new mars3d.graphic.DistanceMeasure({
      positions: points,
      style: {
        width: 0,
        color: '#3388ff',
      },
      showAddText: false,
      label: {
        color: '#ffffff',
        font_size: 12,
      },
    });

    mapUtil.maplayer.addGraphic(waylineListref.value.DistanceMeasure);

    waylineListref.value.distanceStr = waylineListref.value.DistanceMeasure.measured.distanceStr;
    mapUtil.maplayer.removeGraphic(waylineListref.value.DistanceMeasure);
    // console.log('航点航线总长度',waylineListref.value.distanceStr);
  },
  // 线
  drawLine: function (points, number) {
    if (points.length > 1) {
      const polyline = new mars3d.graphic.PolylineEntity({
        positions: points,
        style: {
          width: number || 5,
          color: '#00D590',
          horizontalOrigin: mars3d.Cesium.HorizontalOrigin.CENTER,
          verticalOrigin: mars3d.Cesium.VerticalOrigin.BOTTOM,
        },
      });
      waylineListref.value.waylinePolyline.forEach((item) => {
        mapUtil.maplayer.removeGraphic(item);
      });

      waylineListref.value.waylinePolyline = [];
      waylineListref.value.waylinePolyline.push(polyline);
      mapUtil.maplayer.addGraphic(waylineListref.value.waylinePolyline[0]);

      waylineListref.value.measureFun(points);
    }
  },
  facetayPolylineList: [],
  facetayWayline: function (points) {
    if (points.length > 1) {
      const polygon = new mars3d.graphic.PolygonEntity({
        positions: points,
        style: {
          color: '#3388ff',
          opacity: 0.2,
          outline: true,
          outlineWidth: 3,
          outlineColor: '#3388ff',
          clampToGround: true,
          highlight: {
            outline: true,
            outlineWidth: 3,
            outlineColor: '#ffffff',
          },
        },
      });
      waylineListref.value.facetayPolylineList.forEach((item) => {
        mapUtil.maplayer.removeGraphic(item);
      });

      waylineListref.value.facetayPolylineList = [];
      waylineListref.value.facetayPolylineList.push(polygon);
      mapUtil.maplayer.addGraphic(waylineListref.value.facetayPolylineList[0]);

      // waylineListref.value.measureFun(points);
    }
  },
  // 天上和地上的连线
  earthandearthline: async function (points) {
    waylineListref.value.earthandearthlineList.forEach((item) => {
      mapUtil.maplayer.removeGraphic(item);
    });
    waylineListref.value.earthandearthlineList = [];
    for (const item of points) {
      let res = await mars3d.PointUtil.getSurfaceHeight(mapUtil.map.scene, [item[0], item[1]], { has3dtiles: false });
      const polylineXPoints = [
        [item[0], item[1], res.height],
        [item[0], item[1], item[2]],
      ];
      const polylineX = new mars3d.graphic.PolylineEntity({
        positions: polylineXPoints,
        style: {
          width: 1,
          color: '#FEFEFE',
          horizontalOrigin: mars3d.Cesium.HorizontalOrigin.CENTER,
          verticalOrigin: mars3d.Cesium.VerticalOrigin.BOTTOM,
        },
      });
      waylineListref.value.earthandearthlineList.push(polylineX);
    }
    // points.forEach((item, index) => {
    //   const polylineXPoints = [
    //     [item[0], item[1], 0],
    //     [item[0], item[1], item[2]],
    //   ];
    //   const polylineX = new mars3d.graphic.PolylineEntity({
    //     positions: polylineXPoints,
    //     style: {
    //       // clampToGround: true,
    //       width: 1,
    //       color: '#FEFEFE',
    //       horizontalOrigin: mars3d.Cesium.HorizontalOrigin.CENTER,
    //       verticalOrigin: mars3d.Cesium.VerticalOrigin.BOTTOM,
    //     },
    //   });
    //   waylineListref.value.earthandearthlineList.push(polylineX);
    // });

    waylineListref.value.earthandearthlineList.forEach((item) => {
      mapUtil.maplayer.addGraphic(item);
    });
  },
  // 坐地面上的点
  floorPoint: function (points) {
    const waylineBillboardB = new mars3d.graphic.BillboardEntity({
      position: points,
      style: {
        clampToGround: true,
        image: svg('球'),
        scale: 1.2,
        horizontalOrigin: mars3d.Cesium.HorizontalOrigin.CENTER,
        verticalOrigin: mars3d.Cesium.VerticalOrigin.BOTTOM,
      },
    });
    // waylineListref.value.floorPointList.forEach(item=>{mapUtil.maplayer.removeGraphic(item)})
    waylineListref.value.floorPointList.push(waylineBillboardB);
    mapUtil.maplayer.addGraphic(waylineListref.value.floorPointList[waylineListref.value.floorPointList.length - 1]);
  },
  // 弹窗新建
  Add: function () {
    waylineListref.value.isShowWaylineEditor = false;
    waylineListref.value.visible = true;
    waylineListref.value.visibleEdit = true;
    waylineListref.value.waylineListPop = {}; // 清空弹窗数据
    // waylineListref.value.takeOffPoint(); // 绘制起飞点
  },
  // 弹窗编辑
  Edit: function () {
    waylineListref.value.visible = true;
    waylineListref.value.visibleEdit = false;
  },
  // 弹窗取消
  handleclose: function () {
    waylineListref.value.visible = false;
  },
  // 清空数据
  clearData: function () {
    waylineListref.value.useGlobalHeight = [];
    waylineListref.value.waylinePoints.positions = []; // 清空航点数据
    // 航线和地面的连线清空
    waylineListref.value.earthandearthlineList = [];
    // 航线清空
    waylineListref.value.waylinePolyline = [];
    // 地面的小白点清空
    waylineListref.value.floorPointList = [];
    // 清空航点动作
    waylineListref.value.waylineActionList = [];
    // 航点清空
    waylineListref.value.pointsList = [];
    // 清空面
    waylineListref.value.facetayPolylineList = [];
    waylineListref.valuefacetayLineData = [];
    waylineListref.valuefacetayLinePointData = [];
    // 重置航线设置
    waylineListref.value.waylineSetting = {};
    waylineListref.value.waypointIndex = -1;

    if (pointEditData != null) {
      mapUtil.map.removeThing(pointEditData, true);
      pointEditData = null;
    }
    waylineListref.value.take_off_point_List = [];
    waylineListref.value.take_off_point = [];
    waylineListref.value.offPointePolyline = [];
    waylineListref.value.offPointEarthandearthlineList = [];
    waylineListref.value.floorOffPointList = [];
    waylineListref.value.frustumPrimitive = [];
  },
  // 弹窗确定
  handleOk: function (item) {
    waylineListref.value.editorIn = false;
    waylineListref.value.visible = false;
    mapUtil.maplayer.stopDraw();
    mapUtil.maplayer.stopEditing();
    mapUtil.historyTrack = {}; // 清空历史轨迹

    waylineListref.value.isShowEditor = true;
    waylineListref.value.waylineListPop = item;
    if (waylineListref.value.visibleEdit) {
      mapUtil.maplayer.clear(true); // 清空地图
      waylineListref.value.clearData();
    }
    // waylineListref.value.takeOffPoint(); // 绘制起飞点
  },
  // 航线关闭X
  waylineClose: function () {
    waylineListref.value.isShowEditor = false;
    waylineListref.value.editorIn = false;
    waylineListref.value.changShowType();

    waylineListref.value.isTake_off_point = false;

    waylineListref.value.clearData();
    waylineListref.value.isSave = true;
    waylineListref.value.isShowAddPoint = false;
  },
  // 停止绘制
  stopDraw: function () {
    mapUtil.maplayer.stopDraw();
    waylineListref.value.isShowAddPoint = false;
  },
  removeThingPointEditData: function () {
    if (pointEditData != null) {
      mapUtil.map.removeThing(pointEditData, true);
      pointEditData = null;
    }
    mapUtil.maplayer.removeGraphic(waylineListref.value.frustumPrimitive[0]);
  },
  // 删除航点
  delPoint: function (idx) {
    // console.log('删除航点',idx);

    waylineListref.value.waylinePoints.positions.splice(idx, 1);
    waylineListref.value.useGlobalHeight.splice(idx, 1);
    mapUtil.maplayer.clear(true); // 清空地图
    if (pointEditData != null) {
      mapUtil.map.removeThing(pointEditData, true);
      pointEditData = null;
    }
    waylineListref.value.pointsList = [];
    waylineListref.value.earthandearthlineList = [];
    waylineListref.value.floorPointList = [];
    waylineListref.value.waylinePolyline = [];
    waylineListref.value.delActionItem(idx);
    // 绘制航线
    waylineListref.value.drawLine(waylineListref.value.waylinePoints.positions);
    // 天上和地上的连线
    waylineListref.value.earthandearthline(waylineListref.value.waylinePoints.positions);
    waylineListref.value.waylinePoints.positions.forEach((item, index) => {
      // 绘制地上的小白点
      waylineListref.value.floorPoint([item[0], item[1], 0]);
      // 绘制航点
      waylineListref.value.airlinePoint(item, index + 1);
    });
    // 再重新绘制一下起飞点
    waylineListref.value.actionlrIndex = 0;
    if (waylineListref.value.take_off_point.length > 0) {
      if (idx == 0) {
        waylineListref.value.take_off_point = waylineListref.value.take_off_point.slice(0, 1);
        if (waylineListref.value.waylinePoints.positions.length > 0) {
          let onePoint = waylineListref.value.waylinePoints.positions[0];
          // 再次计算 起飞点线的点位
          if (waylineListref.value.take_off_point[0][2] + waylineListref.value.waylineSetting.takeOffSecurityHeight > onePoint[2]) {
            waylineListref.value.take_off_point.push([
              waylineListref.value.take_off_point[0][0],
              waylineListref.value.take_off_point[0][1],
              waylineListref.value.take_off_point[0][2] + waylineListref.value.waylineSetting.takeOffSecurityHeight,
            ]);
            waylineListref.value.take_off_point.push([
              onePoint[0],
              onePoint[1],
              waylineListref.value.take_off_point[0][2] + waylineListref.value.waylineSetting.takeOffSecurityHeight,
            ]);
          } else {
            // 垂直爬升
            if (waylineListref.value.waylineSetting.flyToWaylineMode == 'safely') {
              waylineListref.value.take_off_point.push([
                waylineListref.value.take_off_point[0][0],
                waylineListref.value.take_off_point[0][1],
                onePoint[2],
              ]);
            } else {
              // 倾斜爬升
              waylineListref.value.take_off_point.push([
                waylineListref.value.take_off_point[0][0],
                waylineListref.value.take_off_point[0][1],
                waylineListref.value.take_off_point[0][2] + waylineListref.value.waylineSetting.takeOffSecurityHeight,
              ]);
            }
          }
          waylineListref.value.take_off_point.push(onePoint);
        } else {
          waylineListref.value.take_off_point.push([
            waylineListref.value.take_off_point[0][0],
            waylineListref.value.take_off_point[0][1],
            waylineListref.value.take_off_point[0][2] + waylineListref.value.waylineSetting.takeOffSecurityHeight,
          ]);
        }
      }
      // 移除图形
      mapUtil.maplayer.removeGraphic(waylineListref.value.take_off_point_List[0]);
      mapUtil.maplayer.removeGraphic(waylineListref.value.offPointePolyline[0]);
      mapUtil.maplayer.removeGraphic(waylineListref.value.offPointEarthandearthlineList[0]);
      mapUtil.maplayer.removeGraphic(waylineListref.value.floorOffPointList[0]);
      waylineListref.value.offPoint(waylineListref.value.take_off_point[0]);
      waylineListref.value.offPointEarthandearthline(waylineListref.value.take_off_point[0]);
      waylineListref.value.floorOffPoint(waylineListref.value.take_off_point[0]);
      waylineListref.value.offPointLine(waylineListref.value.take_off_point);
    }
  },
  rightClickMenu: function (pointMaker) {
    pointMaker
      .off()
      .on(mars3d.EventType.dblClick, async function (e) {
        const idx = waylineListref.value.pointsList.indexOf(pointMaker);
        // 相对地形计算点的高度
        if (waylineListref.value.waylineSetting.altitudeModel == 'aboveGroundLevel') {
          // 需要用当前点的海拔高度减去当前点地面的海拔高度，得到点与地面的距离回显高度差值
          let res = await mars3d.PointUtil.getSurfaceHeight(mapUtil.map.scene, [e.target.point.lng, e.target.point.lat], { has3dtiles: false });
          console.log('转换的海拔高度', Number(res.height.toFixed(1)), Number(e.target.point.alt.toFixed(1)));
          // 记录此刻的差值，用来航点移动时的计算
          waylineListref.value.aboveGroundLevelHT = (Number(e.target.point.alt.toFixed(1)) - Number(res.height.toFixed(1))).toFixed(1);
          waylineListref.value.pointsList[idx]?.setStyle({
            label: { text: `${(Number(e.target.point.alt.toFixed(1)) - Number(res.height.toFixed(1))).toFixed(1)}m` },
          });
        } else if (waylineListref.value.waylineSetting.altitudeModel == 'EGM96') {
          // 海拔高度就是当前点的海拔高度
          waylineListref.value.pointsList[idx]?.setStyle({ label: { text: `${e.target.point.alt.toFixed(1)}m` } });
        } else {
          console.log(Number(e.target.point.alt.toFixed(1)) - waylineListref.value.take_off_point[0][2]);

          waylineListref.value.pointsList[idx]?.setStyle({
            label: { text: `${(Number(e.target.point.alt.toFixed(1)) - Number(waylineListref.value.take_off_point[0][2].toFixed(1))).toFixed(1)}m` },
          });
        }

        if (pointEditData != null) {
          mapUtil.map.removeThing(pointEditData, true);
          pointEditData = null;
        }
        if (waylineListref.value.isShowEditor) {
          // mapUtil.maplayer.startEditing(pointMaker);
          pointEditData = new mars3d.thing.MatrixMove({
            position: pointMaker.position,
          });
          // console.log('1111111111',pointEditData);

          mapUtil.map.addThing(pointEditData);
          pointEditData.on(mars3d.EventType.change, (event) => {
            pointMaker.position = event.position;
          });
          pointEditData.on(mars3d.EventType.stop, async (event) => {
            waylineListref.value.addFrustumPrimitive(waylineListref.value.waylinePoints.positions[idx]); // 更新四锥体位置
            if (waylineListref.value.waylineSetting.altitudeModel == 'aboveGroundLevel' && waylineListref.value.changHT) {
              const res = await mars3d.PointUtil.getSurfaceHeight(mapUtil.map.scene, [e.target.point.lng, e.target.point.lat], { has3dtiles: false });
              waylineListref.value.waylinePoints.positions[idx] = [
                e.target.point.lng,
                e.target.point.lat,
                Number(res.height.toFixed(1)) + Number(waylineListref.value.aboveGroundLevelHT),
              ];
              waylineListref.value.drawLine(waylineListref.value.waylinePoints.positions);
              waylineListref.value.earthandearthlineList[idx]?.setCallbackPositions([
                [e.target.point.lng, e.target.point.lat, res.height],
                [e.target.point.lng, e.target.point.lat, Number(res.height.toFixed(1)) + Number(waylineListref.value.aboveGroundLevelHT)],
              ]);
              waylineListref.value.pointsList[idx]?.setStyle({ label: { text: `${Number(waylineListref.value.aboveGroundLevelHT)}m` } });
              waylineListref.value.floorPointList[idx]?.setCallbackPosition([e.target.point.lng, e.target.point.lat, 0]);
              waylineListref.value.pointsList[idx]?.setCallbackPosition([
                e.target.point.lng,
                e.target.point.lat,
                Number(res.height.toFixed(1)) + Number(waylineListref.value.aboveGroundLevelHT),
              ]);
              waylineListref.value.measureFun(waylineListref.value.waylinePoints.positions);
              mapUtil.map.removeThing(pointEditData, true);
              pointEditData = null;
            }
          });
        }
        // mapUtil.maplayer.startEditing(pointMaker);
        // pointEditData = new mars3d.thing.MatrixMove({
        //   position: pointMaker.position,
        // });
        // console.log('1111111111',pointEditData);

        // mapUtil.map.addThing(pointEditData);

        // pointEditData.on(mars3d.EventType.change, (event) => {
        //   pointMaker.position = event.position;
        // });
        // pointEditData.on(mars3d.EventType.stop, async (event) => {
        //   waylineListref.value.addFrustumPrimitive(waylineListref.value.waylinePoints.positions[idx]); // 更新四锥体位置
        //   if (waylineListref.value.waylineSetting.altitudeModel == 'aboveGroundLevel' && waylineListref.value.changHT) {
        //       const res = await mars3d.PointUtil.getSurfaceHeight(mapUtil.map.scene, [e.target.point.lng, e.target.point.lat])
        //       waylineListref.value.waylinePoints.positions[idx] = [e.target.point.lng, e.target.point.lat, Number(res.height.toFixed(1)) + Number(waylineListref.value.aboveGroundLevelHT)];
        //       waylineListref.value.drawLine(waylineListref.value.waylinePoints.positions);
        //       waylineListref.value.earthandearthlineList[idx]?.setCallbackPositions([
        //         [e.target.point.lng, e.target.point.lat, 0],
        //         [e.target.point.lng, e.target.point.lat, Number(res.height.toFixed(1)) + Number(waylineListref.value.aboveGroundLevelHT)],
        //       ]);
        //       waylineListref.value.pointsList[idx]?.setStyle({ label: { text: `${Number(waylineListref.value.aboveGroundLevelHT)}m` } });
        //       waylineListref.value.floorPointList[idx]?.setCallbackPosition([e.target.point.lng, e.target.point.lat, 0]);
        //       waylineListref.value.pointsList[idx]?.setCallbackPosition([e.target.point.lng, e.target.point.lat, Number(res.height.toFixed(1)) + Number(waylineListref.value.aboveGroundLevelHT)]);
        //       waylineListref.value.measureFun(waylineListref.value.waylinePoints.positions);
        //       mapUtil.map.removeThing(pointEditData, true);
        //       pointEditData = null;
        //   }

        // });
      })
      .on(mars3d.EventType.updatePosition, function (e) {
        if (waylineListref.value.timer !== null) {
          return;
        }
        waylineListref.value.timer = setTimeout(async () => {
          let res = await mars3d.PointUtil.getSurfaceHeight(mapUtil.map.scene, [e.target.point.lng, e.target.point.lat], { has3dtiles: false });
          // 找移动的那个点的下标
          const idx = waylineListref.value.pointsList.indexOf(pointMaker);
          if (waylineListref.value.waylineSetting.altitudeModel == 'EGM96') {
            // 根据下标去修改对应的位置信息
            waylineListref.value.waylinePoints.positions[idx] = [e.target.point.lng, e.target.point.lat, e.target.point.alt];
            // if(waylineListref.value.waylinePolyline[0]){
            //   console.log('线',waylineListref.value.waylinePoints.positions);
            //   waylineListref.value.waylinePolyline[0]?.setCallbackPositions(waylineListref.value.waylinePoints.positions);
            // }
            waylineListref.value.drawLine(waylineListref.value.waylinePoints.positions);
            waylineListref.value.earthandearthlineList[idx]?.setCallbackPositions([
              [e.target.point.lng, e.target.point.lat, res.height],
              [e.target.point.lng, e.target.point.lat, e.target.point.alt],
            ]);
            waylineListref.value.pointsList[idx]?.setStyle({ label: { text: `${e.target.point.alt.toFixed(1)}m` } });
            waylineListref.value.floorPointList[idx]?.setCallbackPosition([e.target.point.lng, e.target.point.lat, 0]);
            waylineListref.value.measureFun(waylineListref.value.waylinePoints.positions);
          } else if (waylineListref.value.waylineSetting.altitudeModel == 'aboveGroundLevel') {
            // console.log('相对地形高度');
            console.log(waylineListref.value.waylinePoints.positions[idx][0], waylineListref.value.waylinePoints.positions[idx][1]);
            console.log(e.target.point.lng, e.target.point.lat);

            if (
              waylineListref.value.waylinePoints.positions[idx][0] == e.target.point.lng &&
              waylineListref.value.waylinePoints.positions[idx][1] == e.target.point.lat
            ) {
              console.log('高度改变了');
              waylineListref.value.useGlobalHeight[idx] = false;
              waylineListref.value.changHT = false;
              const res = await mars3d.PointUtil.getSurfaceHeight(mapUtil.map.scene, [e.target.point.lng, e.target.point.lat], { has3dtiles: false });
              waylineListref.value.waylinePoints.positions[idx] = [e.target.point.lng, e.target.point.lat, e.target.point.alt];
              waylineListref.value.drawLine(waylineListref.value.waylinePoints.positions);
              waylineListref.value.earthandearthlineList[idx]?.setCallbackPositions([
                [e.target.point.lng, e.target.point.lat, res.height],
                [e.target.point.lng, e.target.point.lat, e.target.point.alt],
              ]);
              waylineListref.value.pointsList[idx]?.setStyle({
                label: { text: `${(Number(e.target.point.alt.toFixed(1)) - Number(res.height.toFixed(1))).toFixed(1)}m` },
              });
              waylineListref.value.aboveGroundLevelHT = (Number(e.target.point.alt.toFixed(1)) - Number(res.height.toFixed(1))).toFixed(1);
              waylineListref.value.floorPointList[idx]?.setCallbackPosition([e.target.point.lng, e.target.point.lat, 0]);
              waylineListref.value.measureFun(waylineListref.value.waylinePoints.positions);
            } else {
              console.log('高度没改变');
              waylineListref.value.changHT = true;
              waylineListref.value.waylinePoints.positions[idx] = [e.target.point.lng, e.target.point.lat, e.target.point.alt];
              waylineListref.value.drawLine(waylineListref.value.waylinePoints.positions);
              waylineListref.value.earthandearthlineList[idx]?.setCallbackPositions([
                [e.target.point.lng, e.target.point.lat, res.height],
                [e.target.point.lng, e.target.point.lat, e.target.point.alt],
              ]);
              waylineListref.value.pointsList[idx]?.setStyle({ label: { text: `${Number(waylineListref.value.aboveGroundLevelHT)}m` } });
              waylineListref.value.floorPointList[idx]?.setCallbackPosition([e.target.point.lng, e.target.point.lat, 0]);
              waylineListref.value.measureFun(waylineListref.value.waylinePoints.positions);
            }
          } else {
            waylineListref.value.waylinePoints.positions[idx] = [e.target.point.lng, e.target.point.lat, e.target.point.alt];
            // console.log('相对起飞点高度',idx);
            if (
              waylineListref.value.waylinePoints.positions[idx][0] == e.target.point.lng &&
              waylineListref.value.waylinePoints.positions[idx][1] == e.target.point.lat
            ) {
              waylineListref.value.useGlobalHeight[idx] = false;
            }
            // 根据下标去修改对应的位置信息

            waylineListref.value.drawLine(waylineListref.value.waylinePoints.positions);
            waylineListref.value.earthandearthlineList[idx]?.setCallbackPositions([
              [e.target.point.lng, e.target.point.lat, res.height],
              [e.target.point.lng, e.target.point.lat, e.target.point.alt],
            ]);
            waylineListref.value.pointsList[idx]?.setStyle({
              label: {
                text: `${(Number(e.target.point.alt.toFixed(1)) - Number(waylineListref.value.take_off_point[0][2].toFixed(1))).toFixed(1)}m`,
              },
            });
            waylineListref.value.floorPointList[idx]?.setCallbackPosition([e.target.point.lng, e.target.point.lat, 0]);
            waylineListref.value.measureFun(waylineListref.value.waylinePoints.positions);
          }
          if (idx == 0 && waylineListref.value.take_off_point.length > 0) {
            // 要区分倾斜和垂直爬升，还要区分安全起飞高度和航点的高度 需要把起飞点线的第一个点外，根据计算从新重新赋值
            // 先区安全起飞高度和航点高度
            // 需要拿到海拔高度差
            const res = await mars3d.PointUtil.getSurfaceHeight(mapUtil.map.scene, [e.target.point.lng, e.target.point.lat], { has3dtiles: false });
            // console.log('移动之后的海拔高度差',e.target.point.alt, res.height,Number(res.height.toFixed(1)),Number(e.target.point.alt) - Number(res.height.toFixed(1)));
            // 保留 take_off_point 数组中的第一个元素，移除其他元素
            waylineListref.value.take_off_point = waylineListref.value.take_off_point.slice(0, 1);
            if (waylineListref.value.waylineSetting.takeOffSecurityHeight > Number(e.target.point.alt) - Number(res.height.toFixed(1))) {
              // 爬升后再下降，垂直和倾斜都是此模式，需要重新计算坐标
              waylineListref.value.take_off_point[1] = [
                waylineListref.value.take_off_point[0][0],
                waylineListref.value.take_off_point[0][1],
                waylineListref.value.take_off_point[0][2] + waylineListref.value.waylineSetting.takeOffSecurityHeight,
              ];
              waylineListref.value.take_off_point[2] = [
                waylineListref.value.waylinePoints.positions[0][0],
                waylineListref.value.waylinePoints.positions[0][1],
                waylineListref.value.take_off_point[0][2] + waylineListref.value.waylineSetting.takeOffSecurityHeight,
              ];
              waylineListref.value.take_off_point[3] = [
                waylineListref.value.waylinePoints.positions[0][0],
                waylineListref.value.waylinePoints.positions[0][1],
                waylineListref.value.waylinePoints.positions[0][2],
              ];
            } else {
              // 再判断是倾斜还是垂直爬升 需重新计算坐标
              if (waylineListref.value.waylineSetting.flyToWaylineMode == 'safely') {
                waylineListref.value.take_off_point[1] = [
                  waylineListref.value.take_off_point[0][0],
                  waylineListref.value.take_off_point[0][1],
                  waylineListref.value.waylinePoints.positions[0][2],
                ];
                waylineListref.value.take_off_point[2] = [
                  waylineListref.value.waylinePoints.positions[0][0],
                  waylineListref.value.waylinePoints.positions[0][1],
                  waylineListref.value.waylinePoints.positions[0][2],
                ];
              } else {
                waylineListref.value.take_off_point[1] = [
                  waylineListref.value.take_off_point[0][0],
                  waylineListref.value.take_off_point[0][1],
                  waylineListref.value.take_off_point[0][2] + waylineListref.value.waylineSetting.takeOffSecurityHeight,
                ];
                waylineListref.value.take_off_point[2] = [
                  waylineListref.value.waylinePoints.positions[0][0],
                  waylineListref.value.waylinePoints.positions[0][1],
                  waylineListref.value.waylinePoints.positions[0][2],
                ];
              }
            }
            waylineListref.value.offPointLine(waylineListref.value.take_off_point);
          }
          waylineListref.value.timer = null;
        }, 100);
      })
      .on(mars3d.EventType.leftUp, function (e) {
        // mapUtil.maplayer.stopEditing();
      })
      .on(mars3d.EventType.click, function (e) {
        mapUtil.map.removeThing(pointEditData, true);
        waylineListref.value.pointsList.forEach((item) => item.setStyle({ label: { text: '' } }));
        pointEditData = null;
        // 点击时高亮显示
        const idx = waylineListref.value.pointsList.indexOf(pointMaker);
        // console.log('选中的下标', idx);
        waylineListref.value.waypointIndex = idx;
        waylineListref.value.actionlrIndex = 0;
        // 要判断 切换后的第一个航点动作是否存在 飞行器偏航角、云台偏航、俯仰角动作，然后做四锥体的渲染
        if (waylineListref.value.isShowEditor) {
          let lastAction89 = null;
          let lastAction10 = null;
          if (idx == 0) {
            const lastAction = waylineListref.value.waylineActionList[0][0];
            if (!lastAction) {
              waylineListref.value.addFrustumPrimitive(waylineListref.value.waylinePoints.positions[waylineListref.value.waypointIndex], 0, 0, 270);
              return;
            }

            if (lastAction.actionType == '8' || lastAction.actionType == '9') {
              lastAction89 = lastAction;
            }
            if (lastAction.actionType == '10') {
              lastAction10 = lastAction;
            }
          } else {
            if (
              waylineListref.value.waylineActionList[waylineListref.value.waypointIndex].length > 0 &&
              (waylineListref.value.waylineActionList[waylineListref.value.waypointIndex][0].actionType == '8' ||
                waylineListref.value.waylineActionList[waylineListref.value.waypointIndex][0].actionType == '9' ||
                waylineListref.value.waylineActionList[waylineListref.value.waypointIndex][0].actionType == '10')
            ) {
              if (
                waylineListref.value.waylineActionList[waylineListref.value.waypointIndex][0].actionType == '8' ||
                waylineListref.value.waylineActionList[waylineListref.value.waypointIndex][0].actionType == '9'
              ) {
                lastAction89 = waylineListref.value.waylineActionList[waylineListref.value.waypointIndex][0];
                for (let i = 0; i < waylineListref.value.waypointIndex; i++) {
                  if (waylineListref.value.waylineActionList[i].length > 0) {
                    waylineListref.value.waylineActionList[i].forEach((element) => {
                      // if (element.actionType == '8' || element.actionType == '9') {
                      //   lastAction89 = element;
                      // }
                      if (element.actionType == '10') {
                        lastAction10 = element;
                      }
                    });
                  }
                }
              }
              if (waylineListref.value.waylineActionList[waylineListref.value.waypointIndex][0].actionType == '10') {
                lastAction10 = waylineListref.value.waylineActionList[waylineListref.value.waypointIndex][0];
                for (let i = 0; i < waylineListref.value.waypointIndex + 1; i++) {
                  if (waylineListref.value.waylineActionList[i].length > 0) {
                    waylineListref.value.waylineActionList[i].forEach((element) => {
                      if (element.actionType == '8' || element.actionType == '9') {
                        lastAction89 = element;
                      }
                      // if (element.actionType == '10') {
                      //   lastAction10 = element;
                      // }
                    });
                  }
                }
              }
            } else {
              for (let i = 0; i < waylineListref.value.waypointIndex; i++) {
                // console.log(i);

                if (waylineListref.value.waylineActionList[i].length > 0) {
                  waylineListref.value.waylineActionList[i].forEach((element) => {
                    if (element.actionType == '8' || element.actionType == '9') {
                      lastAction89 = element;
                    }
                    if (element.actionType == '10') {
                      lastAction10 = element;
                    }
                  });
                }
              }
            }
          }
          // console.log('切换航点', lastAction89, lastAction10);
          if (!lastAction89 && !lastAction10) {
            waylineListref.value.addFrustumPrimitive(waylineListref.value.waylinePoints.positions[waylineListref.value.waypointIndex], 0, 0, 270);
            // mapUtil.maplayer.removeGraphic(waylineListref.frustumPrimitive[0]);
          }
          if (lastAction89 && lastAction10) {
            waylineListref.value.addFrustumPrimitive(
              waylineListref.value.waylinePoints.positions[waylineListref.value.waypointIndex],
              lastAction89.angle,
              0,
              270 + lastAction10.angle
            );
          }
          if (lastAction89 && !lastAction10) {
            waylineListref.value.addFrustumPrimitive(
              waylineListref.value.waylinePoints.positions[waylineListref.value.waypointIndex],
              lastAction89.angle,
              0,
              270
            );
          }
          if (!lastAction89 && lastAction10) {
            waylineListref.value.addFrustumPrimitive(
              waylineListref.value.waylinePoints.positions[waylineListref.value.waypointIndex],
              0,
              0,
              270 + lastAction10.angle
            );
          }
        }
      })
      .on(mars3d.EventType.contextMenuOpen, function (e) {
        mapUtil.map.removeThing(pointEditData, true);
        pointEditData = null;
      })
      .unbindContextMenu()
      .bindContextMenu(
        waylineListref.value.isShowEditor
          ? [
              {
                text: '删除',
                callback: (e) => {
                  const idx = waylineListref.value.pointsList.indexOf(pointMaker);
                  waylineListref.value.delPoint(idx);
                },
              },
            ]
          : []
      );
  },
  // 加动作
  addActionItem: function (item) {
    // console.log('航线动作',item);
    waylineListref.value.waylineActionList[waylineListref.value.waypointIndex].push(item);
    waylineListref.value.actionlrIndex = waylineListref.value.waylineActionList[waylineListref.value.waypointIndex].length - 1;
    // console.log(waylineListref.value.waylineActionList);
  },
  // 删除航点列表项
  delActionItem: function (index) {
    waylineListref.value.waylineActionList.splice(index, 1);
    waylineListref.value.waypointIndex = waylineListref.value.waylineActionList.length - 1;
  },
  changeActionlrIndex: function (type) {
    if (type == 'left') {
      if (waylineListref.value.actionlrIndex > 0) {
        waylineListref.value.actionlrIndex--;
      }
    }
    if (type == 'right') {
      if (waylineListref.value.actionlrIndex < waylineListref.value.waylineActionList[waylineListref.value.waypointIndex].length - 1) {
        waylineListref.value.actionlrIndex++;
      }
    }
  },
  editorIn: false,
  // 航线编辑
  EditWaline: function (item, index) {
    waylineListref.value.editorIn = true;
    waylineListref.value.isShowWaylineEditor = true;

    waylineListref.value.isShowEditor = true;
    if (waylineListref.value.take_off_point.length == 0) {
      waylineListref.value.takeOffPoint();
    }
  },
  takeOffRefPointAGLHeight: 0,
  // 最终保存
  saveWayline: async function () {
    // 起飞点与地面的海拔高度差
    if (waylineListref.value.take_off_point.length > 0) {
      let res = await mars3d.PointUtil.getSurfaceHeight(
        mapUtil.map.scene,
        [waylineListref.value.take_off_point[0][0], waylineListref.value.take_off_point[0][1]],
        { has3dtiles: false }
      );
      console.log(waylineListref.value.take_off_point[0][2], res.height);

      waylineListref.value.takeOffRefPointAGLHeight = Math.abs(Math.round(waylineListref.value.take_off_point[0][2] - Number(res.height.toFixed(1))));
    }
    console.log('高度差', waylineListref.value.takeOffRefPointAGLHeight);

    const doneInfoData = wayLineDoneInfo(waylineListref.value.waylineListPop.model_name);
    const kml = await integratedData(
      waylineListref.value.waylineListPop,
      waylineListref.value.waylineSetting,
      waylineListref.value.waylinePoints.positions,
      waylineListref.value.waylineActionList,
      waylineListref.value.take_off_point,
      waylineListref.value.takeOffRefPointAGLHeight,
      mapUtil,
      waylineListref.value.useGlobalHeight
    );
    waylineListref.value.wayLineCreateDTO = {
      droneInfo: {
        droneEnumValue: doneInfoData.droneInfo.droneEnumValue, //飞行器机型主类型
        droneSubEnumValue: doneInfoData.droneInfo.droneSubEnumValue, //飞行器机型子类型
      },
      payloadInfos: [
        {
          payloadEnumValue: doneInfoData.payloadInfo[0].payloadEnumValue, //负载机型主类型
          payloadSubEnumValue: doneInfoData.payloadInfo[0].payloadSubEnumValue, //
          payloadPositionIndex: doneInfoData.payloadInfo[0].payloadPositionIndex, //负载挂载位置
        },
      ],

      name: waylineListref.value.waylineListPop.name,
      templateTypes: '0',
      wayLineJsonObject: kml,
    };
    // console.log('最终保存',waylineListref.value.wayLineCreateDTO);
    // 判断是新增还是编辑的保存
    if (waylineListref.value.isShowWaylineEditor) {
      // 编辑
      // console.log('编辑的保存');
      await waylineApiV2
        .put(Object.assign(waylineListref.value.wayLineCreateDTO, { id: waylineListref.value.wayLineId, sysOrgCode: drawerRef.value.deptFilterCriteria.departmentName }))
        .then(function (res) {
          console.log(res);
          waylineListref.value.isSave = true;
          waylineListref.value.waylineClose();
        })
        .catch((err) => {
          console.log(err);
          waylineListref.value.isSave = true;
        });
    } else {
      //新增
      await waylineApiV2
        .create({...waylineListref.value.wayLineCreateDTO, sysOrgCode: drawerRef.value.deptFilterCriteria.departmentName})
        .then(function (res) {
          console.log(res);
          waylineListref.value.isSave = true;
          waylineListref.value.waylineClose();
        })
        .catch((err) => {
          console.log(err);
          waylineListref.value.isSave = true;
        });
      // console.log('新增的保存');
    }
    // waylineListref.value.changShowType();
  },
  // 航点航线删除
  delWayline: function (item) {
    // console.log('删除',item);
    waylineApiV2.delete({ id: item.id, sysOrgCode: drawerRef.value.deptFilterCriteria.departmentName }).then(() => {
      waylineListref.value.focus = 0;
      waylineListref.value.changShowType();
    });
  },
  frustumPrimitive: [],
  // 四锥体
  addFrustumPrimitive: function (position, heading, pitch, roll) {
    mapUtil.maplayer.removeGraphic(waylineListref.value.frustumPrimitive[0]);
    const graphicFrustum = new mars3d.graphic.FrustumPrimitive({
      position,
      // targetPosition: [waylineListref.value.waylinePoints.positions[idx][0], waylineListref.value.waylinePoints.positions[idx][1], 100], // 可选
      style: {
        angle: 7, // 四棱锥体张角
        angle2: 7, // 四棱锥体张角2
        heading: heading || 0, // 方向角
        pitch: pitch || 0, // 俯仰角
        roll: roll || 270, // 翻滚角
        length: 100, // targetPosition存在时无需传 长度值
        color: '#00D590',
        opacity: 0.2,
        outline: true,
        outlineColor: '#00D590',
        outlineOpacity: 0.5,
      },
    });
    waylineListref.value.frustumPrimitive[0] = graphicFrustum;
    mapUtil.maplayer.addGraphic(graphicFrustum);
  },
});
// end

let agoraRtc = {
  // For the local audio and video tracks.
  localAudioTrack: null,
  localVideoTrack: null,
  client: null,
};

const layerInfoChild = ref(null);
const router = useRouter();

const svg = (name) => {
  return `/mapView/${name}.svg`; //拼接文件路径
};
const png = (name) => {
  return `/mapView/${name}.png`; //拼接文件路径
};
let hasUnreadval = ref(false);
const hasUnread = (val) => {
  hasUnreadval.value = val;
};
const remote = ref({
  showConfirm: false,
  navToRemote: function () {
    remote.showConfirm = false;
    let device = drawerRef.value.deviceList[drawerRef.value.deviceList.focus];
    if (device.deviceList && device.deviceList.length > 0) {
      router.push({
        path: '/remoteOverTwo',
        query: {
          deviceId: device.deviceId,
          deviceSn: device.deviceSn,
          droneId: device.deviceList[0].deviceId,
          aircraftDeviceId: device.deviceList[0].id,
        },
      });
    }
  },
});

const satelliteLayer = ref(null);
const aiSetupRef = ref(null);
const layerTreeBoolean = ref(false);
const aiRecognitionRef = ref(null);
let TurnAiList = ref(null);
const isShowCamera = ref(false); //是否显示摄像头

let drawerRef = ref({
  showType: -1,
  visible: true,
  hover: 0,
  flvURL: ``,
  devicePageNo: 1,
  deviceList: [],
  myDepart:[],
  myDepartListArr:[],
  deviceModelList:[
      {
        label:'全部设备',
        value:null,
      },
      {
        label:'DJI Dock',
        value:'DJI Dock'
      },
      {
        label:'DJI Dock2',
        value:'DJI Dock2'
      },
      {
        label:'DJI Dock3',
        value:'DJI Dock3'
      },
      {
        label:'DJI RC Pro',
        value:'DJI RC Pro'
      },
      {
        label:'DJI RC Plus',
        value:'DJI RC Plus'
      },
      {
        label:'DJI RC Plus2',
        value:'DJI RC Plus2'
      },
      {
        label:'DJI Smart Controller',
        value:'DJI Smart Controller'
      }
    ],
  waylinePageNo: 1,
  waylineList: [],
  markerList: [],
  interval: null,
  aerodromeList: {},
  aircraftList: {},
  departmentList:[],//部门列表
  deptFilterCriteria:{//部门筛选条件
    departmentName:null,
    deviceModel:null,
    deviceName:null,
  },
  showList: async function (showType, direction, keepList) {
    // mapUtil.map.setOptions({
    //   terrain: {
    //     url: "//data.mars3d.cn/terrain",
    //     show: false,
    //     clip: true
    //   },
    // })
    waylineListref.value.clearData();
    if (this.showType == 0 && deviceStatus.value.fullVideo) {
      deviceUtil.fullScreenVideo(deviceStatus.value.fullVideo); //切换设备前先切换地图显示
    }
    var zhis = this;
    zhis.showType = showType
    var init = zhis.showType != showType && direction == null;
    keepList || (zhis.showType = showType);
    if (showType == 0 && (!init || zhis.deviceList.length == 0)) {
      const list = zhis.deviceList;
      // 把摄像头设备放到设备列表中
      const camersData = [];
      await deviceApi.cameraList({ pageSize: 100, pageNo: 1, deviceType: '4', state: '1', sysOrgCode: drawerRef.value.deptFilterCriteria.departmentName, }).then(function (res) {
        // 加层过滤，防止后台给离线设备
        let o = res.records.filter((item) => item.state == '1');
        camersData.push(...o);
      });
      await deviceApi
        .list({ pageSize: 100, pageNo: zhis.devicePageNo + (direction || 0), deviceType: '2,3', state: '1', projectId: localStorage.getItem('Project_Id'), sysOrgCode: drawerRef.value.deptFilterCriteria.departmentName, deviceModel:drawerRef.value.deptFilterCriteria.deviceModel, deviceName: drawerRef.value.deptFilterCriteria.deviceName })
        .then(function (res) {
          res.records.length > 0 && (zhis.devicePageNo += direction || 0);
          if (init || !direction) {
            list.length = 0;
          }
          list.push(
            ...res.records
              .filter(function (item) {
                return (item.deviceType == 2 && item.state != 0) || (item.deviceType == 3 && item.state != 99);
              })
              .map(function (item) {
                return {
                  ...item,
                  droneBatteryMaintenanceInfo: [],
                };
              })
          );
          list.push(...camersData);
          list.hasNextPage = res.current < res.pages;
          // list.focus = Math.min(list.focus || 0, list.length - 1);

          setTimeout(function () {
            list.forEach((device) => {
              device && zhis.requestOnlineShadow(device);
            });
          }, 500);
        });
    } else if (showType == 1 && (!init || zhis.waylineList.length == 0)) {
      var list = zhis.waylineList;
      await waylineApi
        .query({ pageSize: 100, pageNo: zhis.waylinePageNo + (direction || 0), order: 'desc', column: 'createTime' })
        .then(function (res) {
          res.records.length > 0 && (zhis.waylinePageNo += direction || 0);
          if (init || !direction) {
            list.length = 0;
          }
          list.push(...res.records);
          list.hasNextPage = res.current < res.pages;
          list.focus = Math.min(list.focus || 0, list.length - 1);
          console.log('waylineList.length= ' + list.length);
        });
    } else if (showType == 2) {
      // 刷新标记列表时，清空缓存的marker
      for (const key in mapUtil.markers) {
        if (mapUtil.markers.hasOwnProperty(key)) {
          delete mapUtil.markers[key];
        }
      }

      var list = this.markerList;
      await markerApi.groups({sysOrgCode :drawerRef.value.deptFilterCriteria.departmentName, elementName: drawerRef.value.markerList.keywords}).then(function (res) {
        list.length = 0;
        list.push(...res);
        for (let group of res) {
          for (let ele of group.elements) {
            if (ele.display == 0) {
              delete mapUtil.markers[ele.elementId];
              continue;
            }
            mapUtil.markers[ele.elementId] = ele;
          }
          group.sel = [];
        }
        list.hasNextPage = res.current < res.pages;
        list.focus = Math.min(list.focus || 0, list.length - 1);
        let folder = list[list.focus];
        if (folder) {
          folder.expand = true;
        }
      });
    }
    keepList || mapUtil.updateView();
  },
  loadOnScroll: function (scrollId) {
    if ((this.showType == 0 && !this.deviceList.hasNextPage) || (this.showType == 1 && !this.waylineList.hasNextPage)) {
      return;
    }
    var container = document.getElementById(scrollId);
    if (container.scrollTop + container.clientHeight >= container.scrollHeight) {
      console.log('到底了，自动加载下一页');
      this.showList(this.showType, 1);
    }
  },
  toggle: function () {
    this.visible = !this.visible;
  },
  operate: function (device, instuct) {
    const deviceState = (device.deviceList || [])[0]?.state;
    // console.log(deviceState, 'deviceState');

    // 假设您想检查 state 是否为 0, 1 或 18
    const invalidStates = ['0', '1', '18'];
    console.log(device, instuct, 'device,instuct');
    if (invalidStates.includes(deviceState)) {
      if (instuct == 'drone_emergency_stop') {
        return message.warning('无人机为待机、离线、准备起飞状态，急停按钮无效!');
      } else {
        return message.warning('无人机为待机、离线、准备起飞状态，返航按钮无效!');
      }
    }
    // if ((device.deviceList || [])[0]?.state || 18 == 0 || (device.deviceList || [])[0]?.state || 18 == 1 || (device.deviceList || [])[0]?.state || 18 == 18) {
    //   if (instuct == 'drone_emergency_stop') {
    //     return message.warning('无人机为待机、离线、准备起飞状态，急停按钮无效!');
    //   } else {
    //     return message.warning('无人机为待机、离线、准备起飞状态，返航按钮无效!');
    //   }
    // }

    // return;
    const obg = {
      deviceId: device.deviceId,
      deviceSn: device.deviceSn,
      instuct: instuct,
      reqId: localStorage.getItem('wsClientId'),
    };
    instructPost(obg).then((res) => {
      if (res.data.code == 200) {
        createMessage.success(instuct === 'drone_emergency_stop' ? '飞行器急停执行成功!' : '一键返航执行成功!');
      }
    });
  },
  selectDevice: function (device, index) {
    // TurnAiList.value = [];
    mapUtil.map.setCameraView({ lng: device.longitude, lat: device.latitude });
    const zhis = this;
    if (deviceStatus.value.deviceId == device.id) {
      return;
    }

    if (deviceStatus.value.fullVideo) {
      deviceUtil.fullScreenVideo(deviceStatus.value.fullVideo); //切换设备前先切换地图显示
    }
    // 多加个判断字段，如果是摄像头就不显示statebar_deviceState组件
    if (device.deviceType == '4') {
      isShowCamera.value = false;
    } else {
      isShowCamera.value = true;
    }
    zhis.deviceList.focus = index;

    deviceStatus.value.updateByDevice(device);

    mapUtil.updateView();

    if (zhis.interval != null) {
      clearInterval(zhis.interval);
    }
    zhis.interval = setInterval(zhis.requestOnlineShadow(), 1000);
    if (device.deviceType != '4') {
      aiRecognitionRef.value.close(false);
    }
  },
  requestOnlineShadow: function (device) {
    const idx = drawerRef.value.deviceList.focus,
      curr = device || drawerRef.value.deviceList[idx];

    if (curr.uavFlag || curr.uavFlag == '0') {
      deviceApi.getShadow(curr.deviceList[0].deviceId).then((res22) => {
        deviceStatus.value.updateByShadow(null, null, curr.deviceList[0], res22.aircraftShadow);
        console.log(mapUtil.devices[curr.id]);
        if (res22.aircraftShadow.longitude) {
          let lat = [
            parseFloat(res22.aircraftShadow.longitude + ''),
            parseFloat(res22.aircraftShadow.latitude + ''),
            parseFloat(res22.aircraftShadow.height + ''),
          ];
          let tolerance = 0.0001; // 自定义阈值
          if (JSON.stringify(mapUtil.historyTrack) == '{}') {
            return;
          }
          if (mapUtil.historyTrack[curr.id].length == 0) {
            mapUtil.historyTrack[curr.id].push(lat);
          } else {
            if (
              Math.abs(mapUtil.historyTrack[curr.id][mapUtil.historyTrack[curr.id].length - 1][0] - lat[0]) > tolerance ||
              Math.abs(mapUtil.historyTrack[curr.id][mapUtil.historyTrack[curr.id].length - 1][1] - lat[1]) > tolerance ||
              parseInt(Math.abs(mapUtil.historyTrack[curr.id][mapUtil.historyTrack[curr.id].length - 1][2])) != parseInt(Math.abs(lat[2]))
            ) {
              mapUtil.historyTrack[curr.id].push(lat);
              mapUtil.VehiclePosition = lat;
            }
          }

          mapUtil.devices[curr.id]?.[1] && mapUtil.devices[curr.id][1].addTimePosition(lat, 1);
          // console.log('实时的',mapUtil.historyTrack);

          mapUtil.devices[curr.id]?.[3] && mapUtil.devices[curr.id][3].setCallbackPositions(mapUtil.historyTrack[curr.id]);

          // mapUtil.devices[curr.id] && mapUtil.devices[curr.id][2].setCallbackPositions([lat]);
          if (res22.state == 5 && !mapUtil.devices[curr.id]?.[2]?.options?.attr?.airRoute) {
            mapUtil.updateView(true);
          }

          if (curr.state == 0 && (mapUtil.devices[curr.id]?.[2]?.options?.attr?.airRoute || mapUtil.devices[curr.id][3]?.options?.attr?.airRoute)) {
            mapUtil.updateView(true);
          }

          if (mapUtil.devices[curr.id].length > 1 && res22.state == 18) {
            mapUtil.updateView(true);
          }
        }
      });
    }

    // 增加类型判断，摄像头设备则不走下面逻辑
    if (curr && curr.deviceType != '4' && !curr.uavFlag) {
      deviceApi.getShadow(curr.deviceId).then((res) => {
        deviceStatus.value.updateByShadow(curr, res, null, null);
        curr.droneBatteryMaintenanceInfo = res?.dockShadow?.droneBatteryMaintenanceInfo || null;
        drawerRef.value.aerodromeList = res;
      });
      curr.deviceList?.[0] &&
        deviceApi.getShadow(curr.deviceList[0].deviceId).then((res) => {
          drawerRef.value.aircraftList = res;
          deviceStatus.value.updateByShadow(null, null, curr.deviceList[0], res);
          if (mapUtil.devices[curr.id].length == 1 && res.state != 18) {
            mapUtil.updateView(true);
            return;
          }
          // console.log('res', res);

          let lat = [
            parseFloat(res.aircraftShadow.longitude + ''),
            parseFloat(res.aircraftShadow.latitude + ''),
            parseFloat(res.aircraftShadow.height + ''),
          ];
          let tolerance = 0.0001; // 自定义阈值
          // console.log(mapUtil.historyTrack);
          // mapUtil.devices[curr.id]?.[1] && mapUtil.devices[curr.id][1].setCallbackPosition(lat);
          // mapUtil.devices[curr.id]?.[1] && mapUtil.devices[curr.id][1].setCallbackPosition(
          //   new Cesium.CallbackProperty(() => {
          //     return mars3d.PointTrans.lonlat2cartesian(lat);
          //   })
          // );
          // mapUtil.devices[curr.id]?.[1] && mapUtil.devices[curr.id][1].addTimePosition(lat,0);
          // const getFormattedTime = () => {
          //   const now = new Date();
          //   const year = now.getFullYear();
          //   const month = String(now.getMonth() + 1).padStart(2, '0');
          //   const day = String(now.getDate()).padStart(2, '0');
          //   const hours = String(now.getHours()).padStart(2, '0');
          //   const minutes = String(now.getMinutes()).padStart(2, '0');
          //   const seconds = String(now.getSeconds()).padStart(2, '0');
          //   return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
          // };
          // mapUtil.devices[curr.id]?.[1] && mapUtil.devices[curr.id][1].addTimePosition(lat,getFormattedTime());
          if (lat[0] !== 0 || lat[1] !== 0) {
            mapUtil.devices[curr.id]?.[1] && mapUtil.devices[curr.id]?.[1]?.addTimePosition(lat, 1);
          }

          mapUtil.devices[curr.id]?.[3] && mapUtil.devices[curr.id]?.[3]?.setCallbackPositions(mapUtil.historyTrack[curr.id]);
          console.log(mapUtil.devices[curr.id]?.[3]);
          if (curr?.Video3D && Object.keys(curr?.Video3D).length > 0) {
            //修改投射映像位置
            // curr.Video3D.cast[0].position = mars3d.PointTrans.lonlat2cartesian(lat)
            // curr.Video3D.cast[0].position =  new Cesium.CallbackProperty(() => {
            //   return mars3d.PointTrans.lonlat2cartesian(lat);
            // }, false),
            curr.Video3D.cast[0].style.angle = drawerRef.value.aerodromeList.dockShadow.mainGimbalCameraVo.hfov;
            // curr.Video3D.cast[0].style.angle = 10;
            curr.Video3D.cast[0].style.angle2 = drawerRef.value.aerodromeList.dockShadow.mainGimbalCameraVo.vfov;
            curr.Video3D.cast[0].style.distance = drawerRef.value.aerodromeList.dockShadow.mainGimbalCameraVo.projectionDistance;
            curr.Video3D.cast[0].style.pitch = parseFloat(res.aircraftShadow.attitudePitch);
            curr.Video3D.cast[0].style.heading = parseFloat(res.aircraftShadow.attitudeHead).toFixed(2);

            console.log(curr.Video3D.cast[0].style, 'style');
            if (Math.abs(res.aircraftShadow.attitudePitch) < 25) {
              drawerRef.value.videoProjection(null, curr);
              message.error('镜头俯仰角小于25°，无法开启视频投射！');
            }
          }
          // mapUtil.devices[curr.id] && mapUtil.devices[curr.id][2].setCallbackPositions([lat]);
          if (res.state == 5 && !mapUtil.devices[curr.id]?.[2]?.options?.attr?.airRoute && curr.deviceType != 2) {
            mapUtil.updateView(true);
          }
          console.log(mapUtil.devices[curr.id]?.[2],'mapUtil.devices[curr.id]?.[2]','curr',curr);
          if (curr.state == 0 && (mapUtil.devices[curr.id]?.[2]?.options?.attr?.airRoute || mapUtil.devices[curr.id][3]?.options?.attr?.airRoute)) {
            mapUtil.updateView(true);
          }

          if (mapUtil.devices[curr.id].length > 1 && res.state == 18) {
            mapUtil.updateView(true);
          }

          if (JSON.stringify(mapUtil.historyTrack) == '{}') {
            mapUtil.historyTrack[curr.id] = [];

            mapUtil.historyTrack[curr.id].push(lat)
            return;
          }
          if (!mapUtil.historyTrack[curr.id] || mapUtil.historyTrack?.[curr.id]?.length == 0) {
            if (lat[0] === 0 || lat[1] === 0) return;
            mapUtil.historyTrack[curr.id].push(lat);
          } else {
            if (
              Math.abs(mapUtil.historyTrack[curr.id][mapUtil.historyTrack[curr.id].length - 1][0] - lat[0]) > tolerance ||
              Math.abs(mapUtil.historyTrack[curr.id][mapUtil.historyTrack[curr.id].length - 1][1] - lat[1]) > tolerance ||
              parseInt(Math.abs(mapUtil.historyTrack[curr.id][mapUtil.historyTrack[curr.id].length - 1][2])) != parseInt(Math.abs(lat[2]))
            ) {
              if (lat[0] === 0 || lat[1] === 0) return;
              mapUtil.historyTrack[curr.id].push(lat);
              mapUtil.VehiclePosition = lat;
            }
          }
          // console.log(drawerRef.value.aerodromeList,'tyf-----------------------')
          // if(res.dockShadow?.wirelessLink?.drc_state != 2 && res.state != 18){
          //   modeEnter({
          //     deviceId:drawerRef.value.aerodromeList.deviceId,
          //     deviceSn:drawerRef.value.aerodromeList.deviceSn,
          //     instuct: 'drc_mode_enter',
          //     reqId: localStorage.getItem('wsClientId'),
          //     action: { payload_index: res.dockShadow?.liveCapacity?.device_list?.[1]?.camera_list?.[1]?.camera_index }
          //   })
          // }
        });
    }
    return drawerRef.value.requestOnlineShadow;
  },
  selectWayline: function (index, editable) {
    this.waylineList.focus = index;
    waylineEditor.value.data = this.waylineList[index];
    waylineEditor.value.isShowEditor = editable;

    mapUtil.updateView();
  },
  selectMarker: function (evt, folder, item, folderIdx, markIdx, display) {
    // if(display == 0) return;

    this.markerList.focus >= 0 && (this.markerList[this.markerList.focus].focus = -1);

    this.markerList.focus = folderIdx;
    folder.focus = markIdx;

    let sel = folder.sel,
      existIndex = sel.indexOf(markIdx);
    if (evt.ctrlKey) {
      if (existIndex >= 0) {
        sel.splice(existIndex, 1);
      } else {
        sel.push(markIdx);
      }
    } else if (evt.shiftKey) {
      if (sel.length == 0) {
        sel.push(markIdx);
      } else {
        let startIdx = sel[sel.length - 1],
          endIdx = markIdx;
        for (let j = Math.min(startIdx, endIdx); j <= Math.max(startIdx, endIdx); j++) {
          if (sel.indexOf(j) == -1) {
            sel.push(j);
          }
        }
      }
    } else {
      folder.sel = [markIdx];
    }

    mapUtil.updateView();
  },
  aiOpen: function (data) {
    if (data.isAi == 1 && data.liveType == 1) {
      aiSetupRef.value.stop(data);
      TurnAiList.value = null;
      setTimeout(() => {
        var i = 0;
        if (deviceStatus.value.droneCameras.length <= 1) {
          deviceStatus.value.toggleDroneVideo(0, false);
        } else {
          deviceStatus.value.toggleDroneVideo(1, false);
        }
      }, 1000);
    } else {
      aiSetupRef.value.close(data);
    }
    console.log(TurnAiList);
  },
  videoProjection: function (video, data) {
    console.log(video, '投射', data);
    return new Promise((resolve, reject) => {
      if (!data?.Video3D || Object.keys(data.Video3D).length === 0) {
        // data.Video3D = {
        //       videDom:[],
        //       cast:[],
        //     };
        // mapUtil.drawVideo3D(video,data)
        // mapUtil.map.scene.globe.depthTestAgainstTerrain = false;
        // mapUtil.map.scene.requestRender();
        mapViewApi
          .toggleProjection({
            deviceId: data.deviceId,
            projectionEnabled: 1,
          })
          .then((res) => {
            if (res.data.code == 200) {
              data.Video3D = {
                videDom: [],
                cast: [],
              };
              mapUtil.drawVideo3D(video, data);
            } else {
              message.error(res.data.message);
              data.cast = false;
            }
            resolve(res);
          });
      } else {
        data.cast = false;
        mapUtil.maplayer.removeGraphic(data.Video3D.cast[0]);
        data.Video3D.videDom[0].remove();
        data.Video3D = {};
        mapViewApi.toggleProjection({
          deviceId: data.deviceId,
          projectionEnabled: 0,
        });
        // mapUtil.map.scene.globe.depthTestAgainstTerrain = true;
        // mapUtil.map.scene.requestRender();
        console.log(data, 'data关闭');
      }
    });
  },
  departmentChange: function (event) {
    console.log(event,'部门')
    drawerRef.value.showList(0)
  }
});

const TurnAi = (res, data, list) => {
  console.log(res, data, 'data-----------');
  data.urlType = 1;
  data.liveType = 1;
  data.videoUrl = res.aiPlayUrl;
  data.aiProducer = res.aiProducer;
  TurnAiList.value = list;
};

const deviceStatus = ref({
  deviceId: 0,
  deviceName: '',
  deviceSn: '',
  deviceType: '3',
  deviceState: 0,
  cameras: [],
  remain_photo_num: 0,
  deviceWireless: 0,
  safe_land_height: 0,
  droneId: 0,
  droneName: '',
  droneSn: '',
  droneModel: '',
  droneState: 0,
  droneCameras: [],
  droneWireless: 0,
  gps_number: 0,
  height: 0,
  relativeHeight: 0,
  total_flight_distance: 0,
  vpeed: 0,
  hspeed: 0,
  fullVideo: null,
  videoLoadingSnIndex: null,
  updateVideoParamsTimer: null,
  uavFlag: false,
  toggleDockVideo: function (i) {
    var devicelist = drawerRef.value.deviceList,
      device = devicelist[devicelist.focus];
    if (this.fullVideo) {
      deviceUtil.fullScreenVideo(this.fullVideo); //切换设备前先切换地图显示
    }

    var zhis = this,
      snindex = zhis.deviceSn + i,
      currVideo;
    for (var idx in device.videos) {
      if (device.videos[idx].snindex == snindex) {
        currVideo = device.videos[idx];
        break;
      }
    }
    if (currVideo && !currVideo.isHide) {
      // deviceUtil.closeVideo(currVideo);
      return;
    }

    if (this.videoLoadingSnIndex == snindex) {
      return;
    }

    this.videoLoadingSnIndex = snindex;
    const liveParams = {
      cameraType: 0,
      gatewayDeviceSn: zhis.deviceSn,
      deviceId: zhis.deviceId,
      deviceSn: zhis.deviceSn,
      cameraId: zhis.cameras[i].camera_index,
      videoIndex: zhis.cameras[i].video_list[0].video_index,
    };
    deviceApi.getLivingUrl(liveParams).then(function (res) {
      //var liveUrl = "rtmp://************/live/1699878048201";
      if ('urlType' in res) {
        if (currVideo) {
          currVideo.isHide = false;
          currVideo.url = res.livePalyUrl.replace('http://', 'ws://').replace('/hls.m3u8', '.live.flv');
        } else {
          device.videos.push((currVideo = Object.assign({ snindex: snindex, deviceName: zhis.deviceName }, liveParams)));
        }
        zhis.updateVideoParams(currVideo, res);
      }
    });
  },
  toggleDroneVideo: function (i, ai = true) {
    var devicelist = drawerRef.value.deviceList,
      device = devicelist[devicelist.focus];
    if (this.fullVideo) {
      deviceUtil.fullScreenVideo(this.fullVideo); //切换设备前先切换地图显示
    }
    if (!device?.cast) {
      device.cast = false; //视频投射
    }
    var zhis = this,
      snindex = zhis.droneSn + i,
      currVideo;
    for (var idx in device.videos) {
      if (device.videos[idx].snindex == snindex) {
        currVideo = device.videos[idx];
        break;
      }
    }
    if (currVideo && !currVideo.isHide && ai) {
      // deviceUtil.closeVideo(currVideo);
      return;
    }

    if (this.videoLoadingSnIndex == snindex) {
      return;
    }

    this.videoLoadingSnIndex = snindex;
    console.log(zhis, 'zhis669966996699');
    const liveParams = {
      // cameraType: i == 0 ? 1 : i == 1 ? 2 : 1,
      cameraType: zhis.droneCameras[i].video_list[0].switchable_video_types?.length > 1 ? '2' : 1,
      gatewayDeviceSn: zhis.deviceSn,
      deviceId: zhis.droneId,
      deviceSn: zhis.droneSn,
      cameraId: zhis.droneCameras[i].camera_index,
      videoIndex: zhis.droneCameras[i].video_list[0].video_index,
      stopAiAfter:!ai,
    };

    function handleVideo(VideoRes) {
      if (device.deviceType == '2') {
        for (var idx in device.videos) {
          let otherVideo = device.videos[idx];
          otherVideo && !otherVideo.isHide && deviceUtil.closeVideo(otherVideo, true);
        }
      }

      if (currVideo) {
        currVideo.isHide = false;
        // currVideo.url = res.livePalyUrl.replace('http://', 'ws://').replace('/hls.m3u8', '.live.flv');
      } else {
        device.videos.push((currVideo = Object.assign({ snindex: snindex, deviceName: deviceStatus.value.formatDroneCamera(i) }, liveParams)));
        // zhis.videos.push({snindex: snindex, url: res.livePalyUrl.replace('http://', 'ws://').replace('/hls.m3u8', '.live.flv')});
      }

      zhis.updateVideoParams(currVideo, VideoRes);
    }

    // 演示代码，注释先
    // if (device.uavFlag != null) {
    // rtmp://139.9.180.166:5080/live/gdus400
    // handleVideo({ urlType: '1', livePalyUrl: 'http://139.9.180.166:15080/live/' + zhis.droneSn + '/hls.m3u8' });
    // return FullLivingApi.queryLiveList().then(function(res){
    //     let droneVideos = res.filter(function(item){
    //         return item.urlType==1 && item.gatewayDeviceSn==device.deviceSn;
    //     });
    //     droneVideos.length>0 && handleVideo(droneVideos[0]);
    // });
    // }

    deviceApi.getLivingUrl(liveParams).then(function (res) {
      if (res.isAi == 1 && res.liveType == 1) {
        let sceneIds = [];
        aiAlgorithmManagementApi.getAiCameraBindSceneList({ deviceLiveId: res.deviceLiveId }).then((res2) => {
          console.log(res2, 'res66663333');
          res2 &&
            res2.scenes.forEach((item) => {
              sceneIds.push(item.sceneId);
            });

          if (sceneIds.length > 0) {
            TurnAiList.value = res2.scenes;
            aiAlgorithmManagementApi.start({ deviceLiveId: res.deviceLiveId, sceneIdList: sceneIds }).then((url) => {
              const obg = {
                ...res,
                urlType: 1,
                aiProducer: url.aiProducer,
                livePalyUrl: url.aiPlayUrl,
              };
              handleVideo(obg);
            });
          } else {
            'urlType' in res && handleVideo(res);
            TurnAiList.value = [];
          }
        });
      } else {
        'urlType' in res && handleVideo(res);
        TurnAiList.value = [];
      }
    });
  },
  refreshVideo: function (video, ref) {
    var zhis = this;
    deviceApi.getLivingUrl(video).then(function (res) {
      //var liveUrl = "rtmp://************/live/1699878048201";
      if ('urlType' in res) {
        zhis.updateVideoParams(video, res);
        console.log(ref, video);
        const index = ref.findIndex((item) => item.videoOption.videoUrl === video.videoUrl);
        if (index !== -1) {
          ref[index].Refresh();
        }
      }
    });
  },
  updateVideoParams: function (currVideo, res) {
    let zhis = this;
    var devicelist = drawerRef.value.deviceList,
      device = devicelist[devicelist.focus];
    // zhis.updateVideoParamsTimer && clearTimeout(zhis.updateVideoParamsTimer);
    // zhis.updateVideoParamsTimer = setTimeout(() => {
    currVideo.isAi = res.isAi;
    // currVideo.aiProducer = res.isAi == 1 && res.liveType == 1 && res.aiProducer ? 'gddi':'rtmp';
    currVideo.aiProducer = res.aiProducer;
    currVideo.liveType = res.liveType;
    currVideo.deviceLiveId = res.deviceLiveId;
    currVideo.urlType = res.urlType;
    if (currVideo.urlType == '0') {
      currVideo.url = null;
      currVideo.appId = res.livePlayAgoraUrl.appid;
      currVideo.channel = res.livePlayAgoraUrl.channel;
      currVideo.token = res.livePlayAgoraUrl.token;
      currVideo.uid = res.livePlayAgoraUrl.uid;
    } else if (currVideo.urlType == '1') {
      if (res.isAi == 1 && res.liveType == 1) {
        // currVideo.videoUrl = res.livePalyUrl.replace('/hls.m3u8', '.live.flv');
        currVideo.videoUrl = res.livePalyUrl;
      } else {
        // currVideo.videoUrl = res.livePalyUrl.replace('http://', 'ws://').replace('/hls.m3u8', '.live.flv');
        // currVideo.url = res.livePalyUrl.replace('/hls.m3u8', '.live.flv');
        currVideo.videoUrl = res.wsUrl
      }
    }

    zhis.videoLoadingSnIndex = null;
    if (
      drawerRef.value.aerodromeList.dockShadow.projectionEnabled &&
      drawerRef.value.aerodromeList.dockShadow.mainGimbalCameraVo.cameraIndex == currVideo.cameraId
    ) {
      device.cast = true; //开启投射视频投射开关
      drawerRef.value.videoProjection(currVideo, device);
      console.log(currVideo, '视频投射currVideo', device, 'device');
    }
    // }, 300);
  },
  updateByDevice: function (device) {
    if (!device.videos) {
      device.videos = [];
    }

    this.deviceId = device.id;
    this.deviceName = device.deviceName;
    this.deviceSn = device.deviceSn;
    this.deviceType = device.deviceType;
    this.deviceState = device.state;
    device.deviceList || (device.deviceList = []);
    if (device.deviceList.length > 0) {
      this.droneId = device.deviceList[0].id;
      this.droneName = device.deviceList[0].deviceName;
      this.droneSn = device.deviceList[0].deviceSn;
      this.droneState = device.deviceList[0].state;
      this.droneModel = device.deviceList[0].deviceModel;
    }
    // device.videos.length = 0;
    this.updateCameras(device.liveCapacity, device.deviceSn);
  },
  updateByShadow: function (device, deviceShadow, drone, droneShadow) {
    if (deviceShadow != null) {
      let gatewayShadow = deviceShadow.dockShadow || deviceShadow.gatewayShadow;
      device.state = this.deviceState = deviceShadow.state;
      this.remain_photo_num = gatewayShadow?.remain_photo_num || (gatewayShadow?.wirelessLink?.media_file_detail || {}).remain_upload || 0;
      this.deviceWireless = JSON.parse(
        gatewayShadow.dockState ? gatewayShadow.dockState.network_state.rate : gatewayShadow.deviceWireless || '0'
      ).toFixed(2);
      this.safe_land_height = gatewayShadow.dockState
        ? gatewayShadow.dockState.alternate_land_point.safe_land_height
        : gatewayShadow.safe_land_height || 0;
      this.droneWireless =
        (gatewayShadow.wirelessLink ? gatewayShadow.wirelessLink.wireless_link || gatewayShadow.wirelessLink : {}).sdr_quality ||
        gatewayShadow.droneWireless ||
        0;
      this.updateCameras(gatewayShadow.liveCapacity, device.deviceSn);
    }
    if (droneShadow != null) {
      drone.state = this.droneState = droneShadow.state;
      const aircraftShadow = droneShadow.aircraftShadow || {};
      drone.latitude = aircraftShadow.latitude;
      drone.longitude = aircraftShadow.longitude;

      if (drone.deviceOsd) {
        const battery = (drone.deviceOsd.battery = drone.deviceOsd.battery || {});
        battery.capacity_percent = aircraftShadow.battery?.capacity_percent || 0;
      }

      if (drone.uavFlag) {
        drone.deviceOsd = drone.deviceOsd ?? {};
        drone.deviceOsd.battery = {
          ...drone.deviceOsd.battery,
          capacity_percent: droneShadow.battery.capacity_percent,
        };
        this.uavFlag = true;
      } else {
        this.uavFlag = false;
      }

      this.gps_number =
        droneShadow.gps_number ||
        JSON.parse(aircraftShadow.positionState || '{}').gps_number ||
        JSON.parse(droneShadow.positionState || '{}').gps_number ||
        0;
      this.height = JSON.parse(droneShadow.height != undefined ? droneShadow.height : aircraftShadow.height || '0').toFixed(2);
      this.relativeHeight = JSON.parse(
        droneShadow.relativeHeight != undefined ? droneShadow.relativeHeight : aircraftShadow.elevation || '0'
      ).toFixed(2);
      this.vspeed = JSON.parse(droneShadow.verticalSpeed != undefined ? droneShadow.verticalSpeed : aircraftShadow.verticalSpeed || '0').toFixed(2);
      this.hspeed = JSON.parse(
        droneShadow.horizontalSpeed != undefined ? droneShadow.horizontalSpeed : aircraftShadow.horizontalSpeed || '0'
      ).toFixed(2);
      this.total_flight_distance = JSON.parse(droneShadow.homeDistance || aircraftShadow.homeDistance || '0').toFixed(2);
    }
  },
  updateCameras: function (liveCapacity, deviceSn) {
    this.cameras.length = 0;
    this.droneCameras.length = 0;
    if (liveCapacity && liveCapacity.coexist_video_number_max > 0) {
      var device_list = liveCapacity.device_list;
      for (var i in device_list) {
        var cameralist = device_list[i].camera_list;
        for (var j in cameralist) {
          if (device_list[i].sn == deviceSn) {
            this.cameras.push(cameralist[j]);
          } else {
            this.droneCameras.push(cameralist[j]);
          }
        }
      }
    }
  },
  formatDroneCamera: function (i) {
    var camera = this.droneCameras[i];
    var cameraNames = {
      '165-0-7': 'DJI Dock Camera',
      '39-0-7': 'FPV',
      '176-0-0': 'FPV',
      '53-0-0': 'M30T Camera',
      '66-0-0': 'M3E Camera',
      '42-0-0': 'H20',
      '80-0-0': 'Matrice 3D 相机',
      '81-0-0': 'Matrice 3TD 相机',
      '98-0-0': 'Matrice 4D 相机',
      '99-0-0': 'Matrice 4TD 相机',
      'yd-camera': '远度镜头',
      'pz-camera': '普宙镜头',
    };
    return cameraNames[camera.camera_index] || camera.camera_index;
  },
});

const waylineEditor = ref({
  isShowEditor: false,
  data: null,
  pointsIndex: { end: 0, point: {}, tpoint: {}, focus: 0 },
  savePic: [true, false, false, false],
  drawIdx: 0,
  uploadRtPopupVisible:false,//上传弹窗
  uploadDepartment:null,//选择的上传的部门
  selectedFile:null,//选择的文件
  addnew: function () {
    if (this.isShowEditor) {
      return;
    }

    this.data = { wid: 'wid' + Math.random().toString(36).slice(-6), templateTypes: '0' };
    this.isShowEditor = true;
    mapUtil.updateView();
  },
  edit: function (wayline, index) {
    drawerRef.value.selectWayline(index, true);
  },
  save: function () {
    const zhis = this,
      isAddNew = !zhis.data.id;
    waylineApi.save(zhis.data).then((res) => {
      if (res.id) {
        Object.assign(zhis.data, res);
        if (isAddNew) {
          drawerRef.value.waylineList.unshift(zhis.data);
          drawerRef.value.waylineList.focus = 0;
        }
      }

      mapUtil.updateView();
      return res.id ? createMessage.success(res.message || '保存成功。') : createMessage.error(res.message || '保存失败！');
    });
  },
  close: function () {
    this.data = {};
    this.isShowEditor = false;
    this.pointsIndex.end = 0;

    mapUtil.updateView();
  },
  openFlightRouteUploadDialog: function () {
    // if(drawerRef.value.myDepart.length === 1) {
    //   //一个部门直接上传到这个部门
    //   this.showUpload()
    //   return
    // }
    this.uploadRtPopupVisible = true;
  },
  showUpload: function () {
    document.getElementById('filechooser').click();
  },
  readyForUpload: function () {
    console.log(this.selectedFile)
    if(this.selectedFile){
      this.upload(this.selectedFile)
    }else{
      message.error('请选择文件！')
    }
  },
  handleFileChange: function (event) {
    this.selectedFile = event
    // if(drawerRef.value.myDepart.length === 1){
      //一个部门直接上传到这个部门
      // this.upload(event)
    // }
  },
  // upload: function (evt) {
  //   let files = evt.target.files;
  //   const formData = new FormData();
  //   files.forEach((item) => {
  //     const wayLineName = item.name.split('.')[0];
  //     console.log(wayLineName);
  //     waylineApiV2
  //       .duplicateNames({ name: wayLineName, sysOrgCode: waylineEditor.value.uploadDepartment || drawerRef.value.deptFilterCriteria.departmentName })
  //       .then((res) => {
  //         if (res.length > 0) {
  //           evt.target.value = '';
  //           return message.warning('存在相同航线名称，请重命名后再上传！');
  //         } else {
  //           formData.append('file', item);
  //           console.log(formData, '1111111111111');

  //           waylineApiV2.upload(formData, waylineEditor.value.uploadDepartment || drawerRef.value.deptFilterCriteria.departmentName).then(function (msg) {
  //             evt.target.value = '';
  //             waylineEditor.value.selectedFile = null;
  //             waylineListref.value.changShowType();
  //             waylineEditor.value.uploadRtPopupVisible = false;
  //             createMessage.success(msg);
  //             waylineEditor.value.uploadDepartment = ''
  //           });
  //         }
  //       })
  //       .catch((error) => {
  //         return message.error(`验证航线名称时出错：${error.message}`);
  //       });
  //   });

  //   // waylineApi.upload(formData).then(function (msg) {
  //   //   evt.target.value = '';
  //   //   waylineListref.value.changShowType();
  //   //   createMessage.success(msg);
  //   // });
  // },
  upload: function (evt) {
    let files = evt.target.files;
    let duplicateNames = [];
    let promises = []; // 用于存储所有重名校验的 Promise
    // 第一步：收集所有文件的重名信息
    files.forEach((item) => {
      const wayLineName = item.name.split('.')[0];
      promises.push(waylineApiV2
          .duplicateNames({ name: wayLineName,sysOrgCode: waylineEditor.value.uploadDepartment || drawerRef.value.deptFilterCriteria.departmentName  })
          .then((res) => {
              if (res.length > 0) {
                duplicateNames.push(wayLineName);
              }
          })
          .catch((error) => {
              message.error(`验证航线名称时出错：${error.message}`);
          })
      );
    });

    // 第二步：等待所有重名校验请求完成
    Promise.all(promises)
      .then(() => {
        if (duplicateNames.length > 0){
          evt.target.value = '';
          console.log(duplicateNames);
          const text = duplicateNames.join("，");
          return message.warning(`${text} 存在相同航线名称，请重命名后再上传！`); // 显示重名警告消息
        } else {
          // 第三步：根据重名信息决定是否上传文件
          files.forEach((item) => {
            const wayLineName = item.name.split('.')[0];
            const formData = new FormData();
            formData.append('file', item);
            waylineApiV2.upload(formData,waylineEditor.value.uploadDepartment || drawerRef.value.deptFilterCriteria.departmentName).then(function (msg) {
                evt.target.value = ''; // 清空文件输入
                waylineEditor.value.selectedFile = null;
                waylineListref.value.changShowType(); // 更新显示类型
                createMessage.success(msg); // 显示上传成功消息
                waylineEditor.value.uploadRtPopupVisible = false;
                waylineEditor.value.uploadDepartment = ''
            }).catch((error) => {
                message.error(`上传文件时出错：${error.message}`); // 显示上传错误消息
            });

          });
        }
      })
      .catch((error) => {
          message.error(`处理文件上传时出错：${error.message}`); // 处理过程中发生错误时显示消息
      });
  },
  download: function (wayline) {
    waylineApi.get({ workspaceId: wayline.workspaceId, waylineId: wayline.id }).then(function (res) {
      window.open(res.fileUrl);
      console.log(res.fileUrl);
    });
  },
  del: function (item, index) {
    waylineApi.del({ waylineId: item.id }).then(() => {
      drawerRef.value.showList(1, 0);
    });
  },
  focusWaylinePoint: function (index) {
    this.pointsIndex.focus = index;
    this.pointsIndex.point = this.data.wpmz['wpmz/waylines.wpml'].kml.Document.Folder.Placemark[index];
    this.pointsIndex.tpoint = this.data.wpmz['wpmz/template.kml'].kml.Document.Folder.Placemark[index];
  },
  move: function (step) {
    var end = this.pointsIndex.end + step,
      focus = this.pointsIndex.focus + step,
      points = this.data.wpmz['wpmz/waylines.wpml'].kml.Document.Folder.Placemark;
    if (end >= 5 && end < points.length) {
      this.pointsIndex.end = end;
    }
    if (focus >= 0 && focus < points.length) {
      this.pointsIndex.focus += step;
      this.pointsIndex.point = points[this.pointsIndex.focus];
      this.pointsIndex.tpoint = this.data.wpmz['wpmz/template.kml'].kml.Document.Folder.Placemark[this.pointsIndex.focus];
    }
  },
  startDrawWayline: function (index) {
    const zhis = this; // 修复js语言本身的作用域bug
    zhis.drawIdx = index;
    mapUtil.maplayer.startDraw({
      type: 'billboard',
      style: {
        image: svg('' + (index + 1)),
        horizontalOrigin: mars3d.Cesium.HorizontalOrigin.CENTER,
        verticalOrigin: mars3d.Cesium.VerticalOrigin.BOTTOM,
      },
      success: function (graphic) {
        let pos = graphic.point,
          pos2 = [pos.lng, pos.lat];
        var currwayline = mapUtil.waylines[zhis.data.id || zhis.data.wid];
        var currpoints = zhis.data.wpmz['wpmz/waylines.wpml'].kml.Document.Folder.Placemark,
          currtpoints = zhis.data.wpmz['wpmz/template.kml'].kml.Document.Folder.Placemark,
          newPoint = {
            Point: {
              coordinates: pos.lng + ',' + pos.lat,
            },
            'wpml:executeHeight': '120',
            'wpml:waypointSpeed': '10',
            'wpml:waypointHeadingParam': {
              'wpml:waypointHeadingMode': 'followWayline',
              'wpml:waypointHeadingAngle': '0',
              'wpml:waypointPoiPoint': '0.000000,0.000000,0.000000',
              'wpml:waypointHeadingAngleEnable': '0',
              'wpml:waypointHeadingPoiIndex': '0',
            },
            'wpml:waypointTurnParam': {
              'wpml:waypointTurnMode': 'toPointAndStopWithDiscontinuityCurvature',
              'wpml:waypointTurnDampingDist': '0',
            },
            'wpml:useStraightLine': '1',
            'wpml:waypointGimbalHeadingParam': {
              'wpml:waypointGimbalPitchAngle': '0',
              'wpml:waypointGimbalYawAngle': '0',
            },
            'wpml:actionGroup': [],
            'wpml:isRisky': '0',
          },
          newtPoint = {
            Point: {
              coordinates: pos.lng + ',' + pos.lat,
            },
            'wpml:ellipsoidHeight': 100,
            'wpml:height': 100,
            'wpml:useGlobalHeight': 1,
            'wpml:useGlobalSpeed': 1,
            'wpml:useStraightLine': '1',
            'wpml:useGlobalHeadingParam': 1,
            'wpml:useGlobalTurnParam': 1,
            'wpml:actionGroup': [],
          };
        if (zhis.drawIdx != null) {
          currpoints.splice(zhis.drawIdx, 0, newPoint);
          currtpoints.splice(zhis.drawIdx, 0, newtPoint);
        } else {
          currpoints.push(newPoint);
          currtpoints.push(newtPoint);
        }
        currpoints.forEach((p, pIdx) => {
          p['wpml:index'] = pIdx;
        });
        currtpoints.forEach((p, pIdx) => {
          p['wpml:index'] = pIdx;
        });

        mapUtil.maplayer.removeGraphic(graphic);
        mapUtil.renderWayline(zhis.data, true);

        let newPointIndex = currpoints.indexOf(newPoint),
          currEnd = waylineEditor.value.pointsIndex.end;
        waylineEditor.value.pointsIndex.end =
          newPointIndex + 1 >= currEnd ? newPointIndex + 1 : currEnd - newPointIndex <= 5 ? currEnd : newPointIndex + 5;
        waylineEditor.value.pointsIndex.focus = newPointIndex;
        waylineEditor.value.pointsIndex.point = currpoints[newPointIndex];

        waylineEditor.value.pointsIndex.tpoint = currtpoints[waylineEditor.value.pointsIndex.focus];

        zhis.startDrawWayline(index + 1);
      },
    });

    let currwayline = mapUtil.waylines[zhis.data.id || zhis.data.wid];
    if (!currwayline) {
      currwayline = mapUtil.waylines[zhis.data.id || zhis.data.wid] = [];
    }
  },
  updateDroneSubEnumValue: function () {
    var data = this.data,
      missionConfig = data.wpmz['wpmz/waylines.wpml'].kml.Document['wpml:missionConfig'];
    missionConfig['wpml:payloadInfo']['wpml:payloadEnumValue'] = data.payloadModel;
    if (data.droneModel == '67') {
      missionConfig['wpml:payloadInfo']['wpml:droneSubEnumValue'] = data.payloadModel < 100 ? data.payloadModel - 52 : 0;
    } else if (data.droneModel == '77') {
      missionConfig['wpml:payloadInfo']['wpml:droneSubEnumValue'] = data.payloadModel < 100 ? data.payloadModel - 66 : 0;
    } else {
      missionConfig['wpml:payloadInfo']['wpml:droneSubEnumValue'] = 0;
    }
  },
  resetWaylineHeight: function (e) {
    if (e.currentTarget.value == 1) {
      this.pointsIndex.tpoint['wpml:height'] = '';
      this.pointsIndex.point['wpml:executeHeight'] = '';
      this.pointsIndex.tpoint['wpml:ellipsoidHeight'] = '';
    } else {
      this.pointsIndex.tpoint['wpml:height'] = '100';
      this.pointsIndex.point['wpml:executeHeight'] = '120';
      this.pointsIndex.tpoint['wpml:ellipsoidHeight'] = '100';
    }
  },
  resetWaylineFinishAction: function (e) {
    var data = this.data,
      missionConfig = data.wpmz['wpmz/waylines.wpml'].kml.Document['wpml:missionConfig'];
    missionConfig['wpml:finishAction'] = e.currentTarget.value;
  },
  resetWaylineExecuteLostAction: function (e) {
    var data = this.data,
      tmissionConfig = data.wpmz['wpmz/template.kml'].kml.Document['wpml:missionConfig'],
      missionConfig = data.wpmz['wpmz/waylines.wpml'].kml.Document['wpml:missionConfig'];
    missionConfig['wpml:exitOnRCLost'] = tmissionConfig['wpml:exitOnRCLost'];
    if (e.currentTarget.value == 'goContinue') {
      delete tmissionConfig['wpml:executeRCLostAction'];
      delete missionConfig['wpml:executeRCLostAction'];
    } else {
      tmissionConfig['wpml:executeRCLostAction'] = 'goBack';
      missionConfig['wpml:executeRCLostAction'] = 'goBack';
    }
  },
  updateImageFormat: function () {
    var imgOps = this.savePic[0] ? 'wide' : '';
    imgOps += this.savePic[1] ? ',zoom' : '';
    imgOps += this.savePic[2] ? ',ir' : '';
    imgOps += this.savePic[3] ? ',visable' : '';
    this.data.wpmz['wpmz/template.kml'].kml.Document.Folder['wpml:payloadParam']['wpml:imageFormat'] = imgOps;
  },
  formatDuration: function () {
    var duration = this.data.wpmz['wpmz/waylines.wpml'].kml.Document.Folder['wpml:duration'],
      minute = duration / 60,
      second = duration % 60;
    var durationStr = '';
    if (minute > 1) {
      durationStr += parseInt(minute) + ' min ';
    }
    if (second > 0) {
      durationStr += parseInt(second) + ' s';
    }
    return durationStr || '-';
  },
  formatDistance: function () {
    var distance = this.data.wpmz['wpmz/waylines.wpml'].kml.Document.Folder['wpml:distance'];
    return distance > 1000 ? (distance / 1000).toFixed(2) + ' km' : parseInt(distance) + ' m';
  },
});

let markerinfoElementId = ref('');
let startDrawMarkerShow = ref(true);

const markerEditor = ref({
  createType: -1,
  isDrawing: false,
  isEditing: false,
  markerinfo: null,
  updMarkerTimer: null,
  markerColors: { '#2D8CF0': '#2D8CF0', '#19BE6B': '#19BE6B', '#FFBB00': '#FFBB00', '#E23C39': '#E23C39', '#212121': '#212121' },
  startDrawMarker: function (type) {
    startDrawMarkerShow.value = false;
    const zhis = this;
    type != null && (zhis.createType = zhis.createType == type ? -1 : type);
    if (zhis.createType == -1) {
      startDrawMarkerShow.value = true;
      zhis.isDrawing = false;
      return mapUtil.maplayer.stopDraw();
    }

    let folderIdx = drawerRef.value.markerList.focus;
    let folder = drawerRef.value.markerList[folderIdx];
    if (folder && folder.isLock != 0) {
      zhis.isDrawing = false;
      zhis.createType = -1;
      return createMessage.error('文件夹已锁定，不允许新增标记。');
    }

    function addNewMarker(info, amarker) {
      zhis.markerinfo = info;
      markerApi.add({...info,sysOrgCode :drawerRef.value.deptFilterCriteria.departmentName}).then((res) => {
        if (!res.id) {
          return createMessage.error('标记成功，但是保存失败！');
        }

        Object.assign(info, res);

        var markerList = drawerRef.value.markerList;
        markerList[markerList.focus].elements.push(info);

        // 将 res 添加到 mapUtil.markers 中
        if (res.elementId) {
          mapUtil.markers[res.elementId] = res;
        } else {
          // 如果 elementId 不存在，使用其他逻辑，生成一个默认的 key
          const defaultKey = `marker-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
          mapUtil.markers[defaultKey] = res;
        }
        zhis.bindMarkerInfo(info, amarker, true);
      });
    }

    zhis.isDrawing = true;
    if (zhis.createType === 0) {
      mapUtil.maplayer.startDraw({
        type: 'billboard',
        style: {
          image: svg('点标记'),
          color: '#2D8CF0',
          horizontalOrigin: mars3d.Cesium.HorizontalOrigin.CENTER,
          verticalOrigin: mars3d.Cesium.VerticalOrigin.BOTTOM,
          clampToGround: true,
          scale: 1,
          highlight: {
            scale: 1.1,
            // outline: true,
            // outlineWidth: 3,
            // outlineColor:'#ffffff',
            label: {
              color: '#010101',
              outline: false,
            },
          },
          label: {
            text: '新建点' + (folder.elements.length == 0 ? '' : folder.elements.length + 1),
            font_size: 15,
            color: '#010101',
            outline: true,
            outlineColor: '#ffffff',
            outlineWidth: 5,
            distanceDisplayCondition: true,
            distanceDisplayCondition_far: 500000,
            distanceDisplayCondition_near: 0,
          },
        },
        success: function (graphic) {
          let pos = graphic.point,
            info = {
              groupId: folder.groupId,
              elementName: '新建点' + (folder.elements.length == 0 ? '' : folder.elements.length + 1),
              display: '1',
              resource: {
                type: type,
                content: { properties: { color: '#2D8CF0', clampToGround: false }, geometry: { type: 'Point', coordinates: [pos.lng, pos.lat] } },
              },
            };
          addNewMarker(info, graphic);
          zhis.bindEvent(info, graphic);
          zhis.startDrawMarker(-1);
        },
      });
    } else if (zhis.createType === 1) {
      mapUtil.maplayer.startDraw({
        type: 'polyline',
        style: {
          clampToGround: true,
          color: '#3388ff',
          width: 5,
          outline: true,
          outlineWidth: 3,
          outlineColor: '#3388ff',
          highlight: {
            outline: true,
            outlineWidth: 3,
            outlineColor: '#ffffff',
            label: {
              color: '#010101',
              outline: false,
            },
          },
          label: {
            text: '新建线' + (folder.elements.length == 0 ? '' : folder.elements.length + 1),
            font_size: 15,
            color: '#010101',
            outline: true,
            outlineColor: '#ffffff',
            outlineWidth: 5,
            distanceDisplayCondition: true,
            distanceDisplayCondition_far: 500000,
            distanceDisplayCondition_near: 0,
          },
        },
        success: function (graphic) {
          let pos = graphic.point,
            info = {
              groupId: folder.groupId,
              elementName: '新建线' + (folder.elements.length == 0 ? '' : folder.elements.length + 1),
              display: '1',
              resource: {
                type: type,
                content: {
                  properties: { color: '#2D8CF0', clampToGround: false },
                  geometry: { type: 'LineString', coordinates: graphic.points.map((obj) => [obj.lng, obj.lat]) },
                },
              },
            };
          addNewMarker(info, graphic);
          zhis.bindEvent(info, graphic);
          zhis.startDrawMarker(-1);
        },
      });
    } else if (zhis.createType === 2) {
      mapUtil.maplayer.startDraw({
        type: 'polygon',
        style: {
          clampToGround: true,
          color: '#3388ff',
          opacity: 0.5,
          outline: true,
          outlineColor: '#3388ff',
          outlineWidth: 3.0,
          highlight: {
            outline: true,
            outlineWidth: 3,
            outlineColor: '#ffffff',
            label: {
              color: '#010101',
              outline: false,
            },
          },
          label: {
            text: '新建面' + (folder.elements.length == 0 ? '' : folder.elements.length + 1),
            font_size: 15,
            color: '#010101',
            outline: true,
            outlineColor: '#ffffff',
            outlineWidth: 5,
            distanceDisplayCondition: true,
            distanceDisplayCondition_far: 500000,
            distanceDisplayCondition_near: 0,
          },
        },
        success: function (graphic) {
          let pos = graphic.point,
            info = {
              groupId: folder.groupId,
              elementName: '新建面' + (folder.elements.length == 0 ? '' : folder.elements.length + 1),
              display: '1',
              resource: {
                type: type,
                content: {
                  properties: { color: '#2D8CF0', clampToGround: false },
                  geometry: { type: 'Polygon', coordinates: [graphic.points.map((obj) => [obj.lng, obj.lat])] },
                },
              },
            };
          addNewMarker(info, graphic);
          zhis.bindEvent(info, graphic);
          zhis.startDrawMarker(-1);
        },
      });
    }
  },
  bindEvent: function (info, amarker) {
    let folderIdx = drawerRef.value.markerList.focus;
    let folder = drawerRef.value.markerList[folderIdx];
    const zhis = this;
    if (amarker) {
      let lastPosition;
      info.resource.type == 0 &&
        amarker
          .off([mars3d.EventType.leftDown, mars3d.EventType.updatePosition, mars3d.EventType.leftUp])
          .on(mars3d.EventType.leftDown, function (e) {
            if (folder.isLock != 0) return;
            lastPosition = deepClone(e.target.point);
            console.log('leftDown：' + e.target.point);
            mapUtil.maplayer.startEditing(amarker);
          })
          .on(mars3d.EventType.updatePosition, function (e) {
            zhis.updMarkerTimer && clearTimeout(zhis.updMarkerTimer);

            //console.log('旧标记位置更新: ' + [lastPosition[0], lastPosition[1]]);
            console.log('新标记位置更新: ' + e.target.point);
            if (lastPosition?.lng === e.target.point[0] && lastPosition?.lat === e.target.point[1]) {
              return;
            }

            zhis.updMarkerTimer = setTimeout(() => {
              let newMarkerInfo = deepClone(info);
              newMarkerInfo.resource.content.geometry.coordinates = [amarker.point.lng, amarker.point.lat];
              zhis.updMarker(newMarkerInfo, info, null, () => {
                createMessage.error('标记位置更新失败！');
              });
            }, 200);
          })
          .on(mars3d.EventType.leftUp, function (e) {
            mapUtil.maplayer.stopEditing();
          });
      info.resource.type !== 0 &&
        amarker
          .off([mars3d.EventType.click, mars3d.EventType.dblClick, mars3d.EventType.rightClick, mars3d.EventType.editMovePoint])
          .on(mars3d.EventType.click, function (e) {
            // e.stopPropagation();
            setTimeout(function () {
              if (amarker.isEditing) {
                return;
              }

              console.log('线面 Click，显示信息框');

              if (!startDrawMarkerShow.value) {
                amarker.openPopup();
              }
            }, 200);
          })
          .on(mars3d.EventType.dblClick, function (e) {
            if (folder.isLock != 0) return;
            if (amarker.isEditing) {
              return;
            }

            console.log('线面 dblClick，启用编辑');

            e.stopPropagation();
            amarker.closePopup();
            if (startDrawMarkerShow.value) {
              mapUtil.maplayer.startEditing(amarker);
            }
          })
          .on(mars3d.EventType.rightClick, function (e) {
            if (amarker.isEditing) {
              mapUtil.maplayer.stopDraw();
              mapUtil.maplayer.stopEditing();
            }
          })
          .on(mars3d.EventType.editMovePoint, function (e) {
            zhis.updMarkerTimer && clearTimeout(zhis.updMarkerTimer);
            zhis.updMarkerTimer = setTimeout(() => {
              let newMarkerInfo = deepClone(info);
              newMarkerInfo.resource.content.geometry.coordinates =
                info.resource.type == 2 ? [amarker.points.map((obj) => [obj.lng, obj.lat])] : amarker.points.map((obj) => [obj.lng, obj.lat]);
              zhis.updMarker(newMarkerInfo, info, null, () => {
                createMessage.error('标记位置更新失败！');
              });
            }, 200);
          });
      // .on(mars3d.EventType.mouseOver, function (e) {
      //    amarker.style.outlineColor = '#ffffff';
      //    amarker.style.label.outline = false;
      // }).on(mars3d.EventType.mouseOut, function (e) {
      //    amarker.style.outlineColor = info.resource.content.properties.color;
      //    amarker.style.label.outline = true;
      // })
      // .on(mars3d.EventType.dblclick, function(){
      //     if(markerEditor.value.isDrawing){
      //         return;
      //     }

      //     markerEditor.value.bindMarkerInfo(info, amarker);
      // })
    }
  },
  del: function (elementId) {
    var zhis = this;
    zhis.closeMarkerInfo();
    markerApi.del(elementId).then(function () {
      // if (zhis.polyEditor && zhis.polyEditor.getTarget()) {
      //   zhis.polyEditor.close();
      //   mapUtil.map.remove(zhis.polyEditor.getTarget());
      // }
      delete mapUtil.markers[elementId];
      drawerRef.value.showList(2);
    });
  },
  delSelect: function () {
    let folderIdx = drawerRef.value.markerList.focus;
    let folder = drawerRef.value.markerList[folderIdx];
    let sel = folder.sel.map((idx) => folder.elements[idx].elementId);

    if (!sel || sel.length == 0) {
      return;
    }

    if (sel.length == 1) {
      return this.del(sel[0]);
    }

    var zhis = this;
    zhis.closeMarkerInfo();

    markerApi.delBatch({ elementIds: sel }).then(function () {
      sel.forEach((elementId) => {
        delete mapUtil.markers[elementId];
      });
      drawerRef.value.showList(2);
    });

    // async function delBatch(){
    //     for(let i=0;i<sel.length;i++){
    //         await markerApi.del(sel[i]).then(function(){
    //             if(zhis.polyEditor && zhis.polyEditor.getTarget()){
    //                 zhis.polyEditor.close();
    //                 mapUtil.map.remove(zhis.polyEditor.getTarget())
    //             }
    //         });
    //     }
    //     drawerRef.value.showList(2);
    // }
    // delBatch();
  },
  delFolder: function (folder, folderIdx) {
    var zhis = this,
      markerGroups = drawerRef.value.markerList;
    zhis.closeMarkerInfo();
    markerApi.delFolder(folder.groupId).then(function () {
      folder.elements.forEach((element) => {
        delete mapUtil.markers[element.elementId];
      });
      drawerRef.value.showList(2);
    });
  },
  emptyFolder: function (folder) {
    var zhis = this;
    zhis.closeMarkerInfo();
    markerApi.emptyFolder(folder.groupId).then(function () {
      folder.elements.forEach((element) => {
        delete mapUtil.markers[element.elementId];
      });
      drawerRef.value.showList(2);
    });
  },
  moveTo: function (folder, folderIdx) {
    let focusFolderIdx = drawerRef.value.markerList.focus;
    let focusFolder = drawerRef.value.markerList[focusFolderIdx];
    let focusMarker = focusFolder.elements[focusFolder.focus];
    let dragMarkerInfo = Object.assign({}, focusMarker, { groupId: folder.groupId });

    markerApi.move(dragMarkerInfo).then(function (res) {
      if (res.id) {
        Object.assign(focusMarker, res);
        folder.elements.push(focusFolder.elements.splice(focusFolder.focus, 1)[0]);
      }
    });
  },
  bindMarkerInfo: function (markerinfo, marker, showPop) {
    var zhis = this;
    zhis.markerinfo = markerinfo;
    // zhis.isStartDraw && mapUtil.maplayer.stopEditing();

    var lnglat = markerinfo.resource.type == 0 ? marker.point : marker.points[0];
    let markerList = drawerRef.value.markerList,
      folder,
      folderIndex;
    for (var i = 0; i < markerList.length; i++) {
      if (markerList[i].groupId == markerinfo.groupId) {
        folderIndex = i;
        folder = markerList[i];
        break;
      }
    }

    function getMarkerInfoPop() {
      return (
        `<div id="markerinfo" class="Col">
                <div>标记信息</div>
                <div style="display: flex;">
                    <img src="` +
        svg('标记类型' + markerinfo.resource.type) +
        `">
                    <input id="markerinfo-elementname" type="text" value="" ` +
        (folder.isLock == '1' ? 'readOnly' : '') +
        `>
                    <img id="markerinfo-del" src="` +
        svg('删除3') +
        `">
                </div>
                <div id="markerinfo-color" class="Row"><span>颜色</span>
                    <div class="Color Row"><span style="background: #2D8CF0;"></span></div>
                    <div class="Color Row"><span style="background: #19BE6B;"></span></div>
                    <div class="Color Row"><span style="background: #FFBB00;"></span></div>
                    <div class="Color Row"><span style="background: #E23C39;"></span></div>
                    <div class="Color Row"><span style="background: #212121;"></span></div>
                </div>
                <div id="markerinfo-detail" class="Col">
                    <div class="Row ICenter"><i class="Row Flex"></i>&nbsp;&nbsp;测量数据&nbsp;&nbsp;<i class="Row Flex"></i></div>
                    <div id="markerinfo-lnglat" class="Row Hidden ICenter">
                      <div class="lngBox">
                        <span>经度(E)</span><input id="markerinfo-lng" type="number" value="" ` +
        (folder.isLock == '1' ? 'readOnly' : '') +
        `>°
                      </div>
                      <div class="lngBox">
                        <span>纬度(N)</span><input id="markerinfo-lat" type="number" value="" ` +
        (folder.isLock == '1' ? 'readOnly' : '') +
        `>°
                      </div>


                    </div>
                    <div class="Row horizonta Hidden"><span>水平距离</span><span class="horizontalDistance Row Flex"></span></div>
                    <div class="Row horizontaTwo Hidden"><span>直线距离</span><span class="straightLine Row Flex"></span></div>

                    <div class="Row areaDom Hidden"><span>水平面积</span><span class="area Row Flex"></span></div>
                    <div class="Row"><span>创建人</span><span class="Creator Row Flex"></span></div>
                    <div class="Row"><span>创建时间</span><span class="CreateTime Row Flex"></span></div>
                </div>
                <style>
                    .Row{ display: flex; flex-direction: row; }
                    .roW{ display: flex; flex-direction: row; justify-content:flex-end; }
                    .Col{ display: flex; flex-direction: column; }
                    .coL{ display: flex; flex-direction: column; justify-content: flex-end; }
                    .Row.Center,.roW.Center,.Col.Center,.col.Center{ justify-content:center !important; }
                    .Row.ICenter,.roW.ICenter,.Col.ICenter,.col.ICenter{ align-items:center !important; }
                    .Row.ITop,.roW.ITop,.Col.ILeft,.col.ILeft{ align-items:flex-start !important; }

                    .Flex{ flex: 1; }
                    .Hidden{display: none !important;}

                    .mars3d-popup-content{margin: 0}
                    #markerinfo{background: rgba(0,0,0,0.7);border-radius: 4px;color:white;padding: 10px 16px;}
                    #markerinfo>div>*:first-child{min-width: 2em;margin-right: 8px;}
                    #markerinfo .Color>span{display:flex;width: 21px;height: 21px;margin: 0 16px 0 0;border-radius: 16px;}
                    #markerinfo .Color.Focus>span{border: 3px solid #FFFFFF;}
                    #markerinfo>div{margin:8px 0;}
                    #markerinfo input{width: 175px;height: 32px;border-radius: 2px;border: 1px solid rgba(255,255,255,0.5);background: transparent;}

                    #markerinfo-lnglat>input{width: 80px;}
                    #markerinfo-detail>div{margin:6px 0;}
                    #markerinfo-detail>div>i{height:1px;background:#979797}
                    #markerinfo-detail>div>span:first-child{width:70px;font-size:12px}
                    #markerinfo-detail>#markerinfo-lnglat{display: flex;flex-direction: column;gap: 8px;margin-left: -4px;}
                    #markerinfo-detail>#markerinfo-lnglat>.lngBox>span{margin-right: 8px;}
                </style>
            </div>`
      );
    }

    marker.off(mars3d.EventType.popupOpen).on(mars3d.EventType.popupOpen, function (event) {
      const container = event.container; // popup对应的DOM

      let ajaxTimer;

      const btnDel = container.querySelector('#markerinfo-del');
      btnDel &&
        btnDel.addEventListener('click', (e) => {
          ajaxTimer && clearTimeout(ajaxTimer);
          ajaxTimer = setTimeout(function () {
            // 防止删除时，调用后端两次接口时的报错
            if (markerinfoElementId.value == markerinfo.elementId) {
              return;
            }
            zhis.del(markerinfo.elementId);
            markerinfoElementId.value = markerinfo.elementId;
            markerinfo.hasDelete = true;
            marker.remove(true);
          }, 200);
          marker.closePopup();
        });

      const elementNameInput = container.querySelector('#markerinfo-elementname');
      if (elementNameInput) {
        elementNameInput.value = markerinfo.elementName;
        elementNameInput.addEventListener('change', (e) => {
          ajaxTimer && clearTimeout(ajaxTimer);
          ajaxTimer = setTimeout(function () {
            let newMarkerInfo = deepClone(markerinfo);
            newMarkerInfo.elementName = elementNameInput.value;
            // 解决 wininfo 名称修改，列表不修改的bug
            let folderIdx = drawerRef.value.markerList.focus;
            const idx = drawerRef.value.markerList[folderIdx].elements.findIndex((item) => item.id == markerinfo.id);
            drawerRef.value.markerList[folderIdx].elements[idx].elementName = newMarkerInfo.elementName;
            if (markerinfoElementId.value == markerinfo.elementId) {
              return;
            }
            zhis.updMarker(newMarkerInfo, markerinfo, () => {
              markerinfoElementId.value = '';
              //  解决 wininfo 名称修改，地图点下面文字不修改的bug
              marker.setStyle({ label: { text: newMarkerInfo.elementName } });
            });
            markerinfoElementId.value = markerinfo.elementId;
          }, 200);
        });
      }

      const btnColors = container.querySelectorAll('#markerinfo-color>.Color');
      const colors = ['#2D8CF0', '#19BE6B', '#FFBB00', '#E23C39', '#212121'],
        currFocus = colors.indexOf(markerinfo.resource.content.properties.color);
      btnColors &&
        btnColors.forEach((btn, btnIndx) => {
          if (btnIndx == currFocus) {
            btn.classList.add('Focus');
          } else {
            btn.classList.remove('Focus');
          }
          btn.addEventListener('click', (e) => {
            ajaxTimer && clearTimeout(ajaxTimer);
            ajaxTimer = setTimeout(function () {
              let newMarkerInfo = deepClone(markerinfo);
              let colorVal = (newMarkerInfo.resource.content.properties.color = colors[btnIndx]);
              if (markerinfoElementId.value == markerinfo.elementId) {
                return;
              }
              zhis.updMarker(newMarkerInfo, markerinfo, function () {
                markerinfoElementId.value = '';
                const btnColorFocus = container.querySelector('#markerinfo-color>.Color.Focus');
                btnColorFocus && btnColorFocus.classList.remove('Focus');
                btn.classList.add('Focus');
                // const colorNodes = btn.parentNode.children;
                // const idxColorFocus = Array.prototype.indexOf.call(colorNodes,btn)-1;
                marker.setStyle({ color: markerEditor.value.markerColors[colorVal], outlineColor: markerEditor.value.markerColors[colorVal] });
              });
              markerinfoElementId.value = markerinfo.elementId;
            }, 200);
          });
        });

      const lnglatContent = container.querySelector('#markerinfo-lnglat');
      if (markerinfo.resource.type == 0) {
        lnglatContent.classList.remove('Hidden');
        const elementLngInput = container.querySelector('#markerinfo-lng');
        if (elementLngInput) {
          elementLngInput.value = marker.point.lng;
          elementLngInput.addEventListener('change', (e) => {
            ajaxTimer && clearTimeout(ajaxTimer);
            ajaxTimer = setTimeout(function () {
              let lng = parseFloat(elementLngInput.value);
              let newMarkerInfo = deepClone(markerinfo);
              if (lng % 1 === 0) {
                elementLngInput.value = marker.point.lng;
                return message.error('请输入正确的经纬度格式！');
              }
              newMarkerInfo.resource.content.geometry.coordinates[0] = lng;
              if (markerinfoElementId.value == markerinfo.elementId) {
                return;
              }
              zhis.updMarker(newMarkerInfo, markerinfo, () => {
                markerinfoElementId.value = '';
                marker.setCallbackPosition([lng, marker.point.lat]);
              });
              markerinfoElementId.value = markerinfo.elementId;
            }, 200);
          });
        }

        const elementLatInput = container.querySelector('#markerinfo-lat');
        if (elementLatInput) {
          elementLatInput.value = marker.point.lat;
          elementLatInput.addEventListener('change', (e) => {
            ajaxTimer && clearTimeout(ajaxTimer);
            ajaxTimer = setTimeout(function () {
              let lat = parseFloat(elementLatInput.value);
              let newMarkerInfo = deepClone(markerinfo);
              if (lat % 1 === 0) {
                elementLatInput.value = marker.point.lat;
                return message.error('请输入正确的经纬度格式！');
              }
              newMarkerInfo.resource.content.geometry.coordinates[1] = lat;
              if (markerinfoElementId.value == markerinfo.elementId) {
                return;
              }
              zhis.updMarker(newMarkerInfo, markerinfo, () => {
                markerinfoElementId.value = '';
                marker.setCallbackPosition([marker.point.lng, lat]);
              });
              markerinfoElementId.value = markerinfo.elementId;
            }, 200);
          });
        }
      }
      if (markerinfo.resource.type == 1) {
        container.querySelector('.horizonta').classList.remove('Hidden');
        container.querySelector('.horizontaTwo').classList.remove('Hidden');
        container.querySelector('#markerinfo-detail>div>.horizontalDistance').innerText =
          mars3d.MeasureUtil.getDistance(markerinfo.resource.content.geometry.coordinates).toFixed(2) + 'm';
        container.querySelector('#markerinfo-detail>div>.straightLine').innerText =
          mars3d.MeasureUtil.getSurfaceDistance(markerinfo.resource.content.geometry.coordinates).toFixed(2) + 'm';
      }

      if (markerinfo.resource.type == 2) {
        container.querySelector('.areaDom').classList.remove('Hidden');
        container.querySelector('#markerinfo-detail>div>.area').innerText =
          mars3d.MeasureUtil.getArea(markerinfo.resource.content.geometry.coordinates[0]).toFixed(2) + '㎡';

        console.log(mars3d.MeasureUtil.getArea(markerinfo.resource.content.geometry.coordinates[0]));
      }
      container.querySelector('#markerinfo-detail>div>.Creator').innerText = markerinfo.resource.user_name || '';
      container.querySelector('#markerinfo-detail>div>.CreateTime').innerText = markerinfo.createTime || '';

      console.log(markerinfo, 'markerinfo');

      if (markerList.focus >= 0 && markerList.focus !== folderIndex) {
        let lastFolder = markerList[markerList.focus];
        lastFolder.expand = false;
        lastFolder.focus = -1;
      }

      markerList.focus = folderIndex;
      folder.expand = true;
      var elements = folder.elements || [];
      for (var j = 0; j < elements.length; j++) {
        if (elements[j].elementId == markerinfo.elementId) {
          folder.focus = j;
          break;
        }
      }
    });

    marker.unbindPopup().bindPopup(getMarkerInfoPop(), { offsetY: -40 });
    if (showPop) {
      marker.openPopup();
      // 获取相机的当前位置
      const cameraPosition = mapUtil.map.scene.camera.position;

      // 将目标点转换为世界坐标（高度设为 0）
      const targetCartesian = Cesium.Cartesian3.fromDegrees(lnglat.lng, lnglat.lat, lnglat.alt);

      // 计算相机到目标点的距离
      const distance = Cesium.Cartesian3.distance(cameraPosition, targetCartesian);

      console.log('当前相机到目标点的距离:', distance, '米');
      mapUtil.map.flyToPoint(lnglat, { radius: distance });
    }
  },
  closeMarkerInfo: function () {
    this.infoWindow && this.infoWindow.close();
  },
  distribute: function (folder) {
    markerApi.distribute(folder.groupId).then((res) => {
      res.groupId && Object.assign(folder, res);
      createMessage.success('分发成功。');
    });
  },
  getCancelDistribute: function (folder) {
    if (folder.groupType != 2) {
      markerApi.cancelDistribute(folder.groupId).then((res) => {
        res.groupId && Object.assign(folder, res);
        createMessage.success('取消成功。');
      });
    }
  },
  lock: function (folder) {
    // folder.isLock = (folder.isLock!='1'?'1':'0')
    if (folder.groupType == 2) {
      createMessage.error('共享文件夹不可锁定!');
      return;
    }
    console.log(folder, 'folder');
    markerApi.lock(folder.groupId).then((res) => {
      res.groupId && Object.assign(folder, { isLock: res.isLock });
      createMessage.success(res.message || '文件夹已锁定。');
    });
  },
  unlock: function (folder) {
    // folder.isLock = (folder.isLock!='1'?'1':'0')
    console.log(folder, 'folder2');
    markerApi.unlock(folder.groupId).then((res) => {
      res.groupId && Object.assign(folder, { isLock: res.isLock });
      createMessage.success(res.message || '文件夹已解锁。');
    });
  },
  updMarker: function (newMarkerinfo, markerinfo, successCallback, failCallback) {
    markerApi.upd({...newMarkerinfo,sysOrgCode: drawerRef.value.deptFilterCriteria.departmentName}).then(function (res) {
      if (res.id) {
        Object.assign(markerinfo, res);
        if (markerinfo.display == 0) {
          delete mapUtil.markers[markerinfo.elementId];
        } else {
          mapUtil.markers[markerinfo.elementId] = markerinfo;
        }
        successCallback && successCallback();
      } else if (failCallback) {
        failCallback();
      } else {
        createMessage.error('标记更新失败！');
      }
    });
  },
  display: function (markerinfo) {
    let newMarkerInfo = deepClone(markerinfo);
    newMarkerInfo.display = markerinfo.display != '1' ? '1' : '0';
    // markerApi.elementOperate(markerinfo.elementId,markerinfo.display)
    this.updMarker(newMarkerInfo, markerinfo);
  },
  addFolder: function () {
    var markerList = drawerRef.value.markerList;
    var nextNo = (markerList.nextFolderNo = (markerList.nextFolderNo || markerList.length) + 1);
    markerApi.addFolder({ groupName: '新建文件夹' + nextNo, sysOrgCode: drawerRef.value.deptFilterCriteria.departmentName }).then((res) => {
      if (!res.id) {
        return createMessage.error(res);
      }
      res.showEditor = true;
      res.sel = [];
      markerList.push(res);
      markerList.focus = markerList.length - 1;
    });
  },
  updFolder: function (folder) {
    markerApi.updFolder({...folder,sysOrgCode: drawerRef.value.deptFilterCriteria.departmentName}).then((res) => {
      res.groupId && Object.assign(folder, res);
      folder.showEditor = false;
    });
  },
  toggleFolder: function (folder, index, keepExpand) {
    var markerList = drawerRef.value.markerList;
    if (markerList.focus >= 0 && markerList.focus !== index) {
      let lastFolder = markerList[markerList.focus];
      lastFolder.expand = false;
      lastFolder.focus = -1;
    }
    markerList.focus = index;
    if (!keepExpand) {
      folder.expand = !folder.expand;
    }
  },
});

let hierarchyDCount = ref(1000);
let hierarchyXCount = ref(100);
let hierarchyMCount = ref(0);

const mapUtil = {
  map: null,
  mapbacklayer: null,
  maplayer: null,
  devices: {},
  waylines: {},
  markers: {},
  historyTrack: {},
  VehiclePosition: [],
  maptype3d2d: ref('0'),
  updateView: function (islx = false) {
    const zhis = this;
    zhis.maplayer.stopDraw();
    zhis.maplayer.stopEditing();
    zhis.historyTrack = {}; // 清空历史轨迹
    zhis.maplayer.clear(true); // 清空地图

    console.log('drawerRef', drawerRef.value);
    if (drawerRef.value.showType == 0 || islx) {
      let deviceList = drawerRef.value.deviceList;
      deviceList.forEach((device) => {
        setTimeout(() => {
          device && zhis.showDevice(device, drawerRef.value);
        }, 100);
      });
      // zhis.showResourses();
    } else if (drawerRef.value.showType == 1 && !islx) {
      if (waylineEditor.value.isShowEditor) {
        zhis.showWayline(waylineEditor.value.data, true, true);
      } else {
        let waylineList = drawerRef.value.waylineList,
          wayline = waylineList[waylineList.focus];
        wayline && zhis.showWayline(wayline, false, true);
      }
    } else if (drawerRef.value.showType == 2 && !islx) {
      var markerList = drawerRef.value.markerList,
        folder = markerList[markerList.focus];
      zhis.showMarkers(folder.elements[folder.focus]);
    }
  },
  getSampledPositionProperty: function (points) {
    const property = new Cesium.SampledPositionProperty();
    property.forwardExtrapolationType = mars3d.Cesium.ExtrapolationType.HOLD;

    const start = mapUtil.map.clock.currentTime;
    const positions = mars3d.LngLatArray.toCartesians(points);
    for (let i = 0; i < positions.length; i++) {
      const time = mars3d.Cesium.JulianDate.addSeconds(start, i * 20, new Cesium.JulianDate());
      const position = positions[i];
      property.addSample(time, position);
    }
    return property;
  },
  showDevice: async function (device, drawerRef) {
    let zhis = this,
      mapdevice = (this.devices[device.id] = []);
    let alternate_land_point = drawerRef.aerodromeList.dockShadow?.dockState?.alternate_land_point;
    let position1 = [
      device.longitude || alternate_land_point.longitude,
      device.latitude || alternate_land_point.latitude,
      Number(device.dockStateOsd?.height.toFixed(6)) || 0,
    ];
    // if (device.longitude != 0 && device.latitude != 0) {
    const graphic = new mars3d.graphic.BillboardEntity({
      position: position1,
      style: {
        // image: device.deviceType != '2' ? png('deviceModel' + device.deviceModel) : png('地图设备' + device.deviceType),
        image:
          device.deviceType == '2' && device.deviceModel == 'DJI RC Plus2'
            ? png('地图设备' + device.deviceType)
            : device.deviceType == '2'
            ? png('地图设备2-22')
            : png('deviceModel' + device.deviceModel),
        label: {
          text: device.deviceName,
          font_size: 15,
          color: '#010101',
          outline: true,
          outlineColor: '#ffffff',
          outlineWidth: 5,
          distanceDisplayCondition: true,
          distanceDisplayCondition_far: 500000,
          distanceDisplayCondition_near: 0,
        },
        width: 80,
        height: 80,
        horizontalOrigin: mars3d.Cesium.HorizontalOrigin.CENTER,
        verticalOrigin: mars3d.Cesium.VerticalOrigin.BOTTOM,
        clampToGround: device.deviceType == '4' || position1[2] === 0 ? true : false,
      },
      attr: { remark: device.deviceName, data: device },
      // flyTo: true,
      show: device.uavFlag ? false : true,
    });
    mapUtil.maplayer.addGraphic(graphic); // 还可以另外一种写法: graphic.addTo(graphicLayer)

    mapdevice.push(graphic);
    // 机场、摄像头图标增加点击事件，用来定位到设备图标位置，并选中列表中的对应项
    graphic.on('click', (event) => {
      console.log('点击了机场图标', event);
      console.log('列表索引', drawerRef.value.deviceList);
      drawerRef.value.deviceList.forEach((item, index) => {
        if (item.id == event.target.options?.attr.data.id) {
          drawerRef.value.selectDevice(item, index);
        }
      });
    });
    // }

    if (device.uavFlag == '0' || device.uavFlag) {
      let subdevice = device.deviceList[0];
      if (subdevice) {
        let position2 = [parseFloat(subdevice.longitude + '') || device.longitude, parseFloat(subdevice.latitude + '') || device.latitude, 100];

        const graphic = new mars3d.graphic.BillboardEntity({
          position: position2,
          style: {
            image: svg('DJISD'),
            width: 60,
            height: 60,
            horizontalOrigin: mars3d.Cesium.HorizontalOrigin.CENTER,
            verticalOrigin: mars3d.Cesium.VerticalOrigin.BOTTOM,
            clampToGround: false,
            zIndex: 12,
            label: {
              text: subdevice.deviceName,
              font_size: 15,
              color: '#010101',
              outline: true,
              outlineColor: '#ffffff',
              outlineWidth: 5,
              distanceDisplayCondition: true,
              distanceDisplayCondition_far: 500000,
              distanceDisplayCondition_near: 0,
            },
          },
          // circle:{
          //   radius:20,
          //   height:100,
          //   color:'#ffffff',
          //   opacity:0.5,
          //   clampToGround:false,
          //   distanceDisplayCondition:true,
          // },
          attr: { remark: subdevice.deviceName, name: '植保机' },
        });

        mapUtil.maplayer.addGraphic(graphic); // 还可以另外一种写法: graphic.addTo(graphicLayer)
        mapdevice.push(graphic);
      }

      //飞机在航线飞行
      mapViewApi
        .getWayLineByDeviceId({ deviceId: device.deviceId })
        .then((res) => {
          waylineApi.resolveFile(res.fileUrl, function (wpmz) {
            subdevice['wpmz'] = wpmz;

            const points = subdevice.wpmz['wpmz/waylines.wpml'].kml.Document.Folder.Placemark || [];
            let takeOffRefPoint = [0, 0, 0];
            if (subdevice.wpmz['wpmz/waylines.wpml'].kml.Document.Folder['wpml:executeHeightMode'] == 'relativeToStartPoint') {
              // console.log('11111111111111',takeOffRefPoint = subdevice.wpmz['wpmz/template.kml'].kml.Document['wpml:missionConfig']);

              takeOffRefPoint = subdevice.wpmz['wpmz/template.kml'].kml.Document['wpml:missionConfig']['wpml:takeOffRefPoint']
                ? subdevice.wpmz['wpmz/template.kml'].kml.Document['wpml:missionConfig']['wpml:takeOffRefPoint'].split(',').map(Number)
                : [0, 0, 0];
            }
            const position = [];
            // points.forEach((item) => {
            //   position.push(item.Point.coordinates.split(',').map(Number));
            // });
            points.forEach((item) => {
              const coordinates1 = item.Point.coordinates.split(',').map(Number);
              coordinates1.push(Number(item['wpml:executeHeight']) + takeOffRefPoint[2]); // 将 executeHeight 加入到坐标数组
              position.push(coordinates1);
            });
            const graphicTwo = new mars3d.graphic.PolylineEntity({
              positions: position,
              style: {
                width: 5,
                color: '#1791fc',
                // clampToGround: true,
                // clampToGround: true,
              },

              attr: { remark: '飞行航线', airRoute: true },
            });
            mapUtil.maplayer.addGraphic(graphicTwo);
            mapdevice.push(graphicTwo);
          });
        })
        .catch((err) => {
          let position2 = [[subdevice.longitude || device.longitude, subdevice.latitude || device.latitude, subdevice.height || device.height]];
          const graphicTwo = new mars3d.graphic.PolylineEntity({
            positions: position2,
            style: {
              width: 5,
              color: '#1791fc',
              // clampToGround: true,
              // clampToGround: true,
            },

            attr: { remark: '飞行航线', airRoute: false },
          });
          mapUtil.maplayer.addGraphic(graphicTwo);
          mapdevice.push(graphicTwo);
        });

      setTimeout(() => {
        mapViewApi
          .getHistoryTrack({ deviceId: device.deviceId })
          .then(async (res) => {
            await axios.get(res.trackFileUrl).then((response) => {
              mapViewApi.clearHistoryTrack({ fileId: res.trackFileId });
              let arr = [];
              arr = response.data.map((item) => [parseFloat(item.longitude), parseFloat(item.latitude), parseFloat(item.height)]);
              // console.log(arr,'arr--------------123')
              mapUtil.historyTrack[device.id] = arr;
              // console.log(mapUtil.historyTrack,'mapUtil.historyTrack--------------123')
              const graphic3 = new mars3d.graphic.PolylineEntity({
                positions: arr,
                style: {
                  width: 5,
                  color: '#44e0ad',
                  // clampToGround: true,
                  zIndex: 10,
                  // clampToGround: true,
                },
                attr: { remark: '轨迹', airRoute: true },
              });
              mapUtil.maplayer.addGraphic(graphic3);
              mapdevice.push(graphic3);
            });
          })
          .catch((err) => {
            let position2 = [[subdevice.longitude || device.longitude, subdevice.latitude || device.latitude]];
            // console.log('我是catch', position2);

            const graphic3 = new mars3d.graphic.PolylineEntity({
              positions: position2,
              style: {
                width: 5,
                color: '#44e0ad',
                // clampToGround: true,
                zIndex: 10,
                // clampToGround: true,
              },
              attr: { remark: '轨迹', airRoute: false },
            });
            mapUtil.maplayer.addGraphic(graphic3);
            mapdevice.push(graphic3);
          });
      }, 1000);
      return;
    }

    let planeIcon;
    switch (device.deviceModel) {
      case 'DJI Dock':
        planeIcon = png('m30');
        break;
      case 'DJI Dock2':
        planeIcon = png('M3D');
        break;
      case 'DJI Dock3':
        planeIcon = png('M4TD');
        break;
      default:
        planeIcon = png('地图设备0');
        break;
    }
    // 查询下面有无设备 摄像头设备不走下面逻辑
    if (device.deviceList && device.deviceList.length > 0 && device.deviceList[0].state != 18 && device.deviceType != '4') {
      let subdevice = device.deviceList[0];
      if (subdevice) {
        let position2 = [
          parseFloat(subdevice.longitude + '') || device.longitude,
          parseFloat(subdevice.latitude + '') || device.latitude,
          subdevice.deviceOsd.height || device.deviceOsd.height,
        ];
        const graphic = new mars3d.graphic.BillboardEntity({
          // position: mapUtil.getSampledPositionProperty(position2),
          // position: position2,
          position: position1,
          // position:{
          //   type: "time", // 时序动态坐标
          //   speed: 360,
          //   // list: [
          //   // //  mapUtil.VehiclePosition
          //   // mapUtil.historyTrack
          //   // ]
          //   list: mapUtil.historyTrack
          // },
          style: {
            image: planeIcon,
            width: 60,
            height: 60,
            // pixelOffsetY: position1[0] == position2[0] && position1[1] == position2[1] ? -40 : 0,
            horizontalOrigin: mars3d.Cesium.HorizontalOrigin.CENTER,
            verticalOrigin: mars3d.Cesium.VerticalOrigin.BOTTOM,
            clampToGround: false,
            zIndex: 12,
          },
          // clampToTileset: true,
          // path: {
          //   width: 3,
          //   color: '#3388ff',
          // },
          attr: { remark: subdevice.deviceName },
          // flyTo: true,
        });

        mapUtil.maplayer.addGraphic(graphic); // 还可以另外一种写法: graphic.addTo(graphicLayer)
        mapdevice.push(graphic);
        console.log(subdevice, device, 'subdevice,devicepppppppppppppppppppppppp');
        // if (subdevice.state != 18) {
        //飞机在航线飞行
        await mapViewApi
          .getWayLineByDeviceId({ deviceId: device.deviceId })
          .then((res) => {
            waylineApi.resolveFile(res.fileUrl, function (wpmz) {
              subdevice['wpmz'] = wpmz;
              if(Array.isArray(subdevice.wpmz['wpmz/waylines.wpml'].kml.Document.Folder)) {
                const points = subdevice.wpmz['wpmz/waylines.wpml'].kml.Document.Folder || [];
                const position = [];
                points.forEach((item) => {
                  item.Placemark.forEach((lat) => {
                    const coordinates1 = lat.Point.coordinates.split(',').map(Number);
                    coordinates1.push(Number(lat['wpml:executeHeight']));
                    position.push(coordinates1);
                  });
                });

                const graphicTwo = new mars3d.graphic.PolylineEntity({
                positions: position,
                style: {
                  width: 5,
                  color: '#1791fc',
                  // clampToGround: true,
                  // clampToGround: true,
                },

                attr: { remark: '飞行航线', airRoute: true },
              });
              mapUtil.maplayer.addGraphic(graphicTwo);
              mapdevice.push(graphicTwo);
              } else {
                const points = subdevice.wpmz['wpmz/waylines.wpml'].kml.Document.Folder.Placemark || [];
              let takeOffRefPoint = [0, 0, 0];
              if (subdevice.wpmz['wpmz/waylines.wpml'].kml.Document.Folder['wpml:executeHeightMode'] == 'relativeToStartPoint') {
                // console.log('11111111111111',takeOffRefPoint = subdevice.wpmz['wpmz/template.kml'].kml.Document['wpml:missionConfig']);

                takeOffRefPoint = subdevice.wpmz['wpmz/template.kml'].kml.Document['wpml:missionConfig']['wpml:takeOffRefPoint']
                  ? subdevice.wpmz['wpmz/template.kml'].kml.Document['wpml:missionConfig']['wpml:takeOffRefPoint'].split(',').map(Number)
                  : [0, 0, 0];
              }
              const position = [];
              // points.forEach((item) => {
              //   position.push(item.Point.coordinates.split(',').map(Number));
              // });
              points.forEach((item) => {
                const coordinates1 = item.Point.coordinates.split(',').map(Number);
                coordinates1.push(Number(item['wpml:executeHeight']) + takeOffRefPoint[2]); // 将 executeHeight 加入到坐标数组
                position.push(coordinates1);
              });
              const graphicTwo = new mars3d.graphic.PolylineEntity({
                positions: position,
                style: {
                  width: 5,
                  color: '#1791fc',
                  // clampToGround: true,
                  // clampToGround: true,
                },

                attr: { remark: '飞行航线', airRoute: true },
              });
              mapUtil.maplayer.addGraphic(graphicTwo);
              mapdevice.push(graphicTwo);
              }
            });
          })
          .catch((err) => {
            let position2 = [[subdevice.longitude || device.longitude, subdevice.latitude || device.latitude, subdevice.height || device.height]];
            const graphicTwo = new mars3d.graphic.PolylineEntity({
              positions: position2,
              style: {
                width: 5,
                color: '#1791fc',
                // clampToGround: true,
                // clampToGround: true,
              },

              attr: { remark: '飞行航线', airRoute: false },
            });
            mapUtil.maplayer.addGraphic(graphicTwo);
            mapdevice.push(graphicTwo);
          });

        // setTimeout(() => {
        await mapViewApi
          .getHistoryTrack({ deviceId: device.deviceId })
          .then(async (res) => {
            await axios.get(res.trackFileUrl).then((response) => {
              mapViewApi.clearHistoryTrack({ fileId: res.trackFileId });
              let arr = [];
              arr = response.data.map((item) => [parseFloat(item.longitude), parseFloat(item.latitude), parseFloat(item.height)]);

              mapUtil.historyTrack[device.id] = arr;
              console.log(res.trackFileUrl, arr, device, mapUtil.historyTrack[device.id], 'arr--------------123');
              // console.log(mapUtil.historyTrack,'mapUtil.historyTrack--------------123')
              const graphic3 = new mars3d.graphic.PolylineEntity({
                // positions: mapUtil.historyTrack,
                positions: arr,
                style: {
                  width: 5,
                  color: '#44e0ad',
                  // clampToGround: true,
                  zIndex: 10,
                  // clampToGround: true,
                },
                attr: { remark: '轨迹', airRoute: true },
              });
              setTimeout(()=>{
                mapUtil.maplayer.addGraphic(graphic3);
                mapdevice.push(graphic3);
              },1000)
            });
          })
          .catch((err) => {
            let position2 = [[subdevice.longitude || device.longitude, subdevice.latitude || device.latitude]];
            console.log('轨迹position2', position2);
            console.log('轨迹device.longitude, subdevice.latitude', device.longitude, subdevice.latitude, device.latitude);
            console.log('轨迹subdevice', subdevice);
            console.log('轨迹device', device);
            const graphic3 = new mars3d.graphic.PolylineEntity({
              positions: position2,
              style: {
                width: 5,
                color: '#44e0ad',
                // clampToGround: true,
                zIndex: 10,
                // clampToGround: true,
              },
              attr: { remark: '轨迹', airRoute: false },
            });
            setTimeout(()=>{
              mapUtil.maplayer.addGraphic(graphic3);
              mapdevice.push(graphic3);
            },1000)
          });
        // }, 1500);

        // }
      }
    }
  },
  showResourses: function () {
    mapPhoto.queryMapFile(null).then((res) => {
      const filelist = res?.fileListMap || {};
      Object.keys(filelist).forEach((key) => {
        const locationData = filelist[key];
        const position = Cesium.Cartesian3.fromDegrees(locationData[0].lngShootPosition, locationData[0].latShootPosition, 0);
        console.log(locationData.length, 'locationData');
        if (locationData.length > 1) {
          // 多张图片，显示图片数量的正方形小盒子
          const graphic = new mars3d.graphic.DivGraphic({
            position: position,
            pointerEvents: true,
            style: {
              html: `<div class="image-count-box">${locationData.length}</div>`,
            },
            attr: { data: locationData },
          });

          mapUtil.maplayer.addGraphic(graphic);
        } else {
          const graphic = new mars3d.graphic.DivGraphic({
            position: position,
            pointerEvents: true,
            style: {
              html: `<img class="image-count" src="${locationData[0].thumbnailUrl}" alt="${locationData[0].fileName}" />`,
            },
            attr: { data: locationData[0] },
          });
          mapUtil.maplayer.addGraphic(graphic);
        }
        mapUtil.maplayer.bindPopup(
          (event) => {
            const attr = event.graphic.attr || {};
            const dom = initVue3Popup(picturePreview, attr);
            return dom;
          },
          {
            zIndex: 100,
          }
        );
      });
    });
  },
  showWayline: function (waylineInfo, editable, flyTo) {
    if (waylineInfo.templateTypes === '0') {
      this.showPointWayline(waylineInfo, editable, flyTo);
    } else if (waylineInfo.templateTypes === '1') {
      console.log('2D带状航线不支持！');
      this.show2dWayline(waylineInfo, editable, flyTo);
    } else if (waylineInfo.templateTypes === '2') {
      console.log('3D航线不支持！');
      this.show3dWayline(waylineInfo, editable, flyTo);
    } else if (waylineInfo.templateTypes === '3') {
      console.log('带状航线不支持！');
    }
  },
  showPointWayline: async function (waylineInfo, editable, flyTo) {
    var zhis = this,
      thismap = this.map;
    if (!waylineInfo.wpmz) {
      const response = await fetch('/mapView/preIns_wayline_objs.json');
      waylineInfo.wpmz = await response.json();
    }
    waylineEditor.value.data = waylineInfo;
    if (!waylineInfo.id) {
      waylineEditor.value.startDrawWayline(null);
      return;
    }

    waylineApi.get({ workspaceId: waylineInfo.workspaceId, waylineId: waylineInfo.id }).then(function (res) {
      waylineApi.resolveFile(
        res.fileUrl,
        function (wpmz) {
          waylineInfo.wpmz = wpmz; // 航线的点位
          let imageFormat = wpmz['wpmz/template.kml'].kml.Document.Folder['wpml:payloadParam']['wpml:imageFormat'];
          waylineEditor.value.savePic = [
            imageFormat.indexOf('wide') != -1,
            imageFormat.indexOf('zoom') != -1,
            imageFormat.indexOf('ir') != -1,
            imageFormat.indexOf('visable') != -1,
          ];

          if (editable) {
            let points = wpmz['wpmz/waylines.wpml'].kml.Document.Folder.Placemark;
            let tpoints = wpmz['wpmz/template.kml'].kml.Document.Folder.Placemark;
            waylineEditor.value.pointsIndex.end = points.length > 5 ? 5 : points.length;
            waylineEditor.value.pointsIndex.focus = 0;
            waylineEditor.value.pointsIndex.point = points[0];
            waylineEditor.value.pointsIndex.tpoint = tpoints[0];
          }

          zhis.renderWayline(waylineInfo, editable, flyTo);
        },
        function (tempStorage, tagStr, val) {
          var endIndex = tagStr.indexOf(' ');
          var tag = tagStr.substring(1, endIndex == -1 ? tagStr.length - 1 : endIndex);
          let stack = tempStorage.stack,
            dataObj = stack[stack.length - 1];
          let arrTags = ['wpml:actionGroup', 'wpml:action'];
          if (arrTags.indexOf(tag) != -1 && !dataObj[tag]) {
            dataObj[tag] = [];
          }
        }
      );
    });
  },
  renderWayline: function (waylineInfo, editable, flyTo) {
    let zhis = this,
      points = waylineInfo.wpmz['wpmz/waylines.wpml'].kml.Document.Folder.Placemark || [],
      tpoints = waylineInfo.wpmz['wpmz/template.kml'].kml.Document.Folder.Placemark || [];
    var polyline1 = (this.waylines[waylineInfo.id || waylineInfo.wid] = this.waylines[waylineInfo.id || waylineInfo.wid] || []);

    function calcDistanceAndDuration() {
      var distance = 0,
        speed = waylineInfo.wpmz['wpmz/template.kml'].kml.Document['wpml:missionConfig']['wpml:globalTransitionalSpeed'];
      for (var i = 1; i < points.length; i++) {
        let p1 = points[i - 1].Point.coordinates.split(',');
        let p2 = points[i].Point.coordinates.split(',');
        distance += Math.round(
          mars3d.MeasureUtil.getDistance([
            { lng: p1[0], lat: p1[1] },
            { lng: p2[0], lat: p2[1] },
          ])
        );
      }
      if (Math.abs(waylineInfo.wpmz['wpmz/waylines.wpml'].kml.Document.Folder['wpml:distance'] - distance) > 1) {
        waylineInfo.wpmz['wpmz/waylines.wpml'].kml.Document.Folder['wpml:distance'] = distance;
        waylineInfo.wpmz['wpmz/waylines.wpml'].kml.Document.Folder['wpml:duration'] = Math.round(distance / (speed || 15));
      }
    }

    function updateWaylinePoints(pointMaker, lnglat) {
      let seq = polyline1.indexOf(pointMaker);
      // pos = coordtransform.gcj02towgs84(lnglat.lng, lnglat.lat);
      seq != -1 && (points[seq - 1].Point.coordinates = lnglat.lng + ',' + lnglat.lat);

      calcDistanceAndDuration();

      mapUtil.maplayer.removeGraphic(polyline1[0], true);
      polyline1[0] = zhis.createPolyLine(
        points.map((obj) => {
          let lnglat = obj.Point.coordinates.split(',');
          let objPos = [lnglat[0], lnglat[1]]; //coordtransform.wgs84togcj02(lnglat[0], lnglat[1]);
          return objPos;
        })
      );
      mapUtil.maplayer.addGraphic(polyline1[0]);
    }

    calcDistanceAndDuration();

    polyline1.forEach((graphic) => zhis.maplayer.removeGraphic(graphic, true));
    polyline1.length = 0;

    let linePoints = points.map((obj) => {
      let lnglat = obj.Point.coordinates.split(',');
      return [lnglat[0], lnglat[1]]; //coordtransform.wgs84togcj02(lnglat[0], lnglat[1]);
    });
    polyline1[0] = zhis.createPolyLine(linePoints);

    for (var i = 0; i < linePoints.length; i++) {
      let pointMaker = new mars3d.graphic.BillboardEntity({
        position: linePoints[i],
        style: {
          image: svg(i < 100 ? i + 1 : 0),
          horizontalOrigin: mars3d.Cesium.HorizontalOrigin.CENTER,
          verticalOrigin: mars3d.Cesium.VerticalOrigin.BOTTOM,
          clampToGround: true,
        },
      });

      if (editable) {
        pointMaker
          .off()
          .on(mars3d.EventType.leftDown, function (e) {
            mapUtil.maplayer.startEditing(pointMaker);
          })
          .on(mars3d.EventType.updatePosition, function (e) {
            updateWaylinePoints(pointMaker, e.target.point);
          })
          .on(mars3d.EventType.leftUp, function (e) {
            mapUtil.maplayer.stopEditing();
          })
          .unbindContextMenu()
          .bindContextMenu([
            {
              text: '增加航点',
              callback: (e) => {
                const seq = polyline1.indexOf(pointMaker);
                waylineEditor.value.startDrawWayline(seq, true);
              },
            },
            {
              text: '插入航点',
              callback: (e) => {
                const seq = polyline1.indexOf(pointMaker);
                waylineEditor.value.startDrawWayline(seq - 1, true);
              },
            },
            {
              text: '删除航点',
              callback: (e) => {
                const seq = polyline1.indexOf(pointMaker);
                pointMaker.remove(true);
                polyline1.splice(seq, 1);
                points.splice(seq - 1, 1);
                tpoints.splice(seq - 1, 1);
                zhis.renderWayline(waylineInfo, true);

                if (waylineEditor.value.pointsIndex.end > points.length) {
                  waylineEditor.value.pointsIndex.end = points.length;
                }
                if (waylineEditor.value.pointsIndex.focus == waylineEditor.value.pointsIndex.end) {
                  waylineEditor.value.pointsIndex.focus = waylineEditor.value.pointsIndex.end - 1;
                }
                waylineEditor.value.pointsIndex.point = points[waylineEditor.value.pointsIndex.focus];
                waylineEditor.value.pointsIndex.tpoint = tpoints[waylineEditor.value.pointsIndex.focus];
              },
            },
            {
              text: '删除全部航点',
              callback: (e) => {
                polyline1.forEach((graphic) => graphic && graphic.remove(true));
                polyline1.length = 0;
                points.length = 0;
                tpoints.length = 0;
                waylineEditor.value.pointsIndex.end = 0;
                waylineEditor.value.startDrawWayline(null);
              },
            },
          ]);
      }
      polyline1.push(pointMaker);
    }

    polyline1.forEach((graphic) => zhis.maplayer.addGraphic(graphic));

    flyTo && polyline1 && polyline1[0] && mapUtil.map.flyToPoint(polyline1[0].point);
  },
  show2dWayline: async function (waylineInfo, editable, flyTo) {
    this.showPointWayline(waylineInfo, editable, flyTo);
  },
  show3dWayline: async function (waylineInfo, editable, flyTo) {
    var zhis = this,
      thismap = this.map;

    waylineEditor.value.data = null;

    if (waylineInfo.wpmz) {
      zhis.render3dWayline(waylineInfo, editable, flyTo);
      return;
    }

    waylineApi.get({ workspaceId: waylineInfo.workspaceId, waylineId: waylineInfo.id }).then(function (res) {
      waylineApi.resolveFile(
        res.fileUrl,
        function (wpmz) {
          waylineInfo.wpmz = wpmz; // 航线的点位

          zhis.render3dWayline(waylineInfo, editable, flyTo);
        },
        function (tempStorage, tagStr, val) {
          var endIndex = tagStr.indexOf(' ');
          var tag = tagStr.substring(1, endIndex == -1 ? tagStr.length - 1 : endIndex);
          let stack = tempStorage.stack,
            dataObj = stack[stack.length - 1];
          let arrTags = ['wpml:actionGroup', 'wpml:action'];
          if (arrTags.indexOf(tag) != -1 && !dataObj[tag]) {
            dataObj[tag] = [];
          }
        }
      );
    });
  },
  render3dWayline: function (waylineInfo, editable, flyTo) {
    let zhis = this,
      folders = waylineInfo.wpmz['wpmz/waylines.wpml'].kml.Document.Folder || [];
    var polylineArr = (this.waylines[waylineInfo.id || waylineInfo.wid] = this.waylines[waylineInfo.id || waylineInfo.wid] || []);

    polylineArr.length = 0;

    let polyline1, linePoints;
    for (let fIdx = 0; fIdx < folders.length; fIdx++) {
      (polyline1 = []), (linePoints = [null]);
      folders[fIdx].Placemark.forEach((obj) => {
        let lnglat = obj.Point.coordinates.split(',');
        linePoints.push([lnglat[0], lnglat[1]]);
      });

      polyline1[0] = zhis.createPolyLine(linePoints);

      for (var i = 0; i < linePoints.length; i++) {
        let pointMaker = new mars3d.graphic.BillboardEntity({
          position: linePoints[i],
          style: {
            image: svg(i < 100 ? i + 1 : 0),
            horizontalOrigin: mars3d.Cesium.HorizontalOrigin.CENTER,
            verticalOrigin: mars3d.Cesium.VerticalOrigin.BOTTOM,
            clampToGround: true,
          },
        });

        polyline1.push(pointMaker);
      }
      polyline1.forEach((graphic) => zhis.maplayer.addGraphic(graphic));

      polylineArr.push(polyline1);
    }

    flyTo && polyline1 && polyline1[0] && mapUtil.map.flyToPoint(polyline1[0].point);
  },
  createPolyLine: function (linePoints) {
    return new mars3d.graphic.PolylineEntity({
      positions: linePoints,
      style: {
        width: 2,
        color: '#0FAB94',
        highlight: {
          color: '#61C8FF',
        },
        clampToGround: true,
      },
    });
  },
  showMarkers: function (markerInfo) {
    const zhis = this;
    let infoMarker;
    for (let elementId in this.markers) {
      let info = this.markers[elementId];
      let amarker = this.createAmarker(info);
      if (amarker) {
        this.maplayer.addGraphic(amarker);
        if (markerInfo && markerInfo.elementId === elementId) {
          infoMarker = amarker;
        }
        markerEditor.value.bindMarkerInfo(info, amarker, infoMarker === amarker);
      }
    }

    if (markerInfo) {
      if (!infoMarker) {
        infoMarker = this.createAmarker(markerInfo);
        this.maplayer.addGraphic(infoMarker, { flyTo: false });
        markerEditor.value.bindMarkerInfo(markerInfo, infoMarker, true);
      }
    }
  },
  createAmarker: function (info) {
    var amarker = null,
      coordinates = info.resource.content.geometry.coordinates;
    if (!coordinates) {
    } else if (info.resource.type == 0 && coordinates.length >= 1) {
      amarker = new mars3d.graphic.BillboardEntity({
        position: coordinates,
        style: {
          zIndex: hierarchyDCount.value++,
          image: svg('点标记'),
          color: markerEditor.value.markerColors[info.resource.content.properties.color],
          horizontalOrigin: mars3d.Cesium.HorizontalOrigin.CENTER,
          verticalOrigin: mars3d.Cesium.VerticalOrigin.BOTTOM,
          clampToGround: true,
          scale: 1,
          highlight: {
            scale: 1.1,
            // outline: true,
            // outlineWidth: 3,
            // outlineColor:'#ffffff',
            label: {
              color: '#010101',
              outline: false,
            },
          },
          label: {
            text: info.elementName,
            font_size: 15,
            color: '#010101',
            outline: true,
            outlineColor: '#ffffff',
            outlineWidth: 5,
            distanceDisplayCondition: true,
            distanceDisplayCondition_far: 500000,
            distanceDisplayCondition_near: 0,
          },
        },
        attr: { remark: info.name },
      });
    } else if (info.resource.type == 1 && coordinates.length >= 2) {
      amarker = new mars3d.graphic.PolylineEntity({
        positions: coordinates,
        style: {
          zIndex: hierarchyXCount.value++,
          width: 5,
          color: markerEditor.value.markerColors[info.resource.content.properties.color],
          outline: true,
          outlineWidth: 3,
          outlineColor: markerEditor.value.markerColors[info.resource.content.properties.color],
          highlight: {
            outline: true,
            outlineWidth: 3,
            outlineColor: '#ffffff',
            label: {
              color: '#010101',
              outline: false,
            },
          },
          clampToGround: true,
          label: {
            text: info.elementName,
            font_size: 15,
            color: '#010101',
            outline: true,
            outlineColor: '#ffffff',
            outlineWidth: 5,
            distanceDisplayCondition: true,
            distanceDisplayCondition_far: 500000,
            distanceDisplayCondition_near: 0,
          },
        },
        attr: { remark: info.name },
      });
      // mapUtil.map.setSceneOptions( {   center:{}lng:coordinates[0],lat:coordinates[1] })
    } else if (info.resource.type == 2 && coordinates[0].length >= 3) {
      amarker = new mars3d.graphic.PolygonEntity({
        positions: coordinates[0],
        style: {
          zIndex: hierarchyMCount.value++,
          color: markerEditor.value.markerColors[info.resource.content.properties.color],
          opacity: 0.5,
          outline: true,
          outlineWidth: 3,
          outlineColor: markerEditor.value.markerColors[info.resource.content.properties.color],
          clampToGround: true,
          highlight: {
            outline: true,
            outlineWidth: 3,
            outlineColor: '#ffffff',
            label: {
              color: '#010101',
              outline: false,
            },
          },
          label: {
            text: info.elementName,
            font_size: 15,
            color: '#010101',
            outline: true,
            outlineColor: '#ffffff',
            outlineWidth: 5,
            distanceDisplayCondition: true,
            distanceDisplayCondition_far: 500000,
            distanceDisplayCondition_near: 0,
          },
        },
        attr: { remark: info.name },
      });
    }
    markerEditor.value.bindEvent(info, amarker);

    return amarker;
  },
  toggleMode: function () {
    this.maptype3d2d.value = ++this.maptype3d2d.value % 2;
    if (this.maptype3d2d.value) {
      this.map.scene.morphTo2D(0);
    } else {
      this.map.scene.morphTo3D(0);
    }
  },

  renderLayer: function (changedArr, checked) {
    console.log('传过来的  ', changedArr, checked);
    if (checked) {
      // 新增图层
      changedArr.forEach((item) => {
        switch (item.dataType) {
          case '9':
            this.drawPictrue(item);
            break;
          case '10':
            this.drawPictrue(item);
            break;
          case '30':
            this.drawOrthoPhoto(item);
            break;
          case '31':
            this.drawThreeD(item);
            break;
          case '32':
            this.drawPointCloud(item);
            break;
          case '33':
            this.drawPanorama(item);
            break;
        }
      });
    } else {
      // 移除图层
      changedArr.forEach((item) => {
        if (item.dataType === '10' || item.dataType === '33' || item.dataType === '9') {
          console.log('mapUtil.layerInfoLayer.graphics', mapUtil.layerInfoLayer.graphics);
          const graphics = mapUtil.layerInfoLayer.graphics;
          graphics.forEach((ite) => {
            if (ite.id === item.fileId) {
              mapUtil.layerInfoLayer.removeGraphic(ite);
            }
          });
        } else {
          const layers = mapUtil.map.getLayers(); // 获取所有图层
          console.log('layers==>>', layers);
          // 遍历所有图层，找到并移除匹配的图层
          layers.forEach((layer) => {
            if (layer.id === item.fileId) {
              mapUtil.map.removeLayer(layer, true);
            }
          });
        }
      });
    }
  },

  drawPictrue: function (item) {
    const position = Cesium.Cartesian3.fromDegrees(item.lngShootPosition, item.latShootPosition, 0);
    if (item.length > 1) {
      // 多张图片，显示图片数量的正方形小盒子
      const graphic = new mars3d.graphic.DivGraphic({
        id: item.fileId,
        position: position,
        pointerEvents: true,
        style: {
          html: `<div class="image-count-box">${item.length}</div>`,
          clampToGround: true,
        },
        attr: { data: item },
      });

      mapUtil.layerInfoLayer.addGraphic(graphic);
    } else {
      const graphic = new mars3d.graphic.DivGraphic({
        id: item.fileId,
        position: position,
        pointerEvents: true,
        style: {
          html: `<img class="image-count" src="${item.thumbnailUrl}" alt="${item.fileName}" />`,
          clampToGround: true,
        },
        attr: { data: item },
      });
      mapUtil.layerInfoLayer.addGraphic(graphic);
    }
    mapUtil.layerInfoLayer.bindPopup(
      (event) => {
        const attr = event.graphic.attr || {};
        const dom = initVue3Popup(picturePreview, attr);
        return dom;
      },
      {
        zIndex: 100,
      }
    );
    console.log('mapUtil.layerInfoLayer.graphics', mapUtil.layerInfoLayer.graphics);
  },
  drawPanorama: function (item) {
    const position = Cesium.Cartesian3.fromDegrees(item.lngShootPosition, item.latShootPosition, 0);
    const graphic = new mars3d.graphic.DivGraphic({
      id: item.fileId,
      position: position,
      pointerEvents: true,
      style: {
        html: `<img class="image-count" src="${item.thumbnailUrl}" alt="${item.fileName}" />`,
        clampToGround: true,
      },
      attr: { src: item.hdUrl, inMap: true },
    });
    mapUtil.layerInfoLayer.addGraphic(graphic);
    mapUtil.layerInfoLayer.bindPopup(
      (event) => {
        const attr = event.graphic.attr || {};
        const dom = initVue3Popup(VuePannellum, attr);
        console.log('attr', attr);
        console.log('dom', dom);
        return dom;
      },
      {
        zIndex: 100,
      }
    );
  },
  drawOrthoPhoto: function (item) {
    const ortho = item.orthoPhotoDto;
    if (ortho) {
      let tileLayer = new mars3d.layer.WmsLayer({
        id: item.fileId,
        url: ortho.url ? ortho.url.replace('//172.30.50.54:8090/', '//172.30.50.60:8703/') : '//172.30.50.54:8090/geoserver/uav_work/wms', // "//server.mars3d.cn/geoserver/mars/wms",
        // url: ortho.url,
        layers: ortho.layerName, // "mars:hf",
        crs: 'EPSG:4326', // 默认值Web墨卡托投影坐标系
        parameters: {
          transparent: true,
          format: 'image/png',
        },
        // 单击高亮及其样式
        highlight: {
          type: 'wallP',
          diffHeight: 100,
          materialType: mars3d.MaterialType.LineFlow,
          materialOptions: {
            image: 'img/textures/fence.png',
            color: '#ffff00',
            speed: 20, // 速度，建议取值范围1-100
            axisY: true,
          },
        },
        // minimumLevel: 7,
        // maximumLevel: 16,
        popup: 'all',
        // popupOptions: {
        //   autoClose: false,
        //   closeOnClick: false,
        //   checkData: function (attr, graphic) {
        //     if (Cesium.defined(attr.OBJECTID)) {
        //       return graphic.attr.OBJECTID === attr.OBJECTID
        //     }
        //     if (Cesium.defined(attr.NAME)) {
        //       return graphic.attr.NAME === attr.NAME
        //     }
        //     return false
        //   }
        // },
        flyTo: true,
      });
      mapUtil.map.addLayer(tileLayer);
    }
  },
  drawThreeD: function (item) {
    const tiles3dLayer = new mars3d.layer.TilesetLayer({
      id: item.fileId,
      name: item.fileName,
      // url: "http://localhost:5173/terra_b3dms/tileset.json",
      // url: "http://localhost:5173/wkf2/3d_tiles/model/tileset.json",
      // url: "http://**************:19201/uat-bucket/model/terra_b3dms/tileset.json",
      url: item.fileUrl,
      // position: { alt: 148.2 },
      maximumScreenSpaceError: 1,
      dynamicScreenSpaceError: true,
      cullWithChildrenBounds: false,
      skipLevelOfDetail: true,
      preferLeaves: true,
      flyTo: true,
    });
    console.log('mapUtil.map', mapUtil.map);
    mapUtil.map.addLayer(tiles3dLayer);
  },
  drawPointCloud: function (item) {
    const tiles3dLayer = new mars3d.layer.TilesetLayer({
      id: item.fileId,
      name: item.fileName,
      url: item.fileUrl,
      maximumScreenSpaceError: 1,
      flyTo: true,
      // enableDebugWireframe: true, // 是否可以进行三角网的切换显示
    });
    mapUtil.map.addLayer(tiles3dLayer);
  },
  drawAirRoute: function () {},
  createVideoJessiBuca: function (video, data) {
    let jessibucaDom;
    let jessibucaPro;
    jessibucaDom = mars3d.DomUtil.create('div', '', document.body);
    jessibucaDom.style.display = 'none';
    jessibucaPro = new window.JessibucaPro({
      container: jessibucaDom, //播放器容器。
      decoder: '/jessibuca-pro/decoder-pro.js',
      hasAudio: false, //是否有音频，如果设置false，则不对音频数据解码，提升性能。
      videoBuffer: 0.1, // 缓存时长
      isResize: false, //当为true的时候：视频画面做等比缩放后,高或宽对齐canvas区域,画面不被拉伸,但有黑边。 等同于 setScaleMode(1)当为false的时候：视频画面完全填充canvas区域,画面会被拉伸。等同于 setScaleMode(0)
      text: '',
      loadingText: '', //加载过程中文案
      useMSE: true, //是否开启MediaSource硬解码
      autoWasm: true, //是否开启wasm解码
      debug: false, //是否开启控制台调试打印
      wasmDecodeErrorReplay: true, //是否开启解码失败重新播放
      heartTimeoutReplay: true, //是否开启心跳超时之后自动再播放
      loadingTimeoutReplay: true, //是否开启loading超时之后自动再播放
      heartTimeoutReplayTimes: 15, // heartTimeoutReplay 重试次数   如果想无限次重试，可以设置为-1
      loadingTimeoutReplayTimes: 15, // loadingTimeoutReplay 重试次数  如果想无限次重试，可以设置为-1
      showBandwidth: false, // 显示网速
      operateBtns: {
        //配置操作按钮
        fullscreen: false, //是否显示全屏按钮
        // webFullscreen: showOperateBtns,  //当前是否web全屏
        screenshot: false, //是否显示截图按钮
        play: false, //是否显示播放暂停按钮
        audio: false, //是否显示声音按钮
        recorder: false, // 是否显示录制按钮
      },
      supportDblclickFullscreen: false, // 是否支持屏幕的双击事件，触发全屏，取消全屏事件。
      forceNoOffscreen: false, //是否不使用离屏模式（提升渲染能力）
      muted: true,
      useSIMD: true,
      useWCS: true,
    });
    setTimeout(() => {
      jessibucaPro.play(video.videoUrl);
    }, 100);
    data.Video3D.videDom.push(jessibucaDom);
  },
  // createVideoDom: function (video,data) {
  //   let videoElement
  //   videoElement = mars3d.DomUtil.create("video", "", document.body)
  //   videoElement.setAttribute("muted", "muted")
  //   videoElement.setAttribute("autoplay", "autoplay")
  //   videoElement.setAttribute("loop", "loop")
  //   videoElement.setAttribute("crossorigin", "")
  //   videoElement.setAttribute("controls", "")
  //   videoElement.style.display = "none"

  //   // 加FLV演示数据
  //   if (mpegts.isSupported()) {
  //     const flvPlayer = mpegts.createPlayer({
  //       type: "flv",
  //       isLive: true,
  //       hasAudio: false,
  //       url: video.videoUrl
  //     })
  //     flvPlayer.attachMediaElement(videoElement)
  //     flvPlayer.load()
  //     flvPlayer.play()
  //   } else {
  //     // globalMsg("不支持flv格式视频")
  //     console.error("不支持flv格式视频")
  //   }

  //   setTimeout(() => {
  //     try {
  //       if (videoElement.paused) {
  //         // globalMsg("当前浏览器已限制自动播放，请单击播放按钮")
  //         console.error("当前浏览器已限制自动播放，请单击播放按钮")
  //         videoElement.play()
  //       }
  //     } catch (e) {
  //       // 规避浏览器权限异常
  //       console.error("当前浏览器已限制自动播放，请单击播放按钮")
  //       // globalMsg("当前浏览器已限制自动播放，请单击播放按钮")
  //     }
  //   }, 3000)
  //   data.Video3D.videDom.push(videoElement)
  // },
  drawVideo3D: function (video, data) {
    var zhis = this;
    // zhis.createVideoDom(video,data)
    zhis.createVideoJessiBuca(video, data);
    console.log(mapUtil.VehiclePosition, 'mapUtil.VehiclePosition');
    setTimeout(() => {
      const video3D = new mars3d.graphic.Video2D({
        position: new Cesium.CallbackProperty(() => {
          // return mars3d.PointTrans.lonlat2cartesian(mapUtil.VehiclePosition)
          return mapUtil.devices[data.id][1].positions[mapUtil.devices[data.id][1].positions.length - 1];
          // console.log(mapUtil.devices[data.id][1])
          // return mapUtil.VehiclePosition
        }, false),
        style: {
          container: data.Video3D.videDom[0].querySelector('video'),
          angle: drawerRef.value.aerodromeList.dockShadow?.mainGimbalCameraVo?.hfov || 30,
          angle2: drawerRef.value.aerodromeList.dockShadow?.mainGimbalCameraVo?.vfov || 15,
          heading: drawerRef.value.aircraftList.aircraftShadow?.attitudeHead || -18.26,
          pitch: drawerRef.value.aircraftList.aircraftShadow?.attitudePitch || -84.5,
          distance: drawerRef.value.aerodromeList.dockShadow?.mainGimbalCameraVo?.projectionDistance || 81.6962675,
          textureCoordinates: new Cesium.PolygonHierarchy([
            new Cesium.Cartesian2(0, 1),
            new Cesium.Cartesian2(1, 1),
            new Cesium.Cartesian2(1, 0),
            new Cesium.Cartesian2(0, 0),
          ]),
          showFrustum: true,
        },
        flyTo: true,
        attr: { remark: '示例3' },
      });
      mapUtil.maplayer.addGraphic(video3D);
      data.Video3D.cast.push(video3D);
    }, 100);
    console.log(mars3d.PointTrans.cartesian2lonlat({ x: -2405634.271325113, y: 5373163.046995504, z: 2445885.5541609265 }), '卡迪尔');
    setTimeout(() => {
      // data.Video3D.cast[0].angle = 40
      // data.Video3D.cast[0].height = 1000
      // mapUtil.devices[data.id][1].setCallbackPosition([114.121695, 22.696842])
      // data.Video3D.cast[0].position = mars3d.PointTrans.lonlat2cartesian([114.121695, 22.696842,100])
    }, 5000);
  },
};

const deviceUtil = {
  types: ['飞机', '遥控器', '机场', '摄像头'],
  pilotState: ['离线', '在线'],
  // dockState: ['空闲中', '现场调试', '远程调试', '固件升级中', '作业中', '离线'],
  dockState: {
    0: '空闲中',
    1: '现场调试',
    2: '远程调试',
    3: '固件升级',
    4: '作业中',
    5: '待标定',
    99: '离线',
  },
  droneState: [
    '待机',
    '起飞准备',
    '起飞准备完毕',
    '手动飞行',
    '自动起飞',
    '航线飞行',
    '全景拍照',
    '智能跟随',
    'ADS-B 躲避',
    '自动返航',
    '自动降落',
    '强制降落',
    '三桨叶降落',
    '升级中',
    '未连接',
    'APAS',
    '虚拟摇杆状态',
    '指令飞行',
    '离线',
  ],
  payloadlist: {
    89: [
      { code: 42, model: 'H20' },
      { code: 43, model: 'H20T' },
      { code: 61, model: 'H20N' },
      { code: 65534, model: 'PSDK 负载' },
    ],
    60: [
      { code: 42, model: 'H20' },
      { code: 43, model: 'H20T' },
      { code: 61, model: 'H20N' },
      { code: 65534, model: 'PSDK 负载' },
    ],
    67: [
      { code: 52, model: 'M30双光相机' },
      { code: 53, model: 'M30T三光相机' },
      { code: 65534, model: 'PSDK 负载' },
    ],
    77: [
      { code: 66, model: 'Mavic 3E 相机' },
      { code: 67, model: 'Mavic 3T 相机' },
      { code: 68, model: 'Mavic 3M 相机' },
      { code: 65534, model: 'PSDK 负载' },
    ],
    91: [
      { code: 80, model: 'Matrice 3D 相机' },
      { code: 81, model: 'Matrice 3TD 相机' },
    ],
    99: [
      { code: 88, model: 'Matrice 4E 相机' },
      { code: 89, model: 'Matrice 4T 相机' },
    ],
    100: [
      { code: 98, model: 'Matrice 4D 相机' },
      { code: 99, model: 'Matrice 4TD 相机' },
    ],
  },
  dronelist: { 89: 'M350 RTK', 60: 'M300 RTK', 67: 'M30/M30T', 77: 'M3E/M3T/M3M', 91: 'M3D/M3TD', 99: 'M4E/M4T', 100: 'M4D/M4TD' },
  formatDeviceTag: function (device) {
    return this.types[device.deviceType - 1];
  },
  formatDesc: function (device) {
    var desc = '未知状态';
    if (device.deviceType == 2) {
      return this.droneState[device.deviceList[0].state] || desc;
    } else if (device.deviceType == 3) {
      return this.dockState[device.state] || desc;
    }
    return desc;
  },
  formatDroneModel: function (droneModel) {
    return this.dronelist[droneModel] || '未知机型';
  },
  formatPayloadModel: function (droneModel, payloadModel) {
    var paylod = (this.payloadlist[droneModel] || []).filter((obj) => obj.code == payloadModel)[0];
    return (paylod || {}).model || '未知负载';
  },
  formatSpeed: function (speed) {
    if (speed) {
      if (speed >= 1024) {
        return (speed / 1024).toFixed(2) + 'MB'; // 将单位从 KB 转换为 MB
      } else {
        return JSON.parse(speed).toFixed(2) + 'KB';
      }
    }
    // return (speed / 1024).toFixed(2) + 'MB';
  },
  getBattery: function (device, format) {
    if (device?.dockOsd?.drone_battery_maintenance_info?.batteries.length > 0 || device?.deviceList?.length) {
      let percent =
        device?.dockOsd?.drone_battery_maintenance_info?.batteries[0]?.capacity_percent ||
        device.deviceList[0].deviceOsd.battery.capacity_percent ||
        0;
      if (device.deviceList?.[0]?.state == '18') {
        return format
          ? device.droneBatteryMaintenanceInfo?.batteries?.[0].capacity_percent || percent + format
          : device.droneBatteryMaintenanceInfo?.batteries?.[0].capacity_percent || percent;
      }
      return format ? percent + format : percent;
    }
    if (device.uavFlag) {
      return 100;
    }
    return '';
  },
  isLowBattery: function (device) {
    if (device.deviceList && device.deviceList.length > 0 && device.deviceList[0].deviceOsd && device.deviceList[0].deviceOsd.battery) {
      return device.deviceList[0].deviceOsd.battery.capacity_percent < 20;
    }
    return false;
  },
  swapElements: function (a, b) {
    // 检查参数是否为有效的DOM节点
    if (!a || !b || !(a instanceof Node) || !(b instanceof Node)) {
      console.warn('swapElements: 无效的DOM节点参数');
      return;
    }

    if (a == b) return;
    //记录父元素
    var bp = b.parentNode,
      ap = a.parentNode;
    //记录下一个同级元素
    var an = a.nextElementSibling,
      bn = b.nextElementSibling;
    //如果参照物是邻近元素则直接调整位置
    if (an == b) return bp.insertBefore(b, a);
    if (bn == a) return ap.insertBefore(a, b);
    if (a.contains(b)) return ap.insertBefore(b, a), bp.insertBefore(a, bn); //如果a包含了b
    else return bp.insertBefore(a, b), ap.insertBefore(b, an);
  },
  fullScreenVideo: function (video) {
    if (deviceStatus.value.fullVideo && video.snindex != deviceStatus.value.fullVideo.snindex) {
      this.fullScreenVideo(deviceStatus.value.fullVideo);
    }

    let mapDom = document.getElementById('map');
    let videoDom = document.getElementById('video' + video.snindex);
    if (videoDom && videoDom.classList.contains('Hidden')) {
      // 操作dom后会残留重复dom
      videoDom.outerHTML = '';
      videoDom = document.getElementById('video' + video.snindex);
    }
    // 确保所有DOM元素都存在再调用swapElements
    if (mapDom && videoDom) {
      this.swapElements(mapDom, videoDom);
    }
    deviceStatus.value.fullVideo = deviceStatus.value.fullVideo ? null : video;
    // mapUtil.map.resize();
  },
  closeVideo: function (video, skipStopLive, ref) {
    TurnAiList.value = [];
    if (deviceStatus.value.fullVideo && video.snindex == deviceStatus.value.fullVideo.snindex) {
      this.fullScreenVideo(video);
    }
    video.isHide = true;
    video.url = null;
    video.videoUrl = null;
    video.token = null;
    !skipStopLive && video.deviceLiveId && deviceApi.stopLiving({ deviceLiveId: video.deviceLiveId, force: false });
  },
};

watch(
  () => waylineEditor.value.data,
  (newInfo, oldInfo) => {
    if (!newInfo.wpmz) {
      return;
    }
    let tfolder = newInfo.wpmz['wpmz/template.kml'].kml.Document.Folder,
      tpoints = tfolder.Placemark;
    let points = newInfo.wpmz['wpmz/waylines.wpml'].kml.Document.Folder.Placemark;
    for (var i = 0; i < tpoints.length; i++) {
      var tpoint = tpoints[i];
      if (tpoint && tpoint.useGlobalHeight == '1') {
        tpoint['wpml:height'] = tfolder['wpml:globalHeight'];
        points[i]['wpml:executeHeight'] = tfolder['wpml:globalHeight'];
        tpoint['wpml:ellipsoidHeight'] = tfolder['wpml:globalHeight'];
      }
    }
  },
  { deep: true }
);

function addWetherGraphic(graphicLayer, center) {
  markerApi.getWeather({}).then((res) => {
    res.alarms.forEach((item) => {
      const graphic = new mars3d.graphic.BillboardEntity({
        position: [item.longitude, item.latitude],
        style: {
          image: svg(`weather${item.typeKey}`),
          width: 60,
          height: 60,
          scale: 1,
          clampToGround: true,
        },
        pointerEvents: false, // false时不允许拾取和触发任意鼠标事件，但可以穿透div缩放地球
      });
      graphicLayer.addGraphic(graphic);
    });
  });
}

const airSpace = {
  clock: new Date().getTime(),
  flights: null,
  interval: null,
  updateBackLayer: function () {
    mapUtil.mapbacklayer.clear(true);
    console.log('mapbacklayer清除了');

    airSpace.flights.forEach((flight) => {
      mapUtil.mapbacklayer.addGraphic(
        new mars3d.graphic.BillboardEntity({
          position: [flight.lng, flight.lat],
          style: {
            image: svg('机场'),
            horizontalOrigin: mars3d.Cesium.HorizontalOrigin.CENTER,
            verticalOrigin: mars3d.Cesium.VerticalOrigin.BOTTOM,
            rotation: flight.angle,
          },
          attr: { remark: '飞机' },
        })
      );
    });
  },
  updateFlights: function (newFlights) {
    if (Array.isArray(newFlights)) {
      airSpace.flights.push(...newFlights);
      airSpace.updateBackLayer();
    }
  },
  update: function () {
    airSpace.flights = [];
    const mapCenter = mapUtil.map.getCenter();
    airSpace.clock = new Date().getTime();
    mapViewApi.getAreaFlight({ latcenter: mapCenter.lat, longcenter: mapCenter.lng, radius: 100 }).then(function (res) {
      if (res && res.flightCount) {
        airSpace.updateFlights(res.data);
      } else {
        res.json().then((json) => {
          airSpace.updateFlights(json.data);
        });
      }
    });
    return this.update;
  },
};

// 深拷贝
function deepClone(source) {
  if (!source || typeof source !== 'object') {
    return source;
  }
  if (Array.isArray(source)) {
    return source.map(deepClone);
  }
  const target = Object.create(Object.getPrototypeOf(source));
  for (const key in source) {
    if (source.hasOwnProperty(key)) {
      target[key] = deepClone(source[key]);
    }
  }
  return target;
}

const showLayerTree = () => {
  layerTreeBoolean.value = !layerTreeBoolean.value;
};

// 添加WebSocket消息监听器
function addWebSocketListener() {
  console.log('监听websocket');
  onWebSocket((message) => {
    if (message.action == 'add' || message.action == 'update') {
      drawerRef.value.showList(2, 0, drawerRef.value.showType != 2);
    } else if (message.action == 'del') {
      // setTimeout(()=>{
      //   mapUtil.updateView();
      //   drawerRef.value.showList(2);
      // },2000)

      mapUtil.maplayer.clear(true);
      // setTimeout(()=>{
      drawerRef.value.showList(2, 0, drawerRef.value.showType != 2);
      // },2000)
      // drawerRef.value.showList(2);
    }
    console.log(message, '监听到消息3D');
  });
}

function normalizeTree(list = []) {
    return list.map((item) => ({
      ...item,
      key: item.orgCode,
      value: item.orgCode,
      disabled: item.disableCheckbox === true,
      children: item.children ? normalizeTree(item.children) : [],
    }));
}

onMounted(() => {
  console.log('mount and load map...');
  const myDepart = localStorage.getItem('myDepartList');
  const myDepartAndChildrenTree = localStorage.getItem('myDepartAndChildrenTree');
  // drawerRef.value.departmentList = myDepartAndChildrenTree ? JSON.parse(myDepartAndChildrenTree) : [];
  drawerRef.value.departmentList = myDepartAndChildrenTree && myDepartAndChildrenTree !== 'null' ? normalizeTree(JSON.parse(myDepartAndChildrenTree)) : [];
  drawerRef.value.deptFilterCriteria.departmentName = myDepart ? JSON.parse(myDepart)?.[0].orgCode : '';
  drawerRef.value.myDepart = JSON.parse(myDepart)
  JSON.parse(myDepart).map((item)=>{
    drawerRef.value.myDepartListArr.push(item.orgCode)
  })
  let mars3dConfig = mars3d.Util.merge({}, defaultMapConfig.map3d);
  mars3dConfig = mars3d.Util.merge(mars3dConfig, toRaw(configMap));
  mapUtil.map = new mars3d.Map('map', mars3dConfig);
  // 地图背景图层
  mapUtil.mapbacklayer = new mars3d.layer.GraphicLayer({
    allowDrillPick: true, // 如果存在坐标完全相同的图标点，可以打开该属性，click事件通过graphics判断
  });
  mapUtil.map.addLayer(mapUtil.mapbacklayer);

  // 图层信息
  mapUtil.layerInfoLayer = new mars3d.layer.GraphicLayer({
    allowDrillPick: true, // 如果存在坐标完全相同的图标点，可以打开该属性，click事件通过graphics判断
  });
  mapUtil.map.addLayer(mapUtil.layerInfoLayer);

  // 地图业务图层
  mapUtil.maplayer = new mars3d.layer.GraphicLayer({
    allowDrillPick: false, // 如果存在坐标完全相同的图标点，可以打开该属性，click事件通过graphics判断
  });
  mapUtil.map.addLayer(mapUtil.maplayer);

  mapUtil.maplayer.on(mars3d.EventType.rightClick, function (event) {
    console.log('waylineEditor.pointsIndex.end= ' + waylineEditor.value.pointsIndex.end);
    if (waylineEditor.value.isShowEditor && waylineEditor.value.pointsIndex.end >= 2) {
      mapUtil.maplayer.stopDraw();
      mapUtil.maplayer.stopEditing();
    }
  });

  // mapUtil.map.on(mars3d.EventType.cameraChanged, function (e) {
  //   console.log(mapUtil.map.level);

  //   airSpace.interval && clearInterval(airSpace.interval);
  //   mapUtil.mapbacklayer.clear(true);
  //   console.log('mapbacklayer清除了');
  //   if (mapUtil.map.level >= 9) {
  //     // 显示飞机
  //     airSpace.interval = setInterval(airSpace.update(), 6000);
  //   }
  // });

  let latitudeLongitude = localStorage.getItem('latitudeLongitude'),
    center = latitudeLongitude ? JSON.parse(latitudeLongitude) : [116.478935, 39.997761];
  mapUtil.map.setCameraView({ lng: center[0], lat: center[1], alt: 4000 }, { duration: 0.1 });

  drawerRef.value.showList(0);

  //天气图层
  // mapUtil.weatherLayer = new mars3d.layer.GraphicLayer();
  // mapUtil.map.addLayer(mapUtil.weatherLayer);
  // addWetherGraphic(mapUtil.weatherLayer, center);

  console.log('中心点', mapUtil.map.getCenter);

  addWebSocketListener();

  console.log('', layerInfoChild.value);
  layerInfoChild.value.getLayerData();
});

onUnmounted(() => {
  console.log('unmount and destroy map...');

  // airSpace.interval && clearInterval(airSpace.interval);
  mapUtil.map?.destroy();
  mapUtil.map = null;

  if (drawerRef.value.interval != null) {
    clearInterval(drawerRef.value.interval);
  }

  // airSpace.interval && clearInterval(airSpace.interval);
  console.log('unmount and destroy map complete!');
});
</script>

<style lang="less" scoped>
#map {
  width: 100%;
  height: 100%;
}
.Container {
  width: 100%;
  height: 100%;
  position: relative;
}
.Container > * {
  min-width: 30px;
  position: absolute;
  top: 0;
  height: 100%;
}
.Row {
  display: flex;
  flex-direction: row;
}
.roW {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
}
.Col {
  display: flex;
  flex-direction: column;
}
.coL {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}
.Row.Center,
.roW.Center,
.Col.Center,
.col.Center {
  justify-content: center !important;
}
.Row.ICenter,
.roW.ICenter,
.Col.ICenter,
.col.ICenter {
  align-items: center !important;
}
.Row.ITop,
.roW.ITop,
.Col.ILeft,
.col.ILeft {
  align-items: flex-start !important;
}

.Flex {
  flex: 1;
}
.Wrap {
  flex-wrap: wrap;
}
.Scroll {
  overflow: auto;
}

.Aside {
  background-color: white;
  position: relative;
}
#drawer {
  width: 395px;
  align-items: stretch;
}
#drawer .Scroll {
  position: relative;
}
#drawer .Tabs > * {
  flex-grow: 1;
  align-items: center;
  justify-content: center;
  padding: 10px;
  height: auto;
  border: none;
  background: linear-gradient(#fcfcfc, #f5f5f5);
  border-radius: 0;
}
#drawer .Tabs > .Focus {
  background: white;
}
#drawer > div > .Title {
  padding: 10px 20px;
  color: #333333;
  font-size: 16px;
  border-bottom: 1px solid lightgrey;
}
#drawer > div > .Title > img {
  margin-right: 12px;
}

#drawer + span {
  display: flex;
  width: 30px;
  height: 30px;
  position: absolute;
  top: 3.5em;
  right: 0;
  z-index: 99;
  justify-content: center;
  align-items: center;
}

/deep/ #backscreen > .Video {
  position: absolute;
  overflow: hidden;
  height: 100%;
  width: 100%;
  background: black;
  color: white;
  opacity: 0.9;
}
/deep/ #backscreen > .Video > section:not(:last-child) {
  position: absolute;
  top: 0;
  width: 100%;
  background: transparent;
  align-items: center;
  padding: 8px;
  pointer-events: none;
}

/deep/ #backscreen > .Video > section:last-child {
  position: absolute;
  bottom: 0;
  padding: 0 2em 7em 2em;
  width: 100%;
  color: white;
}

/deep/ .Video > section img {
  margin: 0 4px;
  pointer-events: auto;
}
/deep/ .Video .Btn {
  pointer-events: auto;
  min-width: 64px;
  font-size: 12px;
  padding: 4px 8px;
  margin: 0 4px;
  color: whitesmoke;
  background: linear-gradient(180deg, #8f8b8b 0%, rgba(70, 76, 94, 0.95) 100%);
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  opacity: 0.4;
  text-align: center;
}
/deep/ .Video .State > i {
  display: flex;
  background: red;
  width: 8px;
  height: 8px;
}
/deep/ .jessibuca-container {
  position: absolute !important;
}

.Battery {
  background-image: url('/mapView/电池true.svg');
  background-repeat: no-repeat;
  height: 12px;
  width: 24px;
  margin: auto;
  padding-right: 6px;
}
.Battery.Low {
  background-image: url('/mapView/电池false.svg');
}
.Battery > span {
  max-width: 100%;
  background: green;
  margin: 2px 0 3px 0;
}

#drawer .MarkerDrop {
  height: 40px;
  background: rgba(234, 46, 46, 0.04);
  border: 1px solid rgba(234, 46, 46, 0.5);
  font-weight: 400;
  font-size: 12px;
  color: #ea2e2e;
  line-height: 17px;
  font-style: normal;
}

#backscreen {
  position: relative;
  height: 100%;
  overflow: hidden;
}
#backscreen > .Remote {
  position: absolute;
  top: 1em;
  left: 1em;
  opacity: 80%;
  z-index: 9;
}
#backscreen > .Remote:hover {
  opacity: 100%;
}

#mapmode {
  position: absolute;
  bottom: 6em;
  right: 1em;
  opacity: 80%;
  z-index: 9;
  background: #000;
  border-radius: 5px;
}
#mapmode:hover {
  opacity: 100%;
}

#layer-tree-btn {
  position: absolute;
  bottom: 12em;
  right: 1em;
  opacity: 80%;
  z-index: 9;
  background: #000;
  border-radius: 5px;
}
#layer-tree-btn:hover {
  opacity: 100%;
}
#recognitionBox {
  position: absolute;
  top: 6em;
  right: 1em;
  z-index: 9;
  width: 57px;
  height: 52px;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 4px;
  backdrop-filter: blur(10px);

  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-around;
  .dot {
    width: 6px;
    height: 6px;
    background: #2eebbd;
    border: 1px solid #6fffdb;
    border-radius: 50%;
  }
}
.TurnAiListBox {
  position: absolute;
  bottom: 7em;
  left: 1em;
  z-index: 9;
  display: flex;
  gap: 12px;
  .list {
    background: rgba(56, 119, 255, 0.6);
    border-radius: 2px;
    border: 1px solid #3877ff;
    font-weight: 400;
    font-size: 14px;
    color: #ffffff;
    padding: 6px 19px;
  }
}
#backscreen > .Swap {
  position: absolute;
  width: 100%;
  height: 100%;
  background: black;
} //地图容器

#filechooser {
  width: 1px;
  height: 1px;
  position: absolute;
}

img.Btn {
  opacity: 80%;
}
img.Btn:hover {
  opacity: 100%;
}

.Hidden {
  display: none !important;
}
.Transparent {
  opacity: 0;
}
.ReadOnly {
  pointer-events: none;
  color: gray;
}

/deep/ .image-count {
  width: 52px;
  height: 43px;
  border-radius: 4px;
  border: 1px solid #ffffff;
}

/deep/ .image-count-box {
  width: 20px;
  height: 20px;
  background-color: #3498db;
  color: #fff;
  text-align: center;
  line-height: 20px;
  border-radius: 5px;
  cursor: pointer;
}
.takeOffPoint {
  position: fixed;
  width: 800px;
  height: 70px;
  color: #fff;
  background-color: rgba(0, 0, 0, 0.5);
  top: 300px;
  margin-left: calc(100% - 300px - 400px - (100% - 700px) / 2 - 400px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 99;
}
.tabBar {
  width: 32px;
  height: 160px;
  cursor: pointer;
  background-color: #3c3c3c;
  position: fixed;
  // left: 419px;
  z-index: 9;
  border-radius: 2px 2px 0 0;
  top: calc(48%);
  .tabBar-item {
    border-radius: 2px 2px 0 0;
    height: 32px;
    width: 32px;
    text-align: center;
    color: #fff;
    font-size: 14px;
    line-height: 32px;
  }
  .tabBar-item:hover {
    background: #5d5f61;
  }
  .tabBar-item-bg {
    background: #2d8cf0;
  }
}
</style>
