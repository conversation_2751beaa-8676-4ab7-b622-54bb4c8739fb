import { defHttp } from '/@/utils/http/axios';

enum Api {
  add = '/uav/markCategory/add',
  getList = '/uav/markCategory/list',
  delete = '/uav/markCategory/delete',
  edit = '/uav/markCategory/edit',
  getParentList = '/uav/markCategory/parentList', //标签从上级部门获取
  addBatch = '/uav/markCategory/addBatch', //批量添加标签
}

export const tagManageApi = {
  add: (params) => defHttp.post({ url: Api.add, params }),
  getList: (params) => defHttp.get({ url: Api.getList, params }),
  delete: (params) => defHttp.delete({ url: `${Api.delete}?id=${params}` }),
  edit: (params) => defHttp.put({ url: Api.edit, params }),
  getParentList: (params) => defHttp.get({ url: Api.getParentList, params }),
  addBatch: (params) => defHttp.post({ url: Api.addBatch, params }),
};
