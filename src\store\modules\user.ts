import type { UserInfo, LoginInfo } from '/#/store';
import type { ErrorMessageMode } from '/#/axios';
import { defineStore } from 'pinia';
import { store } from '/@/store';
import { RoleEnum } from '/@/enums/roleEnum';
import { PageEnum } from '/@/enums/pageEnum';
import { ROLES_KEY, TOKEN_KEY, USER_INFO_KEY, LOGIN_INFO_KEY, DB_DICT_DATA_KEY, TENANT_ID } from '/@/enums/cacheEnum';
import { getAuthCache, setAuthCache, removeAuthCache } from '/@/utils/auth';
import { GetUserInfoModel, LoginParams, ThirdLoginParams } from '/@/api/sys/model/userModel';
import { doLogout, getUserInfo, loginApi, phoneLoginApi, thirdLogin, getUserConfig } from '/@/api/sys/user';
import { useI18n } from '/@/hooks/web/useI18n';
import { useMessage } from '/@/hooks/web/useMessage';
import { router } from '/@/router';
import { usePermissionStore } from '/@/store/modules/permission';
import { RouteRecordRaw } from 'vue-router';
import { PAGE_NOT_FOUND_ROUTE } from '/@/router/routes/basic';
import { isArray } from '/@/utils/is';
import { useGlobSetting } from '/@/hooks/setting';
import { JDragConfigEnum } from '/@/enums/jeecgEnum';
import { useSso } from '/@/hooks/web/useSso';
import { selectProject, querySysUserSetByUserId, saveSysUserSet, queryById } from '/@/views/projectManage/projectManage/data.api';
import { renderPlatLogo } from '/@/utils/common/compUtils';

interface UserState {
  userInfo: Nullable<UserInfo>;
  token?: string;
  roleList: RoleEnum[];
  dictItems?: [];
  sessionTimeout?: boolean;
  lastUpdateTime: number;
  tenantid?: string | number;
  loginInfo?: Nullable<LoginInfo>;
}

export const useUserStore = defineStore({
  id: 'app-user',
  state: (): UserState => ({
    // 用户信息
    userInfo: null,
    // token
    token: undefined,
    // 角色列表
    roleList: [],
    // 字典
    dictItems: [],
    // session过期时间
    sessionTimeout: false,
    // Last fetch time
    lastUpdateTime: 0,
    //租户id
    tenantid: '',
    //登录返回信息
    loginInfo: null,
  }),
  getters: {
    getUserInfo(): UserInfo {
      return this.userInfo || getAuthCache<UserInfo>(USER_INFO_KEY) || {};
    },
    getLoginInfo(): LoginInfo {
      return this.loginInfo || getAuthCache<LoginInfo>(LOGIN_INFO_KEY) || {};
    },
    getToken(): string {
      return this.token || getAuthCache<string>(TOKEN_KEY);
    },
    getAllDictItems(): [] {
      return this.dictItems || getAuthCache(DB_DICT_DATA_KEY);
    },
    getRoleList(): RoleEnum[] {
      return this.roleList.length > 0 ? this.roleList : getAuthCache<RoleEnum[]>(ROLES_KEY);
    },
    getSessionTimeout(): boolean {
      return !!this.sessionTimeout;
    },
    getLastUpdateTime(): number {
      return this.lastUpdateTime;
    },
    getTenant(): string | number {
      return this.tenantid || getAuthCache<string | number>(TENANT_ID);
    },
  },
  actions: {
    setToken(info: string | undefined) {
      this.token = info ? info : ''; // for null or undefined value
      setAuthCache(TOKEN_KEY, info);
    },
    setRoleList(roleList: RoleEnum[]) {
      this.roleList = roleList;
      setAuthCache(ROLES_KEY, roleList);
    },
    setUserInfo(info: UserInfo | null) {
      this.userInfo = info;
      this.lastUpdateTime = new Date().getTime();
      setAuthCache(USER_INFO_KEY, info);
    },
    setLoginInfo(info: LoginInfo | null) {
      this.loginInfo = info;
      setAuthCache(LOGIN_INFO_KEY, info);
    },
    setAllDictItems(dictItems) {
      this.dictItems = dictItems;
      setAuthCache(DB_DICT_DATA_KEY, dictItems);
    },
    setTenant(id) {
      this.tenantid = id;
      setAuthCache(TENANT_ID, id);
    },
    setSessionTimeout(flag: boolean) {
      this.sessionTimeout = flag;
    },
    resetState() {
      this.userInfo = null;
      this.dictItems = [];
      this.token = '';
      this.roleList = [];
      this.sessionTimeout = false;
    },
    /**
     * 登录事件
     */
    async login(
      params: LoginParams & {
        goHome?: boolean;
        mode?: ErrorMessageMode;
      }
    ): Promise<GetUserInfoModel | null> {
      try {
        const { goHome = true, mode, ...loginParams } = params;
        const data = await loginApi(loginParams, mode);
        const { token, userInfo } = data;
        // save token
        this.setToken(token);
        this.setTenant(userInfo.loginTenantId);
        return this.afterLoginAction(goHome, data);
      } catch (error) {
        return Promise.reject(error);
      }
    },
    /**
     * 扫码登录事件
     */
    async qrCodeLogin(token): Promise<GetUserInfoModel | null> {
      try {
        // save token
        this.setToken(token);
        return this.afterLoginAction(true, {});
      } catch (error) {
        return Promise.reject(error);
      }
    },
    /**
     * 登录完成处理
     * @param goHome
     */
    async afterLoginAction(goHome?: boolean, data?: any, url?: string = null): Promise<any | null> {
      if (!this.getToken) return null;
      //获取用户信息
      const userInfo = await this.getUserInfoAction();

      const sessionTimeout = this.sessionTimeout;
      if (sessionTimeout) {
        this.setSessionTimeout(false);
      } else {
        const permissionStore = usePermissionStore();
        if (!permissionStore.isDynamicAddedRoute) {
          const routes = await permissionStore.buildRoutesAction();
          routes.forEach((route) => {
            router.addRoute(route as unknown as RouteRecordRaw);
          });
          router.addRoute(PAGE_NOT_FOUND_ROUTE as unknown as RouteRecordRaw);
          permissionStore.setDynamicAddedRoute(true);
        }
        await this.setLoginInfo({ ...data, isLogin: true });
        //update-begin-author:liusq date:2022-5-5 for:登录成功后缓存拖拽模块的接口前缀
        localStorage.setItem(JDragConfigEnum.DRAG_BASE_URL, useGlobSetting().domainUrl);
        //update-end-author:liusq date:2022-5-5 for: 登录成功后缓存拖拽模块的接口前缀

        const queryProject = await querySysUserSetByUserId({ type: 0, userId: userInfo.id });
        console.log(queryProject, 'queryProject6666');
        if (queryProject) {
          //查询是否 选择过项目  如果真 直接进入项目
          localStorage.setItem('Project_Id', queryProject.content);
          //设置部门树，所属部门，负责部门缓存
          this.getUserDeptConfig({ projectId: queryProject.content });
          localStorage.setItem('configure_Id', queryProject.id); //这个是配置id
          const res = await queryById({ id: queryProject.content });
          const myArray = [res.longitude, res.latitude];
          localStorage.setItem('latitudeLongitude', JSON.stringify(myArray));
          localStorage.setItem('homePageTitle', res.homePageTitle);
          if (res.projectLogo) localStorage.setItem('projectLogo', res.projectLogo);
          if (res.reportLogo) localStorage.setItem('reportLogo', res.reportLogo);
          if (res.platName) localStorage.setItem('platName', res.platName);
          console.log('888platLogo: ', window.localStorage.getItem('platLogo'));

          if (res.platLogo) localStorage.setItem('platLogo', res.platLogo);
          console.log('999platLogo: ', window.localStorage.getItem('platLogo'));

          // 设置logo的缓存后，页面上未显示，因为此函数在路由进入的时候调用，在进入项目详情之前，会调用页面中的函数设置logo，但是此时，此缓存还未写入，所以导致logo不显示，所以需要调用函数设置logo
          renderPlatLogo();

          // router.replace({ path: '/projectManage/projectDetail', query: { id: queryProject.content } })
          goHome && (await router.replace(url || PageEnum.BASE_HOME));
          console.log('选择过项目了，直接进入');
        } else {
          if (localStorage.getItem('configure_Id')) {
            localStorage.removeItem('configure_Id');
          }
          //假 进入选择项目
          const projectList = await selectProject({ userId: userInfo.id });
          if (projectList && projectList.length >= 2) {
            goHome && (await router.replace({ path: '/projectSelect', query: { userId: userInfo.id } })); //是否2个及以上项目  加载项目选择页
          } else if (projectList.length == 1) {
            //进入项目
            // goHome && (await router.replace((userInfo && userInfo.homePath) || PageEnum.BASE_HOME));
            // await saveSysUserSet({
            //   userId: userInfo.id,
            //   type: 0,
            //   content: projectList[0].id
            // })
            const res = await queryById({ id: projectList[0].id });
            const myArray = [res.longitude, res.latitude];
            localStorage.setItem('latitudeLongitude', JSON.stringify(myArray));
            localStorage.setItem('homePageTitle', res.homePageTitle);
            if (res.projectLogo) localStorage.setItem('projectLogo', res.projectLogo);
            if (res.platName) localStorage.setItem('platName', res.platName);
            // goHome && (await router.replace({ path: '/projectManage/projectDetail', query: { id: projectList[0].id } }));
            // goHome && (await router.replace(url || PageEnum.BASE_HOME));
            goHome && (await router.replace({ path: '/projectSelect', query: { userId: userInfo.id } }));
          } else {
            if (localStorage.getItem('homePageTitle')) {
              localStorage.removeItem('homePageTitle');
            }
            //进入空项目
            goHome && (await router.replace(url || (userInfo && userInfo.homePath) || PageEnum.BASE_HOME));
          }
        }
      }
      return data;
    },
    /**
     * 手机号登录
     * @param params
     */
    async phoneLogin(
      params: LoginParams & {
        goHome?: boolean;
        mode?: ErrorMessageMode;
      }
    ): Promise<GetUserInfoModel | null> {
      try {
        const { goHome = true, mode, ...loginParams } = params;
        const data = await phoneLoginApi(loginParams, mode);
        const { token } = data;
        // save token
        this.setToken(token);
        return this.afterLoginAction(goHome, data);
      } catch (error) {
        return Promise.reject(error);
      }
    },

    /**
     * 获取用户部门配置
     */
    async getUserDeptConfig(params): Promise<any> {
      // 获取用户配置
      const res = await getUserConfig(params);
      if (res) {
        //所在部门及其子部门树形结构

        // 所在部门及其子部门树形结构
        localStorage.setItem('myDepartAndChildrenTree', JSON.stringify(res.myDepartAndChildrenTree));
        //当前用户所属部门

        // 当前用户所属部门
        localStorage.setItem('myDepartList', JSON.stringify(res.myDepartList));

        //当前用户负责部门
        localStorage.setItem('myResponsibleDepartList', JSON.stringify(res.myResponsibleDepartList));

        //当前用户是否为超管
        localStorage.setItem('adminRoleFlag', JSON.stringify(res.adminRoleFlag));
      }
    },
    /**
     * 获取用户信息
     */
    async getUserInfoAction(): Promise<UserInfo | null> {
      if (!this.getToken) {
        return null;
      }
      const { userInfo, sysAllDictItems } = await getUserInfo();
      localStorage.setItem('userId', userInfo.id);
      if (userInfo) {
        const { roles = [] } = userInfo;
        if (isArray(roles)) {
          const roleList = roles.map((item) => item.value) as RoleEnum[];
          this.setRoleList(roleList);
        } else {
          userInfo.roles = [];
          this.setRoleList([]);
        }
        this.setUserInfo(userInfo);
      }
      /**
       * 添加字典信息到缓存
       * @updateBy:lsq
       * @updateDate:2021-09-08
       */
      if (sysAllDictItems) {
        this.setAllDictItems(sysAllDictItems);
      }
      return userInfo;
    },
    /**
     * 退出登录
     */
    async logout(goLogin = false) {
      if (this.getToken) {
        try {
          await doLogout();
        } catch {
          console.log('注销Token失败');
        }
      }

      // //update-begin-author:taoyan date:2022-5-5 for: src/layouts/default/header/index.vue showLoginSelect方法 获取tenantId 退出登录后再次登录依然能获取到值，没有清空
      // let username:any = this.userInfo && this.userInfo.username;
      // if(username){
      //   removeAuthCache(username)
      // }
      // //update-end-author:taoyan date:2022-5-5 for: src/layouts/default/header/index.vue showLoginSelect方法 获取tenantId 退出登录后再次登录依然能获取到值，没有清空

      this.setToken('');
      setAuthCache(TOKEN_KEY, null);
      this.setSessionTimeout(false);
      this.setUserInfo(null);
      this.setLoginInfo(null);
      this.setTenant(null);
      //update-begin-author:liusq date:2022-5-5 for:退出登录后清除拖拽模块的接口前缀
      localStorage.removeItem(JDragConfigEnum.DRAG_BASE_URL);
      //update-end-author:liusq date:2022-5-5 for: 退出登录后清除拖拽模块的接口前缀

      //如果开启单点登录,则跳转到单点统一登录中心
      const openSso = useGlobSetting().openSso;
      if (openSso == 'true') {
        await useSso().ssoLoginOut();
      }
      if (goLogin) {
        const { createMessage } = useMessage();
        createMessage.destroy();
        await router.push(PageEnum.BASE_LOGIN);
      }
    },
    /**
     * 登录事件
     */
    async ThirdLogin(
      params: ThirdLoginParams & {
        goHome?: boolean;
        mode?: ErrorMessageMode;
      }
    ): Promise<any | null> {
      try {
        const { goHome = true, mode, ...ThirdLoginParams } = params;
        const data = await thirdLogin(ThirdLoginParams, mode);
        const { token } = data;
        // save token
        this.setToken(token);
        return this.afterLoginAction(goHome, data);
      } catch (error) {
        return Promise.reject(error);
      }
    },
    /**
     * 退出询问
     */
    confirmLoginOut() {
      const { createConfirm } = useMessage();
      const { t } = useI18n();
      createConfirm({
        iconType: 'warning',
        title: t('sys.app.logoutTip'),
        content: t('sys.app.logoutMessage'),
        onOk: async () => {
          await this.logout(true);
          router.go(0);
          window.localStorage.setItem('platName', ' ');
          window.localStorage.setItem('platLogo', ' ');
          window.localStorage.setItem('Project_Id', ' ');
        },
      });
    },
  },
});

// Need to be used outside the setup
export function useUserStoreWithOut() {
  return useUserStore(store);
}
