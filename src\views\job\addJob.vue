<template>
  <BasicModal
    :width="950"
    :minHeight="[TaskType.InstantTask, TaskType.ScheduledTask, TaskType.ConditionalTask].includes(activeTab.id as number) ? 720 : 820"
    :title="getModalTitle"
    :okText="getModalOkText"
    wrapClassName="add-job-modal-wrap"
    @visible-change="handleVisibleChange"
    @register="register"
    @cancel="cancelModal"
    :maskClosable="false"
    @ok="okModal"
    :canFullscreen="false"
  >
    <div class="container">
      <div class="tabs-wrap" :class="props.pageType === PageType.Edit && 'disabled'">
        <div
          class="tabs"
          :class="activeTab.id === item.id && 'active'"
          v-for="(item, index) in taskListTabs"
          @click="handleClickChange('tabs', item)"
          :key="index"
        >
          <img class="title-icon" :src="item.icon" />
          <div class="title">{{ item.name }}</div>
          <img class="active-icon" v-if="activeTab.id === item.id" :src="item.activeIcon" />
        </div>
      </div>
      <div class="form">
        <a-form ref="formRef" v-bind="formItemLayout" name="advanced_search" class="ant-advanced-search-form" :model="formState">
          <a-row>
            <a-col :span="12">
              <a-form-item
                label="计划名称"
                name="jobName"
                style="flex-direction: column; height: 60px"
                :labelCol="{ span: 12 }"
                :wrapperCol="{ span: 22 }"
                :rules="[{ required: true, message: '请输入计划名称' }]"
              >
                <a-input v-model:value="formState.jobName" />
              </a-form-item>
              <a-form-item
                label="执行设备"
                name="deviceId"
                style="flex-direction: column; height: 60px"
                :labelCol="{ span: 12 }"
                :wrapperCol="{ span: 22 }"
                :rules="[{ required: true, message: '请输入执行设备' }]"
              >
                <a-select
                  v-model:value="formState.deviceId"
                  show-search
                  allowClear
                  style="width: 100%; height: 100%; margin-top: 12px"
                  placeholder="请输入执行设备"
                  :default-active-first-option="false"
                  :show-arrow="false"
                  :filter-option="false"
                  :not-found-content="null"
                  :options="deviceList"
                  @search="handleSearch"
                  @focus="handleFocus"
                  @change="(value) => handleSelectChange('device', value)"
                ></a-select>
              </a-form-item>
              <a-form-item :wrapperCol="{ span: 22 }">
                <div class="device-info">
                  <div class="left">
                    <img
                      :src="curDeviceRef?.deviceType ? mapViewImgUrl(`deviceModel${curDeviceRef?.deviceModel}`, 'png') : jobImgUrl('执行设备', 'svg')"
                    />
                  </div>
                  <div class="right">
                    <div class="title">{{ curDeviceRef?.deviceName || '' }}</div>
                    <div class="model">{{ curDeviceRef?.deviceModel || '' }}</div>
                    <div class="call-info">
                      <!-- <template v-if="curDeviceRef?.projectName"> -->
                      <span><img :src="mapViewImgUrl('部门')" /></span><span>{{ curDeviceRef?.projectName || '' }}</span>
                      <!-- </template> -->
                    </div>
                    <div class="call-info">
                      <span><img :src="mapViewImgUrl('电话')" /></span><span>{{ curDeviceRef?.contactPhone || '' }}</span>
                    </div>
                    <div class="call-info">
                      <span><img :src="mapViewImgUrl('人')" /></span><span>{{ curDeviceRef?.contactName || '' }}</span>
                    </div>
                  </div>
                </div>
              </a-form-item>
              <a-form-item
                label="失联动作"
                name="lossAction"
                :labelCol="{ span: 6 }"
                :wrapperCol="{ span: 16 }"
                :rules="[{ required: true, message: '请选择失联动作' }]"
              >
                <a-select v-model:value="formState.lossAction" placeholder="请选择失联动作" :options="lossActionOptions"> </a-select>
              </a-form-item>
              <a-form-item
                label="自动断点续飞"
                name="breakpoint"
                :labelCol="{ span: 6 }"
                :wrapperCol="{ span: 16, justify: 'end' }"
                :rules="[{ required: true }]"
              >
                <a-switch v-model:checked="formState.breakpoint" />
              </a-form-item>
              <a-form-item
                label="自动建模"
                name="isAutoModeling"
                :labelCol="{ span: 6 }"
                :wrapperCol="{ span: 16, justify: 'end' }"
                :rules="[{ required: true }]"
                v-if="showBuildModel && hasPermission('system:job:AutomaticModeling')"
              >
                <a-switch v-model:checked="formState.isAutoModeling" @change="handleModelChange" />
              </a-form-item>
              <a-form-item
                label="建模类型"
                name="modelingTypeList"
                :labelCol="{ span: 6 }"
                :wrapperCol="{ span: 16, justify: 'end' }"
                :rules="[{ required: true, message: '请选择建模类型' }]"
                v-if="showCheckBoxs"
              >
                <a-checkbox-group v-model:value="formState.modelingTypeList" :options="plainOptions" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item :wrapperCol="{ span: 24 }" style="height: 30px; margin-bottom: 0px">
                <a-row class="wayLineWrap">
                  <a-col :span="10">
                    <a-form-item label="执行航线" name="wayLineId" class="wayLineId" :rules="[{ required: true, message: '请输入执行航线' }]">
                      <a-select
                        v-model:value="formState.wayLineId"
                        show-search
                        allowClear
                        class="wayLine-select-wrap"
                        placeholder="请输入执行航线"
                        :default-active-first-option="false"
                        :show-arrow="false"
                        :filter-option="false"
                        :not-found-content="null"
                        :options="wayLineList"
                        @focus="handleWayLineFocus"
                        @search="handleWayLineSearch"
                        @change="(value) => handleSelectChange('wayLine', value)"
                      ></a-select>
                    </a-form-item>
                  </a-col>
                  <a-col :span="14">
                    <div class="wayLineTextWrap">
                      <div class="wayLineText">
                        <img :src="jobImgUrl('定位')" />
                        <Tooltip :title="dronelist[curWayLineRef?.droneModel] || '未知无人机'">
                          <div class="wayLineTextItem">
                            <img :src="jobImgUrl('无人机型号')" /><span class="text">{{ dronelist[curWayLineRef?.droneModel] || '未知无人机' }}</span>
                          </div>
                        </Tooltip>
                        <Tooltip
                          :title="
                            (payloadlist[curWayLineRef?.droneModel] || []).filter((obj) => obj.code == curWayLineRef?.payloadModel)[0]?.model ||
                            '未知相机'
                          "
                        >
                          <div class="wayLineTextItem camera">
                            <img :src="jobImgUrl('相机')" /><span class="text">{{
                              (payloadlist[curWayLineRef?.droneModel] || []).filter((obj) => obj.code == curWayLineRef?.payloadModel)[0]?.model ||
                              '未知相机'
                            }}</span>
                          </div>
                        </Tooltip>
                      </div>
                    </div>
                  </a-col>
                </a-row>
              </a-form-item>
              <!--显示地图-->
              <a-form-item style="flex-direction: column; width: 100%; height: 339px">
                <!--TODO: 地图画航线, 等待杨柳处理！-->
                <div class="top-bg"></div>
                <div id="map-container" class="map-container"> </div>
              </a-form-item>
              <a-form-item
                label="完成动作"
                name="finishAction"
                :labelCol="{ span: 6 }"
                :wrapperCol="{ span: 16 }"
                :rules="[{ required: true, message: '请选择完成动作' }]"
              >
                <a-select v-model:value="formState.finishAction" placeholder="请选择完成动作" :options="finishActionOptions"> </a-select>
              </a-form-item>
              <a-form-item
                label="相对机场返航高度"
                name="alt"
                :labelCol="{ span: 8 }"
                :wrapperCol="{ span: 14 }"
                :rules="[{ required: true, message: '请输入相对机场返航高度' }]"
              >
                <a-input-number style="width: 100%" min="20" max="1500" v-model:value="formState.alt" addon-after="m"></a-input-number>
              </a-form-item>
              <a-form-item label="AI开关" name="aiSwitch" :labelCol="{ span: 3 }" :wrapperCol="{ span: 19 }" v-if="hasPermission('system:job:AI')">
                <div class="AI-Switch">
                  <div class="aiTitle">
                    <img class="aiIcon" @click="setAi" :src="jobImgUrl('设置', 'svg')" />
                    <div>{{ labelListName || '' }}</div>
                  </div>
                  <a-switch v-model:checked="formState.isAi" :checkedValue="1" :unCheckedValue="0" @change="aiSwitchChange" />

                  <div class="selectAI" v-show="aiSwitch" @mouseleave="hideSelectAI">
                    <div class="selectAI-Title"> 选择AI <span>每次任务最多使用3种AI</span> </div>
                    <div class="selectAI-checkbox">
                      <div class="checkboxList">
                        <a-checkbox-group
                          name="checkboxgroup"
                          v-model:value="selectedOptions"
                          @change="aiAlgorithmChange"
                          :options="aiAlgorithmOptions"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row style="margin: 10px 0px" justify="space-around" v-if="[1, 2, 3, 4].includes(activeTab.id as number)">
            <a-col :span="10">
              <div class="second-mod icon"><span class="task-icon"></span></div>
            </a-col>
            <a-col :span="2">
              <div class="second-mod">{{ activeTab.name }}</div>
            </a-col>
            <a-col :span="10">
              <div class="second-mod icon"><span class="task-icon"></span></div>
            </a-col>
          </a-row>
          <a-row
            v-if="[TaskType.ScheduledTask, TaskType.ConditionalTask, TaskType.RepeatingTask, TaskType.InstantTask].includes(activeTab.id as number)"
            style="background: rgba(21, 91, 129, 0.04)"
          >
            <!-- 即时任务 -->
            <template v-if="true">
              <a-col :span="12">
                <a-form-item label="喊话器" name="speakerType" :labelCol="{ span: 6 }" :wrapperCol="{ span: 16, justify: 'end' }">
                  <a-switch v-model:checked="LoudspeakerStatus" @change="LoudspeakerStatusChange" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <div class="LoadedText" v-show="formState.speakerType != -1">
                  <a-select class="statusSwitching" @change="speakerTypeChange" v-model:value="formState.speakerType">
                    <a-select-option :value="0">文本</a-select-option>
                    <a-select-option :value="1">语音</a-select-option>
                    <a-select-option :value="-2">录音</a-select-option>
                  </a-select>
                  <div class="inputOneBox" v-show="formState.speakerType == 0">
                    <a-input
                      class="inputOne"
                      @focus="handleFocusInput"
                      @blur="handleBlur"
                      show-count
                      v-model:value="formState.speakerContent"
                      :maxlength="100"
                    />
                    <div class="historyBox" v-show="historyBoxShow && historyList.length > 0">
                      <div class="historyItem" :title="item?.content" @click.stop="historyItemCilck(item.content)" v-for="item in historyList">{{
                        item.content
                      }}</div>
                    </div>
                  </div>

                  <div v-show="formState.speakerType == 1 && UploadedVoiceData == null" class="uploadingBox" @click="clickUpload">
                    <img :src="jobImgUrl('上传', 'svg')" alt="" />
                    上传文件
                  </div>
                  <div v-show="formState.speakerType == 1 && UploadedVoiceData" class="voiceBox">
                    <div class="voiceLeft">
                      <img :src="jobImgUrl('MP3Two', 'svg')" />
                      <div class="title">
                        {{ UploadedVoiceData?.title }}
                      </div>
                    </div>
                    <div class="voiceRight">
                      <img :src="jobImgUrl('列表播放', 'svg')" @click="PlayAudio" alt="" />
                      <a-popconfirm title="确认删除吗？" @confirm="voiceDeletion">
                        <CloseOutlined class="close" />
                      </a-popconfirm>
                    </div>
                  </div>

                  <div class="phoneticTranscription" v-show="formState.speakerType == -2">
                    <div
                      class="StartRecordingBox"
                      v-show="!recordProgress && audioFile == null && formState.speakerContent == ''"
                      @click="StartRecording"
                    >
                      语音录制
                    </div>
                    <div class="recordingBox" v-show="recordProgress">
                      <div class="recordingLeft">
                        <div class="dot"></div>
                        <div class="text"
                          >录制中<span class="num">{{ formattedTime }}</span></div
                        >
                      </div>
                      <div class="recordingRight" @click="stopRecording">
                        <div class="dot"></div>
                      </div>
                    </div>
                    <div
                      class="voiceBoxTwo"
                      v-show="(!recordProgress && audioFile != null) || (audioFileName != '' && formState.speakerContent != '')"
                    >
                      <div class="voiceLeft">
                        <img :src="jobImgUrl('MP3Two', 'svg')" />
                        <div class="title">
                          {{ audioFileName }}
                        </div>
                      </div>
                      <div class="voiceRight">
                        <img :src="jobImgUrl('列表播放', 'svg')" @click="playRecorder" />
                        <a-popconfirm title="确认删除吗？" @confirm="destroyRecorder">
                          <CloseOutlined class="close" />
                        </a-popconfirm>
                      </div>
                    </div>
                  </div>

                  <input type="file" style="display: none" @change="handleFileChange" :multiple="false" ref="fileInput" />
                </div>
              </a-col>
            </template>
            <!--定时任务-->
            <template v-if="activeTab.id === TaskType.ScheduledTask">
              <a-col :span="12">
                <a-form-item
                  label="执行时间"
                  :name="['jobTimeDto', 'time']"
                  style="flex-direction: column; height: 60px"
                  :labelCol="{ span: 12 }"
                  :wrapperCol="{ span: 22 }"
                  :rules="[{ required: true, message: '请输入执行时间' }]"
                >
                  <a-date-picker
                    style="width: 100%"
                    v-model:value="formState.jobTimeDto.time"
                    showTime
                    format="YYYY-MM-DD HH:mm:ss"
                    value-format="YYYY-MM-DD HH:mm:ss"
                  />
                </a-form-item>
              </a-col>
            </template>
            <!--重复任务-->
            <template v-if="activeTab.id === TaskType.RepeatingTask">
              <a-col :span="12">
                <a-form-item
                  label="日期范围"
                  name="dateRange"
                  style="flex-direction: column; height: 60px"
                  :labelCol="{ span: 12 }"
                  :wrapperCol="{ span: 22 }"
                  :rules="[{ required: true, message: '请输入日期范围' }]"
                >
                  <a-range-picker style="width: 100%" v-model:value="formState.dateRange" format="YYYY-MM-DD" value-format="YYYY-MM-DD" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item
                  label="执行频率"
                  :name="['jobTimeDto', 'timeType']"
                  style="flex-direction: column; height: 60px"
                  :labelCol="{ span: 12 }"
                  :wrapperCol="{ span: 22 }"
                  :rules="[{ required: true, message: '请选择执行频率' }]"
                >
                  <a-select
                    style="width: 100%; margin-top: 5px"
                    v-model:value="formState.jobTimeDto.timeType"
                    :options="timeTypeOptions"
                    @change="(value) => handleSelectChange('timeType', value)"
                  ></a-select>
                </a-form-item>
                <template v-if="formState.jobTimeDto.timeType == 2">
                  <a-form-item
                    :name="['jobTimeDto', 'weekDays']"
                    style="width: 100%"
                    :wrapperCol="{ span: 22 }"
                    :rules="[{ required: true, message: '请选择' }]"
                  >
                    <div class="weekDayWrap" v-if="formState.jobTimeDto.timeType == 2">
                      <div
                        class="weekDayItem"
                        :class="formState.jobTimeDto.weekDays?.length && formState.jobTimeDto.weekDays.includes(item.value) && 'active'"
                        v-for="(item, index) in weekOptions"
                        @click="handleClickChange('weekDays', item)"
                        :key="index"
                      >
                        {{ item.label }}
                      </div>
                    </div>
                  </a-form-item>
                </template>
                <a-form-item
                  label="执行时间"
                  :name="['jobTimeDto', 'time']"
                  style="flex-direction: column; height: 60px"
                  :labelCol="{ span: 12 }"
                  :wrapperCol="{ span: 22 }"
                  :rules="[{ required: true, message: '请选择执行时间' }]"
                >
                  <a-time-picker style="width: 100%" v-model:value="formState.jobTimeDto.time" format="HH:mm:ss" value-format="HH:mm:ss" />
                </a-form-item>
              </a-col>
            </template>
            <!--条件任务-->
            <template v-if="activeTab.id === TaskType.ConditionalTask">
              <a-col :span="12">
                <a-form-item
                  label="执行条件"
                  style="flex-direction: column; height: 60px"
                  :labelCol="{ span: 12 }"
                  :wrapperCol="{ span: 23 }"
                  :rules="[{ required: true }]"
                >
                  <a-row style="height: 30px">
                    <a-col :span="2" style="display: flex; justify-content: center; margin-top: 8px">当</a-col>
                    <a-col :span="9">
                      <a-form-item
                        :name="['jobConditionDto', 'conditionId']"
                        style="margin-bottom: 0px"
                        :rules="[{ required: true, message: '请选择条件' }]"
                      >
                        <a-select
                          style="width: 100%; margin-top: 5px"
                          v-model:value="formState.jobConditionDto.conditionId"
                          :options="conditionList"
                          @change="(value) => handleSelectChange('conditionId', value)"
                        ></a-select>
                      </a-form-item>
                    </a-col>
                    <a-col :span="9">
                      <a-form-item
                        :name="['jobConditionDto', 'triggerValue']"
                        style="margin-bottom: 0px"
                        :rules="[{ required: true, message: '请选择条件的取值' }]"
                      >
                        <a-select
                          style="width: 100%; margin-top: 5px"
                          v-model:value="formState.jobConditionDto.triggerValue"
                          :options="valueInfoList"
                        ></a-select>
                      </a-form-item>
                    </a-col>
                    <a-col :span="4" style="display: flex; justify-content: center; margin-top: 8px">触发计划</a-col>
                  </a-row>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item
                  label="日期范围"
                  name="dateRange"
                  style="flex-direction: column; height: 60px"
                  :labelCol="{ span: 12 }"
                  :wrapperCol="{ span: 22 }"
                  :rules="[{ required: true, message: '请输入日期范围' }]"
                >
                  <a-range-picker style="width: 100%" v-model:value="formState.dateRange" format="YYYY-MM-DD" value-format="YYYY-MM-DD" />
                </a-form-item>
              </a-col>
            </template>
          </a-row>
        </a-form>
      </div>
    </div>
  </BasicModal>
</template>
<script lang="ts" setup>
import { inject, onMounted, toRaw, computed, defineProps, watch, ref, reactive, defineEmits, nextTick } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import * as mars3d from 'mars3d';
import * as Cesium from 'mars3d-cesium';
import poiNum from '@/views/wayline/poi-num.png';
import defaultMapConfig from '@/views/mapView/map3d.config.json';
import { useBasemapConfig } from '@/hooks/web/useBasemapConfig';
import ApiListOptions from '/@/api/type';
import { deviceApi } from '/@/views/equipManage/deviceManageList/data.api';
import { waylineApi } from '/@/views/wayline/data.api';
import type { SelectProps } from 'ant-design-vue';
import { Tooltip } from 'ant-design-vue';
import { useMessage } from '/@/hooks/web/useMessage';
import AMapLoader from '@amap/amap-jsapi-loader';
import coordtransform from 'coordtransform';
import { getIconUrl } from '/@/utils';
import { PageType, TaskType } from '/@/constants/job';
import { loudSpeakApi } from '/@/views/remoteOver/data.api';
import { CloseOutlined } from '@ant-design/icons-vue';
import Recorder from 'js-audio-recorder';
import { aiAlgorithmManagementApi } from '/@/views/components/aiSetup/data.api';
import { usePermission } from '/@/hooks/web/usePermission';
import { waylineApiV2 } from '/@/views/wayline/data.api3';
const { hasPermission } = usePermission();

interface TabsItem {
  id: number;
  name: string;
  icon: string;
  activeIcon: string;
}

interface FormState {
  jobName: string;
  jobType: number;
  deviceId: string;
  deviceName: string;
  lossAction: number;
  breakpoint: boolean;
  isAutoModeling: boolean;
  wayLineId: string;
  modelingTypeList: any[];
  wayLineName: string;
  finishAction: number;
  alt: number | null;
  conditionId: number | null;
  conditionValue: number | null;
  dateRange: any[];
  jobTimeDto: any;
  jobConditionDto: any;
  speakerContent: string;
  speakerType: number | null;
}

const props = defineProps({
  pageType: {
    type: String,
    default: PageType.Add,
  },
  jobData: {
    // 复制和编辑时的数据
  },
  sysOrgCode:{
    type:String,
  }
});

const pageTypeList: any[] = [
  {
    type: PageType.Add,
    modalTitle: '新增计划',
  },
  {
    type: PageType.COPY,
    modalTitle: '复制计划',
  },
  {
    type: PageType.Edit,
    modalTitle: '编辑计划',
  },
];

let historyBoxShow = ref<Boolean>(false);
const templateTypes = ref('');
const showBuildModel = ref<Boolean>(false);

// 选择倾斜摄影时，需要勾选建模类型
const showCheckBoxs = computed(() => formState.value.isAutoModeling && templateTypes.value === '2');

const mapViewImgUrl = (name: string, suffix = 'svg') => {
  return `/mapView/${name}.${suffix}`; //拼接文件路径
};

const jobImgUrl = (name: string, suffix = 'png') => {
  return getIconUrl(name, 'icons/job/', suffix);
};

const taskListTabs: TabsItem[] = [
  {
    id: TaskType.InstantTask,
    name: '即时计划',
    icon: jobImgUrl('即时任务'),
    activeIcon: jobImgUrl('对勾', 'svg'),
  },
  {
    id: TaskType.ScheduledTask,
    name: '定时计划',
    icon: jobImgUrl('定时任务'),
    activeIcon: jobImgUrl('对勾', 'svg'),
  },
  {
    id: TaskType.RepeatingTask,
    name: '重复计划',
    icon: jobImgUrl('重复任务'),
    activeIcon: jobImgUrl('对勾', 'svg'),
  },
  // {
  //   id: TaskType.ConditionalTask,
  //   name: '条件计划',
  //   icon: jobImgUrl('条件任务'),
  //   activeIcon: jobImgUrl('对勾', 'svg'),
  // },
];

const lossActionOptions = ref<SelectProps['options']>([
  {
    value: 0,
    label: '继续执行',
  },
  {
    value: 1,
    label: '立即返航',
  },
]);

const finishActionOptions = ref<SelectProps['options']>([
  {
    value: 1,
    label: '自动返航',
  },
]);

const timeTypeOptions = ref<SelectProps['options']>([
  {
    value: 1,
    label: '每天',
  },
  {
    value: 2,
    label: '每周',
  },
]);

const weekOptions = ref<SelectProps['options']>([
  {
    value: 1,
    label: '周一',
  },
  {
    value: 2,
    label: '周二',
  },
  {
    value: 3,
    label: '周三',
  },
  {
    value: 4,
    label: '周四',
  },
  {
    value: 5,
    label: '周五',
  },
  {
    value: 6,
    label: '周六',
  },
  {
    value: 7,
    label: '周日',
  },
]);

const formItemLayout = {
  labelAlign: 'left',
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 16 },
  },
};
const baseFormState: FormState = {
  jobName: '',
  jobType: TaskType.InstantTask,
  deviceId: '',
  deviceName: '',
  lossAction: 1,
  breakpoint: false,
  isAutoModeling: false,
  modelingTypeList: [],
  wayLineId: '',
  wayLineName: '',
  finishAction: 1,
  alt: null,
  conditionId: null, // 条件ID
  conditionValue: null, // 条件的取值
  dateRange: [], // 日期范围
  jobTimeDto: {
    // 第2个模块参数
    timeType: 1, // 执行频率(默认每天)
    weekDays: [], // 每周设置的 第几星期
    time: '', // 执行时间
  },
  jobConditionDto: {
    conditionId: '',
    triggerValue: '',
  },
  speakerType: -1,
  speakerContent: '',
  isAi: 0,
  aiScenes: '',
};
const $http: ApiListOptions | undefined = inject('api');
const [register, { closeModal }] = useModalInner();
const { createMessage, createConfirm } = useMessage();
const emits = defineEmits(['success', 'register']);
let map = ref(); // 高德地图实例对象
let AmapObj = reactive<any>({}); // 高德地图实例对象
const activeTab = reactive<Partial<TabsItem>>({});
const formRef = ref();
const formState = ref<FormState>({ ...baseFormState });
let timeout: any = null;
const currentValue = ref();
const deviceListCache = ref<any[]>([]); // 设备列表
const deviceList = ref<any[]>([]); // 设备列表
const curDeviceRef = ref<any>(null); // 选中的设备
let timeoutWayLine: any = null;
const currentValueWayLine = ref();
const wayLineListCache = ref<any[]>([]); // 航线列表
const wayLineList = ref<any[]>([]); // 航线列表
const curWayLineRef = ref<any>(null); // 选中的航线
const conditionList = ref<any[]>([]); // 条件列表
const curConditionRef = ref<any>(null); // 选中的条件
const valueInfoList = ref<any[]>([]); // 条件列表的取值
let LoudspeakerStatus = ref<boolean>(false); //喊话器状态
//const curValueInfoRef = ref<any>(null);   // 选中的条件取值
let fileInput = ref();
let recordProgress = ref<boolean>(false); //是否正在录音
const recorder = ref();
let PlaybackAddress = ref<string>(''); //录音的回放地址
let historyList = ref<any[]>([]);
let aiSwitch = ref<boolean>(false); //AI开关

const plainOptions = [
  { label: '三维模型', value: '5' },
  { label: '点云', value: '7' },
];
const payloadlist = {
  '89': [
    { code: 42, model: 'H20' },
    { code: 43, model: 'H20T' },
    { code: 61, model: 'H20N' },
    { code: 65534, model: 'PSDK 负载' },
  ],
  '60': [
    { code: 42, model: 'H20' },
    { code: 43, model: 'H20T' },
    { code: 61, model: 'H20N' },
    { code: 65534, model: 'PSDK 负载' },
  ],
  '67': [
    { code: 52, model: 'M30双光相机' },
    { code: 53, model: 'M30T三光相机' },
    { code: 65534, model: 'PSDK 负载' },
  ],
  '77': [
    { code: 66, model: 'Mavic 3E 相机' },
    { code: 67, model: 'Mavic 3T 相机' },
    { code: 68, model: 'Mavic 3M 相机' },
    { code: 65534, model: 'PSDK 负载' },
  ],
  '91': [
    { code: 80, model: 'Matrice 3D 相机' },
    { code: 81, model: 'Matrice 3TD 相机' },
  ],
  '100': [
    { code: 98, model: 'Matrice 4D 相机' },
    { code: 99, model: 'Matrice 4TD 相机' },
  ],
};
const dronelist = { '89': 'M350 RTK', '60': 'M300 RTK', '67': 'M30/M30T', '77': 'M3E/M3T/M3M', '91': 'M3D/M3TD', '100': 'M4D/M4TD' };

const getModalTitle = computed((): string => (props.pageType ? pageTypeList.find((item) => item.type === props.pageType).modalTitle : '新增计划'));

const getModalOkText = computed((): string => (props.pageType === PageType.Edit ? '确定' : '发布'));

let selectedOptions = ref([]);
const aiAlgorithmOptions = ref([]);

//初始化录音
const initRecorder = () => {
  recorder.value = new Recorder({
    // 采样位数，支持 8 或 16，默认是16
    sampleBits: 16,
    // 采样率，支持 11025、16000、22050、24000、44100、48000，根据浏览器默认值
    sampleRate: 16000,
    // 声道，支持 1 或 2， 默认是1
    numChannels: 1,
  });
};

let labelList = ref();

initRecorder();

watch(
  () => formState.value.jobType,
  async (newValue) => {
    //console.log("更新formState.jobType--", newValue, conditionList.value)
    if (newValue === TaskType.ConditionalTask && !conditionList.value?.length) {
      initCondition();
    }
  },
  { immediate: true }
);

const handleModelChange = (checked) => {
  if (checked && templateTypes.value === '1') {
    formState.value.modelingTypeList = ['4'];
  }
};

const aiList = () => {
  console.log(curDeviceRef.value, 'curDeviceRef.value');
  aiAlgorithmManagementApi
    .getAiSceneList({ aircraftDeviceId: curDeviceRef.value.deviceList[0].id, gatewaySn: curDeviceRef.value.deviceSn, sysOrgCode:props.sysOrgCode, })
    .then((res) => {
      if (res.length > 0) {
        aiAlgorithmOptions.value = res.map((item) => {
          return {
            ...item,
            label: item.sceneName,
            value: item.sceneId,
            disabled: false,
          };
        });
      }

      console.log(res, 'res');
    })
    .catch(() => {
      aiAlgorithmOptions.value = [];
    });
  setTimeout(() => {
    aiAlgorithmChange();
  }, 500);
};

const hideSelectAI = () => {
  setTimeout(() => {
    aiSwitch.value = false;
  }, 300);
};

const aiSwitchChange = (event) => {
  if (!curDeviceRef.value) {
    formState.isAi = 0;
    return;
  }
  console.log(selectedOptions.value, event);
  if (!event) {
    selectedOptions.value = [];
  }
  if (event && selectedOptions.value.length === 0) {
    aiSwitch.value = true;
    aiList();
  }
  // console.log(curDeviceRef.value, 'curDeviceRef.value');
  // if (!curDeviceRef.value) {
  //   aiSwitch.value = false;
  //   return createMessage.error('请先选择设备');
  // }
  // if(formState.value.isAi == 0){
  //   aiSwitch.value = false;
  // }
  // aiSwitch.value = true;
  // formState.value.isAi && aiList();
  aiList();
};

const setAi = () => {
  if (!curDeviceRef.value) {
    aiSwitch.value = false;
    return createMessage.error('请先选择设备');
  }
  aiSwitch.value = !aiSwitch.value;
  aiSwitch.value && aiList();
};
let labelListName = ref();
const aiAlgorithmChange = () => {
  if (selectedOptions.value.length >= 3) {
    // 当选择了三个选项时，禁用其他选项
    aiAlgorithmOptions.value = aiAlgorithmOptions.value.map((option) => ({
      ...option,
      disabled: !selectedOptions.value.includes(option.value),
    }));
  } else {
    // 否则，允许所有选项可选
    aiAlgorithmOptions.value = aiAlgorithmOptions.value.map((option) => ({
      ...option,
      disabled: false,
    }));
  }

  getlabelListName();
};

const getlabelListName = () => {
  formState.value.aiScenes = selectedOptions.value.join(',');
  labelList.value = aiAlgorithmOptions.value.filter((option) => selectedOptions.value.includes(option.value));
  console.log(labelList.value, 'labelList.value');

  let arr = [];
  labelList.value.forEach((item) => {
    arr.push(item.sceneName);
  });

  labelListName.value = arr.join('/');
};

const LoudspeakerStatusChange = (events) => {
  console.log(curDeviceRef.value, 'curDeviceRef.value');
  if (!curDeviceRef.value) {
    LoudspeakerStatus.value = false;
    return createMessage.error('请先选择设备');
  }
  if (events) {
    formState.value.speakerType = 0;
  } else {
    formState.value.speakerType = -1;
  }
};

watch(
  () => curDeviceRef.value,
  (newValue) => {
    console.log(newValue, 'newValueID---------');
    setTimeout(() => {
      getHistoryList();
    }, 1000);
  }
);

const historyItemCilck = (item) => {
  formState.value.speakerContent = item;
  historyBoxShow.value = false;
};

const handleFocusInput = () => {
  console.log('获取到焦点');
  historyBoxShow.value = true;
};

const handleBlur = () => {
  console.log('失去了焦点');
  setTimeout(() => {
    historyBoxShow.value = false;
  }, 300);
};

const getHistoryList = (title = '') => {
  loudSpeakApi
    .list({
      deviceId: curDeviceRef.value.deviceId,
      projectId: localStorage.getItem('Project_Id'),
      title: title,
      type: 0,
      pageNo: 1,
      pageSize: 100,
      sysOrgCode:props.sysOrgCode,
    })
    .then((res) => {
      historyList.value = res.records;
      console.log(historyList.value, res, 'historyList.value');
    });
};

const clickUpload = () => {
  if (formState.value.deviceId) {
    fileInput.value.value = null;
    fileInput.value.click();
  }
};

let UploadedVoiceData = ref<Object | null>(null);

// 从 URL 获取 PCM 音频数据
async function fetchPCMData(pcmUrl) {
  try {
    const response = await fetch(pcmUrl);
    const arrayBuffer = await response.arrayBuffer();
    return arrayBuffer;
  } catch (error) {
    console.error('Failed to fetch PCM data:', error);
    return null;
  }
}

const PlayAudio = async () => {
  const pcmData = await fetchPCMData(UploadedVoiceData.value.content);
  if (pcmData) {
    convertPCMtoWAV(pcmData);
  }
};
let audioContext = null;
let audioBufferSource = null;
// 将 PCM 数据转换为 WAV 格式
function convertPCMtoWAV(pcmData) {
  if (audioContext) {
    audioContext.close(); // 关闭之前的音频上下文
  }

  audioContext = new (window.AudioContext || window.webkitAudioContext)();
  const audioBuffer = audioContext.createBuffer(1, pcmData.byteLength / 2, 16000); // 1 channel, 16000Hz
  const channelData = audioBuffer.getChannelData(0);
  const int16Array = new Int16Array(pcmData);

  for (let i = 0; i < int16Array.length; i++) {
    channelData[i] = int16Array[i] / 32768; // Normalize to [-1, 1]
  }

  if (audioBufferSource) {
    audioBufferSource.stop(); // 停止之前的音频播放
  }

  audioBufferSource = audioContext.createBufferSource();
  audioBufferSource.buffer = audioBuffer;
  audioBufferSource.connect(audioContext.destination);
  audioBufferSource.start();

  return audioBufferSource;
}

const voiceDeletion = () => {
  UploadedVoiceData.value = null;
};

const handleFileChange = (event) => {
  if (event.target.files[0].name.split('.').pop() == 'pcm' || event.target.files[0].name.split('.').pop() == 'mp3') {
    const formData = new FormData();
    let name =
      event.target.files[0].name.split('.').slice(0, -1).join('.') + JSON.stringify(Date.now()) + '.' + event.target.files[0].name.split('.').pop();

    formData.append('deviceId', formState.value.deviceId);
    formData.append('file', event.target.files[0], name);
    loudSpeakApi.upload(formData).then(
      (res) => {
        console.log('上传结果', res);
        UploadedVoiceData.value = res.data.result;
        formState.value.speakerContent = res.data.result.id;
      },
      (error) => {
        createMessage.error(error);
      }
    );
  } else {
    createMessage.error(`您上传的格式为:${event.target.files[0].name.split('.').pop()},请上传pcm或mp3格式文件`);
  }
};

const speakerTypeChange = () => {
  formState.value.speakerContent = '';
  UploadedVoiceData.value = null;
  audioFile.value = null;
  audioFileName.value = '';
  PlaybackAddress.value = '';
};

const handleVisibleChange = async (visible) => {
  if (visible) {
    // console.log("接受props", props)
    if ([PageType.Edit, PageType.COPY].includes(props.pageType)) {
      const { jobType, beginTime, endTime, aiScenes, jobConditionDto = {} } = props.jobData;
      formState.value = props.jobData as FormState;
      formState.value.dateRange = [beginTime, endTime];
      formState.value.jobConditionDto = jobConditionDto; // 返回可能为null
      activeTab.id = jobType;
      activeTab.name = taskListTabs.find((item) => item.id === activeTab.id)?.name || '';
      LoudspeakerStatus.value = props.jobData.speakerType != -1 ? true : false;

      if ([-2, 1].includes(props.jobData.speakerType)) {
        loudSpeakApi.speakeGet({ id: props.jobData.speakerContent }).then((res) => {
          console.log(res, '复制');
          if (props.jobData.speakerType == 1) {
            UploadedVoiceData.value = res;
          } else {
            audioFileName.value = res.title;
            formState.value.speakerContent = res.id;
            PlaybackAddress.value = res.content;
          }
        });
      }
      await initMap();
      await asyncInit(props.jobData);
      if (jobType === TaskType.ConditionalTask && jobConditionDto?.conditionId) {
        // 异步更新条件
        await initCondition({ id: jobConditionDto?.conditionId });
      }
      // selectedOptions.value = [];
      formState.value.isAi && aiList();
      getlabelListName();
      // aiList();
    } else {
      formState.value = { ...baseFormState };
      formState.value.jobTimeDto = baseFormState.jobTimeDto;
      await initMap();
      await initData();
    }
  }
};

// 异步更新设备/航线
const asyncInit = async (data) => {
  const { aiScenes } = props.jobData;
  handleSearch(data.deviceName);
  const result = await $http?.getDeviceGet({
    deviceId: data.deviceId,
  });
  if (result) {
    curDeviceRef.value = result;
    // const name = `dji_${result.deviceSn}`;
    // handleSelectChange('device', name);
  }
  formState.value.wayLineId = data.wayLineId.toString();
  handleWayLineSearch(data.wayLineName);
  const waylineRes = await waylineApi.get({
    waylineId: data.wayLineId,
    workspaceId: localStorage.getItem('Project_Id'),
  });
  //console.log("waylineRes", waylineRes)
  if (waylineRes) {
    curWayLineRef.value = waylineRes;
    // 显示建模按钮
    templateTypes.value = waylineRes.templateTypes;
    showBuildModel.value = ['1', '2'].includes(templateTypes.value);

    waylineApi.resolveFile(waylineRes.fileUrl, function (waylines_wpml) {
      renderWayline({ waylines_wpml, id: curWayLineRef.value.id });
    });
  }
  // aiList();
  selectedOptions.value = aiScenes.split(',').map((item) => item);
  formState.value.aiScenes = selectedOptions.value.join(',');
  setTimeout(() => {
    // aiAlgorithmChange();
  }, 800);
};

const initCondition = async (params = {}) => {
  const conditionRes = await $http?.getConditionList({
    pageNo: 1,
    pageSize: 100,
    ...params,
  });
  if (conditionRes.records?.length) {
    const resData: any[] = conditionRes.records.map((item, index) => {
      return {
        ...item,
        value: item.id,
        label: item.conName,
      };
    });
    conditionList.value = resData;
  }
};

// 初始化设备/航线/条件
const initData = async () => {
  activeTab.id = TaskType.InstantTask;
  activeTab.name = '即时计划';
  const result = await deviceApi.list({
    deviceType: '3',
    sysOrgCode:props.sysOrgCode,
    pageNo: 1,
    pageSize: 200,
  });
  const { records = [] } = result;
  if (records?.length) {
    const resData: any[] = records.map((item, index) => {
      return {
        ...item,
        value: item.deviceId,
        label: item.deviceName,
      };
    });
    deviceListCache.value = resData;
    deviceList.value = resData;
  }
  const waylineRes = await waylineApiV2.webList({
    pageSize: 200,
    pageNo: 1,
    sysOrgCode:props.sysOrgCode,
    order: 'desc',
    column: 'createTime',
  });
  // const waylineRes = await waylineApi.query({
  //   pageNo: 1,
  //   pageSize: 200,
  // });
  if (waylineRes.records?.length) {
    const resData: any[] = waylineRes.records.map((item, index) => {
      return {
        ...item,
        value: item.id,
        label: item.name,
      };
    });
    wayLineListCache.value = resData;
    wayLineList.value = resData;
  }
};
let mapbacklayer: any = null;
const { configMap } = useBasemapConfig();
const initMap = async () => {
  try {
    if (map.value) {
      map.value.destroy();
      map.value = null;
    }
    await nextTick(); // 确保DOM更新完成

    const container = document.getElementById('map-container');
    if (!container) {
      throw new Error('Map map-container not found');
    }
    const mars3dConfig = mars3d.Util.merge(defaultMapConfig.map3d, toRaw(configMap));
    map.value = new mars3d.Map('map-container', mars3dConfig);

    mapbacklayer = new mars3d.layer.GraphicLayer({
      allowDrillPick: true, // 如果存在坐标完全相同的图标点，可以打开该属性，click事件通过graphics判断
    });
    map.value.addLayer(mapbacklayer);
    let latitudeLongitude = localStorage.getItem('latitudeLongitude'),
      center = latitudeLongitude ? JSON.parse(latitudeLongitude) : [116.478935, 39.997761];
    map.value.setCameraView({ lng: center[0], lat: center[1], alt: 4000 }, { duration: 0.1 });
  } catch (error) {
    console.error('Map initialization failed:', error);
  }
  // try {
  //   // 禁止多种API加载方式混用
  //   (AMapLoader as any).reset();
  //   const AMap = await AMapLoader.load({
  //     key: '59c61f064c552f2ace25ff0c1d6465a7', // 申请好的Web端开发者Key，首次调用 load 时必填
  //     version: '2.0', // 指定要加载的 JS API 的版本，缺省时默认为 1.4.15
  //     plugins: ['AMap.PolygonEditor'], // 需要使用的的插件列表，如比例尺'AMap.Scale'等
  //   });
  //   AmapObj = AMap;
  //   //console.log("加载完地图---------------")
  //   map.value = new AMap.Map('map-container', {
  //     center: [114.118725, 22.697947],
  //     zoom: 16,
  //     viewMode: '3D', //使用3D视图
  //   });
  // } catch (e) {
  //   console.log(e);
  // }
};

//更新地图
const handleChangeMap = (lng, lat) => {
  //console.log("更新地图==map", AmapObj, map.value)
  if (lng && lat) {
    const position = new AmapObj.LngLat(lng, lat);
    map.value.setZoomAndCenter(16, position); // 传入经纬度，设置地图中心点
  } else {
    map.value.clearMap();
  }
};

const handleSelectChange = async (type: string, value) => {
  //console.log("handleSelectChange==value", value)
  switch (type) {
    case 'timeType':
      {
        if (value === 1) {
          formState.value.jobTimeDto.weekDays = [];
        }
      }
      break;
    case 'device':
      {
        if (value) {
          curDeviceRef.value = deviceList.value.find((item) => item.deviceId === value); // 缓存选中设备
          selectedOptions.value = [];
          formState.value.isAi && aiList();
          //console.log("curDeviceRef.value", curDeviceRef.value)
        } else {
          curDeviceRef.value = null;
        }
      }
      break;
    case 'wayLine':
      {
        if (value) {
          if (currentMarker) {
            map.value.graphicLayer.removeGraphic(currentMarker); // 移除之前的标点
          }
          if (pointsList.value.length > 0) {
            pointsList.value.forEach((item) => {
              map.value.graphicLayer.removeGraphic(item); // 移除之前的标点
            });
            pointsList.value = [];
          }
          curWayLineRef.value = wayLineList.value.find((item) => item.id === value);
          console.log('curWayLineRef.value', curWayLineRef.value);
          const waylineRes = await waylineApi.get({
            waylineId: curWayLineRef.value.id,
            workspaceId: localStorage.getItem('Project_Id'),
          });
          console.log('切换航线--waylineRes', waylineRes);
          waylineApi.resolveFile(waylineRes.fileUrl, function (waylines_wpml) {
            //console.log("切换航线--waylines_wpml", waylines_wpml)
            renderWayline({ waylines_wpml, id: curWayLineRef.value.id });
          });

          // 切换航线，自动建模开关等处理逻辑
          formState.value.isAutoModeling = false;
          formState.value.modelingTypeList = [];
          templateTypes.value = waylineRes.templateTypes;
          // 0：航点，1：2d(正射), 2: 3d(倾射), 3: 带状
          showBuildModel.value = ['1', '2'].includes(templateTypes.value);
        } else {
          curWayLineRef.value = null;
        }
      }
      break;
    case 'conditionId':
      {
        if (value) {
          curConditionRef.value = conditionList.value.find((item) => item.id === value);
          valueInfoList.value = curConditionRef.value.valueInfo?.length
            ? curConditionRef.value.valueInfo.map((item) => {
                return {
                  value: item,
                  label: item,
                  key: Date.now(),
                };
              })
            : [];
          //console.log("curConditionRef.value", curConditionRef.value)
        } else {
          curConditionRef.value = null;
        }
      }
      break;
  }
};
let indexMark;
async function getMarkerImg(num) {
  if (!indexMark) {
    indexMark = await Cesium.Resource.fetchImage({ url: poiNum });
  }

  const canvas = document.createElement('canvas');
  canvas.width = 19;
  canvas.height = 25;
  const ctx:any = canvas.getContext('2d');
  ctx.clearRect(0, 0, canvas.width, canvas.height);
  ctx.drawImage(indexMark, 0, 0); // 绘制图片

  // 绘制文字
  ctx.fillStyle = '#ffffff';
  ctx.font = '12px 楷体';
  ctx.textBaseline = 'middle';
  ctx.fillText(num, num < 10 ? 6 : 3, 10);

  return canvas.toDataURL('image/png');
}
var polyEditor = null;
var currentMarker: any = null;
const position:any = ref([]);
const pointsList:any = ref([]);
const renderWayline = async (waylineInfo) => {
  position.value = [];
  const { id, waylines_wpml } = waylineInfo;
  //console.log("waylines_wpml", waylines_wpml)
  const points = waylines_wpml.Folder.Placemark || [];
  points.forEach((item) => {
    position.value.push([Number(item.lng), Number(item.lat), Number(waylines_wpml.Folder.globalHeight)]);
  });
  // if (points.length == 0) {
  //   points.push({ lng: 114.118862694324, lat: 22.6973392910372, height: 0 }, { lng: 114.1288, lat: 22.7, height: 0 });
  // }
  console.log('航点信息', points);
  // if (currentMarker) {
  //   map.value.graphicLayer.removeGraphic(currentMarker); // 移除之前的标点
  // }
  // if (pointsList.value.length > 0) {
  //   pointsList.value.forEach((item) => {
  //     map.value.graphicLayer.removeGraphic(item); // 移除之前的标点
  //   });
  //   pointsList.value = [];
  // }
  // 添加标点
  currentMarker = new mars3d.graphic.PolylineEntity({
    positions: position.value, // 位置使用经纬度
    flyTo: true, // 飞向该点
    style: {
      width: 5,
      color: '#00D590',
      horizontalOrigin: mars3d.Cesium.HorizontalOrigin.CENTER,
      verticalOrigin: mars3d.Cesium.VerticalOrigin.BOTTOM,
      // clampToGround: true,
    },
  });
  map.value.graphicLayer.addGraphic(currentMarker);
  for (let i = 0; i < position.value.length; i++) {
    const waylineBillboard = new mars3d.graphic.BillboardEntity({
      position: position.value[i],
      style: {
        image: await getMarkerImg(i + 1),
        horizontalOrigin: mars3d.Cesium.HorizontalOrigin.CENTER,
        verticalOrigin: mars3d.Cesium.VerticalOrigin.BOTTOM,
      },
    });
     map.value.graphicLayer.addGraphic(waylineBillboard);
     pointsList.value.push(waylineBillboard);
  }
  // const polyline1 = new AmapObj.Polyline({
  //   path: points.map((obj) => coordtransform.wgs84togcj02(obj.lng, obj.lat)),
  //   strokeColor: 'blue',
  //   strokeWeight: 3,
  //   strokeOpacity: 0.9,
  //   lineJoin: 'round',
  //   lineCap: 'round',
  // });
  // map.value.clearMap();
  // map.value.add([polyline1]);
  // map.value.setFitView();

  // if (polyEditor != null && polyEditor.getTarget()) {
  //   polyEditor.close();
  //   map.value && map.value.remove(polyEditor.getTarget());
  // }
  // polyEditor = new AMap.PolygonEditor(map.value, polyline1);
  // polyEditor.open();
};
const svg = (name) => {
  return `/mapView/${name}.svg`; //拼接文件路径
};
const handleClickChange = (type: string, item) => {
  //console.log("handleClickChange==item", item)
  switch (type) {
    case 'tabs':
      {
        const { id, name } = item;
        activeTab.name = name;
        activeTab.id = id;
        formState.value.jobType = id;
        formState.value.jobTimeDto = {
          timeType: 1,
          weekDays: [],
          time: '',
        };
        formState.value.jobConditionDto = {
          conditionId: '',
          triggerValue: '',
        };
      }
      break;
    case 'weekDays':
      {
        if (!formState.value.jobTimeDto.weekDays?.length) {
          formState.value.jobTimeDto.weekDays = [];
          formState.value.jobTimeDto.weekDays.push(item.value);
        } else {
          const index = formState.value.jobTimeDto.weekDays.findIndex((items) => items == item.value);
          //console.log("index", index, item.value, formState.value.jobTimeDto.weekDays)
          if (index !== -1) {
            formState.value.jobTimeDto.weekDays.splice(index, 1);
          } else {
            formState.value.jobTimeDto.weekDays.push(item.value);
          }
        }
      }
      break;
  }
};

const searchDeviceData = (value: string, callback: any) => {
  if (timeout) {
    clearTimeout(timeout);
    timeout = null;
  }

  currentValue.value = value;

  const fetch = async () => {
    const result = await deviceApi.list({
      deviceType: '3',
      deviceName: value,
      pageNo: 1,
      pageSize: 20,
    });
    //console.log("result===", result)
    const { records = [] } = result;
    if (records?.length) {
      if (currentValue.value === value) {
        const resData: any[] = records.map((item, index) => {
          return {
            ...item,
            value: item.deviceId,
            label: item.deviceName,
          };
        });
        callback(resData);
      }
    }
  };
  timeout = setTimeout(fetch, 300);
};

const handleFocus = () => {
  //console.log("获取焦点----")
  if (deviceListCache.value?.length) {
    deviceList.value = deviceListCache.value;
  } else {
    initData();
  }
};

const handleSearch = (val: any) => {
  //console.log("handleSearch--data", val)
  if (val) {
    searchDeviceData(val, (d: any[]) => (deviceList.value = d));
  }
};

const searchWayLineData = (value: string, callback: any) => {
  if (timeoutWayLine) {
    clearTimeout(timeoutWayLine);
    timeoutWayLine = null;
  }

  currentValueWayLine.value = value;

  const fetch = async () => {
    const result = await waylineApiV2.webList({
      name: value,
      pageNo: 1,
      pageSize: 20,
    });
    //console.log("result===", result)
    const { records = [] } = result;
    if (records?.length) {
      if (currentValueWayLine.value === value) {
        const resData: any[] = records.map((item, index) => {
          return {
            ...item,
            value: item.id,
            label: item.name,
          };
        });
        callback(resData);
      }
    }
  };
  timeoutWayLine = setTimeout(fetch, 300);
};

const handleWayLineFocus = () => {
  //console.log("获取焦点--航线--")
  if (wayLineListCache.value?.length) {
    wayLineList.value = wayLineListCache.value;
  } else {
    initData();
  }
};

const handleWayLineSearch = (val: any) => {
  //console.log("handleSearchWayLine--data", val)
  if (val) {
    searchWayLineData(val, (d: any[]) => (wayLineList.value = d));
  }
};

let timer: ReturnType<typeof setTimeout>;
let currentTime = ref(0);
// 将当前时间格式化为 "00:00" 形式的字符串

//开始录制
const StartRecording = () => {
  recorder.value.start().then(
    () => {
      recordProgress.value = true;
      // 每秒更新一次当前时间
      timer = setInterval(() => {
        currentTime.value++;
      }, 1000);
    },
    (error) => {
      console.error('Error starting recording:', error);
      createMessage.error(`浏览器不支持 getUserMedia !`);
    }
  );
};

const formattedTime = computed(() => {
  const minutes = Math.floor(currentTime.value / 60);
  const seconds = currentTime.value % 60;
  return `${minutes < 10 ? '0' : ''}${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
});

let audioFile = ref<Blob | null>(null);
let audioFileName = ref<string | null>(null);
//录制结束
const stopRecording = () => {
  clearInterval(timer);
  currentTime.value = 0;
  recorder.value.stop();
  audioFile.value = recorder.value.getPCMBlob();
  audioFileName.value = JSON.stringify(Date.now()) + '.pcm';
  recordProgress.value = false;
};

// 播放录音
const playRecorder = async () => {
  if (PlaybackAddress.value != '') {
    const pcmData = await fetchPCMData(PlaybackAddress.value);
    if (pcmData) {
      convertPCMtoWAV(pcmData);
    }
  } else {
    recorder.value.play();
  }
};

// 销毁录音
const destroyRecorder = () => {
  recorder.value.destroy().then(() => {
    audioFile.value = null;
    audioFileName.value = null;
  });
};

const startCounting = () => {};

const okModal = () => {
  nextTick(() => {
    formRef.value
      .validate()
      .then(async (formResult) => {
        if (formState.value.speakerType == -2 && audioFile.value != null && formState.value.speakerContent == '') {
          const formDataTwo = new FormData();
          formDataTwo.append('deviceId', formState.value.deviceId);
          formDataTwo.append('file', audioFile.value, audioFileName.value);
          await loudSpeakApi.upload(formDataTwo).then((res) => {
            formState.value.speakerContent = res.data.result.id;
          });
        }
        const { dateRange, jobTimeDto } = formState.value;
        if (jobTimeDto) {
          jobTimeDto.timeType = Number(jobTimeDto.timeType);
        }
        const formData: any = Object.assign(
          {},
          {
            ...formState.value,
            lossAction: Number(formState.value.lossAction),
            finishAction: Number(formState.value.finishAction),
            jobTimeDto: jobTimeDto,
          }
        );
        if (dateRange?.length) {
          formData.beginTime = dateRange[0];
          formData.endTime = dateRange[1];
          formData.dateRange = undefined;
        }
        if (props.pageType === PageType.COPY) {
          formData.id = undefined;
          formData.jobId = undefined;
        }

        //console.log('表单校验--通过', formData);
        if ([PageType.Add, PageType.COPY].includes(props.pageType)) {
          const payload = {
            ...formData,
            ...(props.pageType === PageType.Add ? { sysOrgCode: props.sysOrgCode } : {})
          };
          const res = await $http?.postWaylinejobSave(payload);
          const { data } = res;
          if (data.code === 200) {
            createMessage.success('操作成功');
            cancelModal();
          } else {
            createMessage.warning(data.message);
          }
        } else if (props.pageType === PageType.Edit) {
          const res2 = await $http?.postWaylinejobEdit(formData);
          const { data } = res2;
          if (data.code === 200) {
            createMessage.success('操作成功');
            cancelModal();
          } else {
            createMessage.warning(data.message);
          }
        }
      })
      .catch((err) => {
        console.log('新建/编辑计划报错', err);
      });
  });
};

const cancelModal = () => {
  //console.log('关闭弹框');
  selectedOptions.value = [];
  aiSwitch.value = false;
  formState.value = baseFormState;
  curWayLineRef.value = null;

  formRef.value.resetFields();
  emits('success');
  closeModal();
  LoudspeakerStatus.value = false;
  PlaybackAddress.value = '';
  labelList.value = [];
  labelListName.value = '';
};
</script>
<style lang="less" scoped>
/deep/.scroll-container {
  padding: 0;
}

.container {
  padding: 0 20px;
  height: 560px;

  .tabs-wrap {
    display: flex;

    &.disabled {
      cursor: not-allowed;
    }

    .tabs {
      flex-grow: 1;
      position: relative;
      width: 200px;
      height: 49px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: 15px;
      background: rgba(21, 91, 129, 0.04);

      &:last-child {
        margin-right: 0;
      }

      .title-icon {
        width: 41px;
        height: 36px;
        margin-right: 5px;
      }

      .active-icon {
        width: 15px;
        height: 15px;
        position: absolute;
        right: 0px;
        bottom: 0px;
      }

      .title {
        font-size: 16px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #333333;
      }

      &.active {
        color: #0a7bb8;
        background: rgba(21, 91, 129, 0.04);
        border-radius: 5px;
        border: 1px solid #0a7bb8;
      }
    }
  }

  .form {
    margin-top: 10px;
    width: 100%;
    height: 500px;

    .device-info {
      width: 405px;
      height: 204px;
      display: flex;
      align-items: center;
      background: #f8fbfd;
      border-radius: 4px;

      .left {
        width: 50%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;

        img {
          width: 136px;
          height: 144px;
        }
      }

      .right {
        width: 50%;
        text-align: left;

        .title {
          width: 100%;
          height: 21px;
          font-size: 15px;
          font-family: PingFangSC-Semibold, PingFang SC;
          font-weight: 600;
          color: #333333;
          line-height: 21px;
        }

        .model {
          margin-top: 8px;
          width: 100%;
          height: 18px;
          font-size: 13px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #666666;
          line-height: 18px;
        }

        .call-info {
          margin-top: 11px;
          width: 100%;
          height: 18px;
          font-size: 13px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #333333;
          line-height: 18px;
        }
      }
    }

    .flyline {
      width: 449px;
      height: 330px;
    }

    /deep/ .wayLineWrap {
      position: absolute;
      left: 0px;
      top: 0px;
      width: 99%;
      display: flex;
      color: #fff;
      z-index: 9999;
      opacity: 0.9;

      .wayLineId {
        flex-direction: column;
        height: 75px;
        margin-bottom: 0px;

        &.ant-form-item-with-help {
          margin-bottom: 0px !important;

          .ant-form-item-explain {
            margin-left: 20px;
          }
        }
      }

      .wayLineTextWrap {
        height: 100%;
        display: flex;
        justify-content: flex-end;
        align-items: flex-end;

        .wayLineText {
          width: 100%;
          height: 36px;
          padding-bottom: 10px;
          display: flex;
          justify-content: flex-end;

          .wayLineTextItem {
            display: flex;
            align-items: center;
            margin-left: 5px;
            width: 98px;

            &.camera {
            }

            .text {
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
            }
          }
        }
      }
    }

    .AI-Switch {
      display: flex;
      align-items: center;
      justify-content: space-between;
      position: relative;
      .aiTitle {
        display: flex;
        align-items: center;
        .aiIcon {
          cursor: pointer;
        }
      }

      .selectAI {
        position: absolute;
        width: 426px;
        height: 185px;
        background: rgba(0, 0, 0, 0.6);
        border-radius: 4px;
        z-index: 999;
        padding: 9px 12px;
        top: 29px;
        left: -58px;
        .selectAI-Title {
          font-weight: 500;
          font-size: 14px;
          color: #ffffff;
          border-bottom: 1px solid rgba(255, 255, 255, 0.7);
          span {
            font-weight: 400;
            font-size: 12px;
            color: rgba(255, 255, 255, 0.8);
            margin-left: 13px;
          }
        }
        .selectAI-checkbox {
          width: 100%;
          height: 100%;
          overflow: scroll;
          .checkboxList {
            width: 100%;
            height: 100%;
            :deep(.ant-checkbox-group-item > span:nth-child(2)) {
              color: #ffffff;
            }
            :deep(.ant-checkbox-group-item > span:nth-child(2)) {
              color: #ffffff;
            }
          }
        }
      }
    }

    .top-bg {
      position: absolute;
      left: 0px;
      top: 0px;
      width: 440px;
      height: 42px;
      background-color: #000;
      background: #000;
      opacity: 0.7;
      z-index: 999;
    }

    #map-container {
      width: 440px;
      height: 339px;
    }

    .map-container {
      position: relative;
    }

    .second-mod {
      width: 100%;
      height: 22px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 16px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #333333;
      line-height: 22px;

      &.icon {
        width: 360px;

        .task-icon {
          width: 100%;
          height: 2px;
          border: 1px solid #ededed;
        }
      }
    }

    .LoadedText {
      display: flex;
      align-items: center;

      .statusSwitching {
        width: 108px;
        margin-right: 4px;
        border: 1px solid #b6ccd9 !important;
      }

      .inputOneBox {
        border: 1px solid #b6ccd9 !important;
        width: 100%;
        position: relative;

        .inputOne {
          width: 100%;
        }

        .historyBox {
          height: 100px;
          width: 100%;
          position: absolute;
          overflow: auto;
          top: 32px;
          background-color: #ffffff;
          z-index: 10;
          border: 1px solid #bdd0e0;
          .historyItem {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            width: 347px;
            padding: 5px 0;
            cursor: pointer;
          }
        }
      }

      .uploadingBox {
        border-radius: 2px;
        border: 1px dashed #b6ccd9;
        width: 100%;
        height: 32px;
        cursor: pointer;
        font-weight: 400;
        font-size: 14px;
        color: #708c9d;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .voiceBox {
        background: #ebf2f7;
        border-radius: 2px;
        border: 1px solid rgba(21, 91, 129, 0.2);
        width: 100%;
        height: 32px;
        display: flex;
        align-items: center;
        padding: 0 12px;
        justify-content: space-between;

        .voiceLeft {
          display: flex;
          align-items: center;

          .title {
            font-weight: 400;
            font-size: 14px;
            color: #666666;
            margin-left: 6px;

            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            width: 245px;
          }
        }

        .voiceRight {
          display: flex;
          align-items: center;
          gap: 14px;

          .close {
            color: #80a7bc;
          }
        }
      }

      .phoneticTranscription {
        width: 100%;
        height: 32px;
        background-color: #f2f9fd;
        border-radius: 2px;
        border: 1px solid #72c7f4;
        display: flex;
        align-items: center;
        justify-content: center;

        .StartRecordingBox {
          font-weight: 400;
          font-size: 14px;
          color: #28b1f9;
          width: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }

      .recordingBox {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        padding: 0 13px;

        .recordingLeft {
          display: flex;
          align-items: center;

          .dot {
            width: 7px;
            height: 7px;
            border-radius: 50%;
            background-color: #ff3c3c;
            margin-right: 13px;
          }

          .text {
            font-weight: 400;
            font-size: 14px;
            color: #333333;

            .num {
              margin-left: 11px;
            }
          }
        }

        .recordingRight {
          width: 19px;
          height: 19px;
          background: rgba(255, 60, 60, 0.1);
          border-radius: 50%;
          border: 1px solid #ff3c3c;
          position: relative;
          cursor: pointer;

          .dot {
            width: 7px;
            height: 7px;
            background: #ff3c3c;
            border-radius: 1px;
            border: 1px solid #ff7878;
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
          }
        }
      }

      .voiceBoxTwo {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .voiceLeft {
          display: flex;
          align-items: center;

          .title {
            font-weight: 400;
            font-size: 14px;
            color: #666666;
            margin-left: 6px;

            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            width: 245px;
          }
        }

        .voiceRight {
          display: flex;
          align-items: center;
          gap: 14px;

          .close {
            color: #80a7bc;
          }
        }
      }
    }
  }

  .weekDayWrap {
    width: 100%;
    display: flex;

    .weekDayItem {
      flex-grow: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      border: 1px solid #bbcfda;

      &.active {
        background: linear-gradient(315deg, #2c9ad6 0%, #167db5 100%);
      }
    }
  }
}
</style>
<style lang="less" scoped>
.add-job-modal-wrap {
  .ant-modal-content {
    .ant-modal-header {
      background: linear-gradient(135deg, #073451 0%, #0b4c68 100%);

      .jeecg-basic-title {
        color: #fff;
      }
    }

    .ant-modal-close-x {
      color: #fff;
    }

    .ant-modal-body {
      .ant-form-item.ant-form-item-with-help {
        margin-bottom: 36px !important;
      }

      .ant-select.wayLine-select-wrap {
        width: 100%;
        margin-top: 4px;
        // color: #fff;

        .ant-select-selector {
          margin-left: 10px;
          background-color: transparent;
          border-radius: 15px;
          opacity: 0.8;
          border: 1px solid #fff;

          .ant-select-clear {
            background: none;
          }
        }

        .ant-select-clear {
          color: #fff;
          background: none;
        }
      }
    }
  }
}
</style>
