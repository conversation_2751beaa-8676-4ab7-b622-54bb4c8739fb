<template>
  <div id="event-manage-wrapper">
    <div class="btn-box">
      <div class="page-title"> 事件管理 </div>
      <div>
        <a-tree-select
          v-if="treeData.length"
          v-model:value="treeValue"
          show-search
          style="margin-right: 15px; min-width: 200px"
          :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
          placeholder="请选择部门"
          allow-clear
          tree-default-expand-all
          :tree-data="treeData"
          :fieldNames="{ label: 'departName', value: 'orgCode' }"
          @change="(value) => selectChange('depart', value)"
        >
        </a-tree-select>
        <a-select class="search-item" v-model:value="queryParam.labelUser" @change="(value) => selectChange('labelUser', value)">
          <a-select-option value="">全部标注人员</a-select-option>
          <a-select-option v-for="item in markerList" :key="item.username" :value="item.username">{{ item.realname }}</a-select-option>
        </a-select>
        <a-select class="search-item" v-model:value="queryParam.handler" @change="(value) => selectChange('handler', value)">
          <a-select-option value="">全部处置人员</a-select-option>
          <a-select-option v-for="item in dealerList" :key="item.username" :value="item.username">{{ item.realname }}</a-select-option>
        </a-select>
        <a-tree-select
          :field-names="{ children: 'children', value: 'id', label: 'name' }"
          v-model:value="queryParam.labelIdList"
          style="min-width: 200px; margin-right: 15px"
          :tree-data="labelIdsOptions"
          tree-checkable
          allow-clear
          show-checked-strategy="SHOW_CHILD"
          placeholder="请选择标签类型"
          tree-node-filter-prop="name"
          :max-tag-count="3"
          @change="(value) => selectChange('labelIdList', value)"
        />

        <a-select class="search-item" v-model:value="queryParam.status" @change="(value) => selectChange('status', value)">
          <a-select-option value="">全部状态</a-select-option>
          <a-select-option value="10">未处置</a-select-option>
          <a-select-option value="20">已处置</a-select-option>
        </a-select>

        <a-range-picker
          class="pic-date"
          v-model:value="dateRange"
          :placeholder="['拍摄开始日期', '拍摄结束日期']"
          @change="(value) => selectChange('time', value)"
        />
        <a-range-picker
          v-model:value="dealDateRange"
          :placeholder="['处置开始日期', '处置结束日期']"
          @change="(value) => selectChange('handlerTime', value)"
        />
      </div>
    </div>

    <a-table
      :columns="columns"
      :dataSource="eventList"
      :rowKey="(record) => record.id"
      :indentSize="10"
      :pagination="pagination"
      @change="paginationChange"
      :scroll="{ x: 1500 }"
    >
      <template #bodyCell="{ record, column }">
        <template v-if="column.dataIndex === 'eventNo'">
          <a-button type="link" @click="openDetail(record)">{{ record.eventNo }}</a-button>
        </template>
        <template v-if="column.dataIndex === 'eventLocation'">
          <Tooltip :title="record.eventLocation">
            <environment-filled v-if="record.eventLocation" />
            <span class="position">{{ record.eventLocation || '--' }}</span>
          </Tooltip>
        </template>
        <!-- <template v-if="column.dataIndex === 'eventTypes'">
          <div v-if="!record.eventTypes"></div>
          <div v-else v-for="(item, index) in record.eventTypes.split('，')" :key="index">{{ item }}</div>
        </template> -->
        <!-- <template v-if="column.dataIndex === 'eventDescription'">
          <Tooltip :title="record.eventDescription">
            <span>{{ record.eventDescription }}</span>
          </Tooltip>
        </template> -->
        <template v-if="column.dataIndex === 'image'">
          <Image :width="60" :src="record.eventFile.thumbnailUrl" :preview="{ src: record.eventFile.url }" v-if="record.eventFile.url" />
          <span v-else>--</span>
        </template>
        <template v-if="column.dataIndex === 'status'">
          <a-tag :color="record.status === '20' ? 'green' : ''">{{ record.status === '20' ? '已处置' : '未处置' }}</a-tag>
        </template>
        <template v-if="column.dataIndex === 'operation'">
          <a-button type="link" @click="handleEvent(record)" v-if="isMyPart(record.sysOrgCode)">事件处置</a-button>
        </template>
      </template>
    </a-table>
    <dealModal
      ref="dealModalRef"
      :dealId="dealId"
      :eventNo="eventNo"
      :dealStatus="dealStatus"
      :sysOrgCode="queryParam.sysOrgCode"
      @getEventList="getList(queryParam)"
    />
  </div>
</template>

<script lang="ts" name="dataManage-eventManage-index" setup>
import { ref, reactive, computed, onMounted, toRaw, nextTick, watch } from 'vue';
import { Image, Tooltip, message } from 'ant-design-vue';
import { EnvironmentFilled, PlusOutlined } from '@ant-design/icons-vue';
import { useRouter } from 'vue-router';
import { eventManageApi } from './eventManage.api';
import dealModal from './dealModal.vue';
import dayjs from 'dayjs';
import { getDepartNameByCode } from '/@/utils/common/compUtils';

const isEms = localStorage.getItem('environmentTag') === 'privateCloud' ? true : false;
const dealModalRef = ref('');
const router = useRouter();
const markerList = ref([]);
const dealerList = ref([]);
const dateRange = ref([]);
const dealDateRange = ref([]);
const PAGE_SIZE = 10;
const dealId = ref('');
const dealStatus = ref('');
const eventNo = ref('');

const treeValue = ref('');

const treeData = ref([]);
const queryParam = ref({
  labelUser: '',
  handler: '',
  labelIdList: [],
  status: '',
  eventBeginTime: '',
  eventEndTime: '',
  handlerBeginTime: '',
  handlerEndTime: '',
  pageNo: 1,
  pageSize: PAGE_SIZE,
  deptQueryType: 'CURRENT',
  sysOrgCode: '', // 初始化为空字符串
});

const columns = [
  {
    title: '事件编号',
    dataIndex: 'eventNo',
    key: 'eventNo',
    align: 'center',
    width: 150,
    fixed: 'left',
    sorter: (a, b) => a.eventNo - b.eventNo,
  },
  ...(!isEms
    ? [
        {
          title: '事件位置',
          dataIndex: 'eventLocation',
          key: 'eventLocation',
          ellipsis: true,
          width: 300,
          align: 'center',
        },
      ]
    : []),
  {
    title: '所属部门',
    dataIndex: 'myDepartName',
    key: 'myDepartName',
    align: 'center',
    ellipsis: true,
    width: 200,
    customRender: function ({ text, record }) {
      const { sysOrgCode } = record as any;
      return getDepartNameByCode(sysOrgCode);
    },
  },
  {
    title: '事件类型',
    dataIndex: 'eventTypes',
    key: 'eventTypes',
    align: 'center',
    ellipsis: true,

    width: 200,
    customRender: function ({ text }) {
      return text || '--';
    },
  },
  {
    title: '事件描述',
    dataIndex: 'eventDescription',
    key: 'eventDescription',
    ellipsis: true,
    width: 400,
    align: 'center',
    customRender: function ({ text }) {
      return text || '--';
    },
  },
  {
    title: '标注人员',
    dataIndex: 'labelUsers',
    key: 'labelUsers',
    align: 'center',
    width: 100,
    customRender: function ({ text }) {
      return text || '--';
    },
  },
  {
    title: '现场照片',
    dataIndex: 'image',
    key: 'image',
    align: 'center',
    width: 150,
  },
  {
    title: '拍摄时间',
    dataIndex: 'photoCreateTime',
    key: 'photoCreateTime',
    align: 'center',
    width: 200,
    sorter: (a, b) => {
      return new Date(a.eventFile.photoCreateTime) - new Date(b.eventFile.photoCreateTime);
    },
    customRender: function ({ record }) {
      const { eventFile } = record;
      return eventFile.photoCreateTime || '--';
    },
  },
  {
    title: '工单状态',
    dataIndex: 'status',
    key: 'status',
    align: 'center',
    width: 150,
  },
  {
    title: '处置人员',
    dataIndex: 'handlerRealname',
    key: 'handlerRealname',
    align: 'center',
    width: 100,
    customRender: function ({ text }) {
      return text || '--';
    },
  },
  {
    title: '处置时间',
    dataIndex: 'handlerTime',
    key: 'handlerTime',
    align: 'center',
    width: 200,
    sorter: (a, b) => new Date(a.handlerTime) - new Date(b.handlerTime),
    customRender: function ({ text }) {
      return text || '--';
    },
  },
  {
    title: '操作',
    dataIndex: 'operation',
    key: 'operation',
    fixed: 'right',
    width: 100,
    align: 'center',
  },
];

const eventList = ref([]);
const labelIdsOptions = ref([]);
const isMyPart = (code) => {
  const myDepartList = localStorage.getItem('myDepartList');

  const parsedDepartList = myDepartList && myDepartList !== 'null' ? JSON.parse(myDepartList) : [];
  return parsedDepartList.some((item) => item.orgCode === code);
};
/**
 * 将树形数据转换为适合树形控件使用的数据格式
 *
 * @param data 原始树形数据
 * @returns 转换后的树形数据
 */
const transformTreeData = (data) => {
  return data.map((node) => {
    const newNode = {
      ...node,
      disabled: node.disableCheckbox,
      label: node.departName,
      value: node.orgCode,
      key: node.orgCode,
    };
    delete newNode.disableCheckbox;

    if (node.children) {
      newNode.children = transformTreeData(node.children);
    }

    return newNode;
  });
};
const setDepartmentTree = async () => {
  const treeObj = localStorage.getItem('myDepartAndChildrenTree');
  const rawData = treeObj ? JSON.parse(treeObj) : [];
  treeData.value = transformTreeData(rawData);

  const myDepartList = localStorage.getItem('myDepartList');
  treeValue.value = myDepartList && myDepartList !== 'null' ? JSON.parse(myDepartList)[0].orgCode : '';
};
const openDetail = (record) => {
  router.push({
    path: '/dataManage/eventManage/eventDetail',
    query: {
      id: record.id,
      sysOrgCode: record.sysOrgCode,
    },
  });
};
// 获取类型列表
const getLabelList = (params) => {
  eventManageApi.markCategoryList(params).then((res) => {
    console.log(res);
    labelIdsOptions.value = res;
  });
};

// 列表翻页
const paginationChange = (pagination) => {
  const params = { ...queryParam.value, pageNo: pagination.current, pageSize: pagination.pageSize };
  getList(params);
};

const pagination = ref({
  total: 0,
  current: 1,
  pageSize: 10,
  change: paginationChange,
});
// 获取数据列表
const getList = (params) => {
  console.log('params', params);
  eventManageApi.getList(params).then((res) => {
    pagination.value.total = res.total;
    pagination.value.current = res.current;
    pagination.value.pageSize = res.size;
    eventList.value = res.records;
  });
};
const selectChange = (type, value) => {
  console.log(type, value);
  switch (type) {
    case 'depart':
      console.log('depart', value);
      queryParam.value.sysOrgCode = value || '';
      // 当部门变化时，同时更新标签列表，并清空已选择的标签类型
      queryParam.value.labelIdList = [];
      break;
    case 'labelUser':
      console.log('labelUser', value);
      queryParam.value.labelUser = value ?? '';
      break;
    case 'handler':
      queryParam.value.handler = value ?? '';
      break;
    case 'labelIdList':
      queryParam.value.labelIdList = value ?? [];
      break;

    case 'status':
      queryParam.value.status = value ?? '';
      break;
    case 'time':
      queryParam.value.eventBeginTime = value ? dayjs(value[0].$d).format('YYYY-MM-DD 00:00:00') : '';
      queryParam.value.eventEndTime = value ? dayjs(value[1].$d).format('YYYY-MM-DD 23:59:59') : '';
      break;
    case 'handlerTime':
      queryParam.value.handlerBeginTime = value ? dayjs(value[0].$d).format('YYYY-MM-DD 00:00:00') : '';
      queryParam.value.handlerEndTime = value ? dayjs(value[1].$d).format('YYYY-MM-DD 23:59:59') : '';
      break;
  }
  console.log('type', queryParam.value);
  getList(queryParam.value);
};

const handleEvent = async (record) => {
  dealId.value = record.id;
  dealStatus.value = record.status;
  eventNo.value = record.eventNo;
  console.log('record', record.status);
  await nextTick();
  dealModalRef.value.openModal();
};
const getAllUser = () => {
  //haveAiUser,true为查询标注人员，false为查询处置人员
  eventManageApi.queryAllUser({ ticketUserType: '2', haveAiUser: false }).then((res) => {
    console.log('res', res);
    dealerList.value = res;
  });
  eventManageApi.queryAllUser({ ticketUserType: '1', haveAiUser: true }).then((res) => {
    console.log('res', res);
    markerList.value = res;
  });
};
watch(treeValue, (value) => {
  queryParam.value.sysOrgCode = value || '';
  getLabelList({ sysOrgCode: value || '' });
});

onMounted(async () => {
  await setDepartmentTree();
  getList(queryParam.value);
  getAllUser();
});
</script>

<style lang="less" scoped>
#event-manage-wrapper {
  margin: 20px;
  padding: 10px;
  background-color: #fff;
  height: calc(100% - 40px);
  .btn-box {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    .page-title {
      padding-left: 7px;
      font-size: 16px;
      font-weight: 500;
      line-height: 24px;
      color: rgba(0, 0, 0, 0.85);
    }
  }
}
.position {
  vertical-align: middle;
  margin-left: 5px;
}
:deep(span.anticon:not(.app-iconify)) {
  vertical-align: middle !important;
}
:deep(.ant-empty-normal) {
  margin: 160px 0;
}
:deep(.ant-image-mask-info) {
  display: flex;
  align-items: center;
}
:deep(.ant-btn-link) {
  padding: 0;
}

.search-item {
  width: 130px;
  margin-right: 15px;
}
.pic-date {
  margin-right: 15px;
}
</style>
