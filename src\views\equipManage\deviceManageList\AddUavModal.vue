<template>
  <a-modal v-model:visible="visible" :title="edit ? '编辑飞行器' : '添加飞行器'" @ok="submit" @cancel="cancel">
    <div class="uavModal">
      <a-form ref="formRef" :model="formState" :label-col="{ span: 5 }" :wrapper-col="{ span: 18 }" :rules="rules">
        <a-form-item label="设备名称" name="deviceName">
          <a-input v-model:value="formState.deviceName" :maxlength="25" />
        </a-form-item>
        <a-form-item label="所属部门" name="sysOrgCode">
          <a-select v-model:value="formState.sysOrgCode" :options="departlist" allowClear></a-select>
        </a-form-item>
        <a-form-item label="设备品牌" name="deviceProducer">
          <a-select v-model:value="formState.deviceProducer">
            <a-select-option value="YD">远度</a-select-option>
            <a-select-option value="ZH">纵横</a-select-option>
            <a-select-option value="PZ">普宙</a-select-option>
            <a-select-option value="DJISD">大疆植保</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="设备型号" name="deviceModel">
          <a-input v-model:value="formState.deviceModel" :maxlength="25" />
        </a-form-item>
        <a-form-item :label="edit ? '设备SN' : '飞行器SN'" name="deviceSn">
          <a-input v-model:value="formState.deviceSn" :maxlength="25" :disabled="edit" />
        </a-form-item>
        <a-form-item label="遥控器SN" v-if="!edit" name="gatewaySn">
          <a-input v-model:value="formState.gatewaySn" :maxlength="25" />
        </a-form-item>
        <a-form-item label="定位器SN" name="payloadSn" v-if="formState.deviceProducer === 'DJISD'">
          <a-input v-model:value="formState.payloadSn" :maxlength="25" />
        </a-form-item>
        <a-form-item label="责任人" name="userId">
          <a-select v-model:value="formState.userId" :options="getUeserList" :fieldNames="{ label: 'realname', value: 'id' }"> </a-select>
        </a-form-item>
      </a-form>
    </div>
  </a-modal>
</template>
<script lang="ts" setup>
  import { reactive, ref, onMounted } from 'vue';
  import { UserList, deviceApi } from './data.api';
  const visible = ref<boolean>(false);
  const props = defineProps({
    departlist: {
      type: Array,
      default: () => [],
    },
  });
  const emits = defineEmits(['addOk']);
  import { message } from 'ant-design-vue';
  let getUeserList = ref();
  let formRef = ref();
  let formState = reactive({
    deviceName: '',
    deviceProducer: '',
    deviceModel: '',
    deviceSn: '',
    gatewaySn: '',
    payloadSn: '',
    userId: '',
    sysOrgCode: undefined,
  });

  const rules = {
    sysOrgCode: [{ required: true, message: '请选择部门', trigger: 'change' }],
    deviceName: [
      {
        required: true,
        trigger: 'change',
        validator: async (_rule, value) => {
          console.log(value, '--**/*/-');
          if (!formState.deviceName) {
            return Promise.reject('请输入设备名称！');
          }

          return Promise.resolve();
        },
      },
    ],
    deviceProducer: [
      {
        required: true,
        trigger: 'change',
        validator: async (_rule, value) => {
          if (!formState.deviceProducer) {
            return Promise.reject('请选择设备品牌！');
          }

          return Promise.resolve();
        },
      },
    ],
    deviceModel: [
      {
        required: true,
        trigger: 'change',
        validator: async (_rule, value) => {
          if (!formState.deviceModel) {
            return Promise.reject('请输入设备型号！');
          }

          return Promise.resolve();
        },
      },
    ],
    deviceSn: [
      {
        required: true,
        trigger: 'change',
        validator: async (_rule, value) => {
          if (!formState.deviceSn) {
            return Promise.reject('请输入飞行器SN！');
          }
          const reg = /^[A-Za-z0-9]+$/;
          if (!reg.test(value)) {
            return Promise.reject('只能输入数字和字母，不允许特殊字符');
          }
          return Promise.resolve();
        },
      },
    ],
    gatewaySn: [
      {
        required: true,
        trigger: 'change',
        validator: async (_rule, value) => {
          if (!formState.gatewaySn) {
            return Promise.reject('请输入遥控器SN！');
          }
          const reg = /^[A-Za-z0-9]+$/;
          if (!reg.test(value)) {
            return Promise.reject('只能输入数字和字母，不允许特殊字符');
          }
          return Promise.resolve();
        },
      },
    ],
    payloadSn: [
      {
        required: true,
        trigger: 'change',
        validator: async (_rule, value) => {
          if (!formState.payloadSn) {
            return Promise.reject('请输入定位器SN！');
          }
          const reg = /^[A-Za-z0-9]+$/;
          if (!reg.test(value)) {
            return Promise.reject('只能输入数字和字母，不允许特殊字符');
          }
          return Promise.resolve();
        },
      },
    ],
    userId: [
      {
        required: true,
        trigger: 'change',
        validator: async (_rule, value) => {
          if (!formState.userId) {
            return Promise.reject('请选择责任人！');
          }

          return Promise.resolve();
        },
      },
    ],
  };

  let edit = ref(false);
  const openModal = (value = false, record) => {
    visible.value = true;
    edit.value = value;
    if (record) {
      Object.assign(formState, { ...record });
      console.log('编辑设备', formState);
    }
    if (record?.deviceProducer == 'DJISD') {
      getLocationSnApi(record);
    }
  };

  const getLocationSnApi = async (record) => {
    const res = await deviceApi.getLocationSn({ deviceId: record.deviceId });
    console.log(res);
    if (res.data.code == 200) {
      formState.payloadSn = res.data.result;
    } else {
      message.error(res.data.message);
    }
  };

  const getUeser = () => {
    UserList({ pageNo: 1, pageSize: 1000 }).then((res) => {
      console.log('oooo--999', res);
      getUeserList.value = res.records;
    });
  };

  const submit = async () => {
    await formRef.value.validate();
    console.log('通过了校验！');
    if (edit.value) {
      const res = await deviceApi.edit(formState);
      if (res?.id) {
        message.success('编辑成功！');
        cancel();
        emits('addOk');
      } else {
        message.error('失败！');
      }
    } else {
      const res = await deviceApi.addDrone(formState);
      console.log(res, 'res');
      if (res.data.code == 200) {
        message.success('新增成功！');
        cancel();
        emits('addOk');
      } else {
        message.error(res.data.message);
      }
    }
  };

  const cancel = () => {
    formRef.value.resetFields();
    Object.assign(formState, {
      deviceName: '',
      deviceProducer: '',
      deviceModel: '',
      deviceSn: '',
      gatewaySn: '',
      payloadSn: '',
      userId: '',
    });
    visible.value = false;
  };
  onMounted(() => {
    getUeser();
  });

  defineExpose({
    openModal,
  });
</script>
<style lang="less">
  .uavModal {
    padding: 20px 0 0 0;
  }
</style>
