<template>
  <div id="deal-modal-wrapper">
    <a-modal
      title="事件处置"
      width="800px"
      v-model:visible="isVisible"
      :getContainer="getContainer"
      :destroyOnClose="true"
      :maskClosable="false"
      okText="提交"
      @ok="handleSubmit"
    >
      <div class="deal-box">
        <a-form :labelCol="{ span: 3 }" labelAlign="left" ref="formRef" :model="dealFormData">
          <a-form-item label="处置照片" name="dealImg" :rules="[{ required: true, validator: validateUpload, message: '请上传至少一张照片！' }]">
            <a-upload
              v-model:file-list="dealImgList"
              name="dealImg"
              :multiple="true"
              list-type="picture-card"
              class="deal-img-uploader"
              :customRequest="dealCustomRequest"
              :before-upload="dealBeforeUpload"
              :max-count="maxFiles"
              :show-upload-list="{ showRemoveIcon: true, showPreviewIcon: false }"
              @remove="handleRemove"
              @change="handleImgChange"
            >
              <div v-if="dealImgList?.length < maxFiles">
                <plus-outlined :style="{ fontSize: '25px' }"></plus-outlined>
                <div class="ant-upload-text">上传</div>
              </div>
            </a-upload>
            <ul class="tips">
              <li>文件必须是jpg或png格式的图片</li>
              <li>单个事件最多上传5张图片</li>
              <li>每个文件大小不超过50M</li>
            </ul>
          </a-form-item>

          <a-form-item label="处置描述" name="desc" :rules="[{ required: true, message: '请输入处置描述！' }]">
            <a-textarea v-model:value="dealFormData.desc" :placeholder="'请输入处置描述'" :rows="3" show-count :maxlength="100" />
          </a-form-item>
        </a-form>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, defineProps, watch, defineEmits, toRefs } from 'vue';
import { EnvironmentFilled, PlusOutlined } from '@ant-design/icons-vue';
import { eventManageApi } from './eventManage.api';
import { message } from 'ant-design-vue';

const emit = defineEmits();
const props = defineProps({
  dealId: {
    type: String,
    default: '',
  },
  dealStatus: {
    type: String,
    default: '',
  },
  eventNo: {
    type: String,
    default: '',
  },
  sysOrgCode: {
    type: String,
    default: '',
  },
});
const isVisible = ref(false);
const formRef = ref(null);
const dealFormData = ref({
  desc: '',
});
const dealImgList = ref([]);
const maxFiles = 5;

const openModal = () => {
  isVisible.value = true;
  console.log('props.dealStatus', props.dealStatus);
  props.dealStatus === '20' && getHandledInfo();
  props.dealStatus === '10' && resetForm();
};
const getContainer = () => {
  return document.getElementById('deal-modal-wrapper');
};

const handleSubmit = () => {
  formRef.value
    .validate()
    .then(() => {
      // 验证通过，执行提交逻辑

      //接口仅传参新上传的图片id
      const fileIdList = dealImgList.value
        .filter((ite) => {
          return ite.response;
        })
        .map((item) => item.response.fileId);
      const params = {
        fileIdList: fileIdList,
        handlerDescription: dealFormData.value.desc,
        id: props.dealId,
        status: '20',
        sysOrgCode: props.sysOrgCode,
      };
      console.log('params', params);
      eventManageApi.eventDeal(params).then((res) => {
        isVisible.value = false;
        resetForm();
        emit('getEventList');
      });
    })
    .catch((error) => {
      // 验证失败，处理错误
      console.log('验证失败：', error);
    });
};
const validateUpload = (rule, value) => {
  if (dealImgList.value.length === 0) {
    return Promise.reject('请上传至少一张照片！');
  }
  return Promise.resolve();
};
const dealCustomRequest = (e) => {
  const { file, onSuccess, onError } = e;
  let formData = new FormData();
  formData.append('file', file);
  console.log('formData', formData);
  eventManageApi
    .upload(props.sysOrgCode, formData)
    .then((res) => {
      onSuccess(res, file);
    })
    .catch((error) => {
      onError(error);
    });
};
const dealBeforeUpload = (file, fileListToUpload) => {
  const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
  if (!isJpgOrPng) {
    message.error('图片格式错误');
    return false; // 阻止上传
  }

  const isLt50M = file.size / 1024 / 1024 < 50;
  if (!isLt50M) {
    message.error('图片大小不能超过50MB!');
    return false; // 阻止上传
  }

  // 计算即将上传的文件数量
  const totalFiles = dealImgList.value.length + fileListToUpload.length;
  // 如果文件总数超过了最大限制，阻止上传并给出提示

  if (totalFiles > maxFiles) {
    message.error(`最多只能上传 ${maxFiles} 张图片！`);
    file.status = 'error';
    return false;
  }

  return true; // 通过校验，允许上传
};
//过滤掉列表里面的error文件
const handleImgChange = ({ fileList }) => {
  dealImgList.value = fileList.filter((file) => file.status !== 'error');
};
const handleRemove = (file) => {
  // 当文件删除时，从 fileList 中移除该文件
  dealImgList.value = dealImgList.value.filter((item) => item.uid !== file.uid);
  console.log(file, '删除的文件');
  console.log(dealImgList.value, '列表');
  if (props.dealStatus === '10') {
    // 未处置过的文件，调用/file/manage/delete-file接口删除
    eventManageApi.deleteFile(file.fileId).then((res) => {
      console.log(res);
    });
  } else {
    // 已处置过的文件，调用/uav/ticket/deleteTicketFile接口删除
    const params = {
      fileId: file.fileId,
      id: props.dealId,
    };
    eventManageApi.deleteEventFile(params).then((res) => {
      console.log(res);
    });
  }
};
const getHandledInfo = () => {
  eventManageApi.queryDetail({ id: props.dealId, sysOrgCode: props.sysOrgCode }).then((res) => {
    console.log(res, 'res');
    dealFormData.value.desc = res.handlerDescription;
    dealImgList.value = res.handlerFileList || [];
  });
};
const resetForm = () => {
  dealFormData.value = {
    desc: '',
  };
  dealImgList.value = [];
};
defineExpose({
  openModal,
});
</script>

<style lang="less" scoped>
#deal-modal-wrapper {
  :deep(.ant-modal .ant-modal-body) {
    padding: 0px 20px 20px 20px;
    div[aria-hidden='true'] {
      display: none !important;
    }
  }
  .deal-box {
    margin-top: 20px;
    .tips {
      list-style-type: disc;
      margin: 10px;
      color: #666;
    }
  }
}
</style>