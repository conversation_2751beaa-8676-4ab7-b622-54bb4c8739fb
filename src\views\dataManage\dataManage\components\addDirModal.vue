<template>
  <BasicModal
    :width="450"
    :minHeight="120"
    :height="120"
    title="新建文件夹"
    @register="register"
    @ok="onSubmit"
    @cancel="cancelModal"
    :canFullscreen="false"
  >
    <div class="addDirModal">
      <BasicForm
        ref="modelFormRef"
        @register="registerForm"
        :labelAlign="'left'"
        :wrapperCol="{ span: 24 }"
        :actionColOptions="{ span: 24 }"
        :labelWidth="100"
      >
      </BasicForm>
    </div>
  </BasicModal>
</template>
<script lang="ts" setup>
import { ref, computed, defineProps } from 'vue';
import { postAddDirApi } from '../data.api';
import { BasicModal, useModalInner } from '/@/components/Modal';
import { BasicForm, useForm } from '/@/components/Form';
import { useMessage } from '/@/hooks/web/useMessage';

const modelFormRef = ref(null);

const props = defineProps({
  dirList: {
    type: Array,
    default: [],
  },
  sysOrgCode: {
    type: String,
    default: '',
  },
});

let schemas = ref([
  {
    label: '文件夹名称',
    field: 'directoryName',
    component: 'Input',
    required: true,
  },
]);
const emits = defineEmits(['success', 'register']);
const { createMessage, createErrorModal } = useMessage();
const [registerForm, { validate, validateFields, resetFields, setFieldsValue }] = useForm({
  schemas: schemas,
  showActionButtonGroup: false,
  // rowProps: { gutter: 24, justify: 'center', align: 'middle' },
  // baseColProps: { span: 12 },
  baseRowStyle: { width: '100%' },
});
const [register, { closeModal }] = useModalInner(() => {});
async function onSubmit() {
  const value = await validate();
  //console.log("value", value)
  if (!value.directoryName) {
    return;
  }
  const curDirListTemp = props.dirList;
  const parentItem = curDirListTemp[curDirListTemp?.length - 1];
  const res = await postAddDirApi({
    directoryName: value.directoryName,
    directoryType: 3,
    leaf: 0,
    parentId: parentItem.id,
    sysOrgCode: props.sysOrgCode,
  });
  const { data } = res;
  if (data.code === 200) {
    createMessage.info('新建文件夹成功');
    await resetFields();
    emits('success');
    closeModal();
  } else {
  }
}

// 取消按钮点击
const cancelModal = () => {
  modelFormRef.value.resetFields();
  closeModal();
};
</script>
<style lang="less" scoped>
/deep/.scroll-container {
  padding: 0;
}
.addDirModal {
  display: flex;
  padding-top: 50px;
  .leftBox {
    padding-right: 10px;
    border-right: 2px solid rgba(0, 0, 0, 0.05);
    .title {
      font-size: 14px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #333333;
    }
    .moveFileListBox {
      margin-top: 12px;
      .list {
        cursor: pointer;
        width: 206px;
        height: 40px;
        background-color: #feffff;
        border-radius: 2px;
        border: 1px solid #f1f8fb;
        display: flex;
        align-items: center;
        color: #333333;
        font-size: 14px;
        font-weight: 400;
        margin-bottom: 8px;
        justify-content: space-between;

        .text {
          display: flex;
          align-items: center;
        }
        .close {
          color: #6eb0d4;
          margin-right: 10px;
          opacity: 0;
        }
      }
      .list:hover {
        background: linear-gradient(180deg, #feffff 0%, #fafcfe 100%);
        border: 1px solid #c6e0ee;
        .close {
          opacity: 1;
        }
      }
    }
  }
  .rightBox {
    flex: 1;
    padding-left: 12px;
    .title {
    }
    .Box {
      display: flex;
      justify-content: flex-start;
      width: 692px;
      overflow-x: auto;
      .listBox {
        background: linear-gradient(180deg, #ffffff 0%, #f9fcfd 100%);
        padding: 8px 6px;
        min-height: 464px;
        margin-right: 12px;
        border: 2px solid #d3e7f1;
        .list {
          width: 206px;
          height: 32px;
          border-radius: 2px;
          display: flex;
          align-items: center;
          justify-content: space-between;
          cursor: pointer;
          .text {
            display: flex;
            align-items: center;
            span {
              font-size: 14px;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: #333333;
              white-space: nowrap;
              text-emphasis: none;
              overflow: hidden;
            }
          }
          .CaretRight {
            color: #999999;
            margin-right: 6px;
          }
        }
        .list:hover {
          background-color: #167db5;
          span {
            color: #ffffff;
          }
          .CaretRight {
            color: white;
          }
        }
      }
    }
  }
}
</style>
