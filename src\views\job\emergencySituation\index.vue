<template>
  <div id="situation-manage-wrapper">
    <div class="btn-box">
      <div class="expand-collapse"> 警情管理 </div>
      <div>
        <a-tree-select
          v-model:value="selectedDepartment"
          multiple
          show-search
          tree-default-expand-all
          style="min-width: 181px; max-width: auto; margin-right: 8px"
          :dropdown-style="{ width: 'auto' }"
          :tree-data="departmentList"
          :field-names="{ children: 'children', label: 'departName', value: 'orgCode' }"
          :dropdownMatchSelectWidth="false"
          placeholder="请选择部门"
          :maxTagCount="1"
          :treeCheckable="true"
          :treeCheckStrictly="true"
          :maxTagPlaceholder="(omittedValues) => `+${omittedValues.length} 更多`"
          @change="selectChange('sysMultiOrgCode', selectedDepartment)"
        >
          <template #title="{ value: val, departName }" style="width: 500px">
            <div>{{ departName }}</div>
          </template>
        </a-tree-select>
        <a-select
          class="search-condition"
          allowClear
          v-model:value="queryParam.executionTaskStatus"
          @change="(value) => selectChange('executionTaskStatus', value)"
        >
          <a-select-option v-for="item in TaskStatusEnumOptions" :key="item.value" :value="item.value">{{ item.label }}</a-select-option>
        </a-select>
        <a-select
          class="search-condition"
          allowClear
          v-model:value="queryParam.executionDeviceId"
          @change="(value) => selectChange('executionDeviceId', value)"
        >
          <a-select-option value="">全部执行设备</a-select-option>
          <a-select-option v-for="item in executionDevice" :value="item.executionDeviceId" :key="item.executionDeviceId">{{
            item.executionDeviceName
          }}</a-select-option>
        </a-select>
        <a-select
          class="search-condition"
          allowClear
          v-model:value="queryParam.alarmDeviceId"
          @change="(value) => selectChange('alarmDeviceId', value)"
        >
          <a-select-option value="">全部上报设备</a-select-option>
          <a-select-option v-for="item in alarmDevice" :value="item.alarmDeviceId" :key="item.alarmDeviceId">{{
            item.alarmDeviceName
          }}</a-select-option>
        </a-select>

        <a-range-picker
          class="pic-date"
          v-model:value="dateRange"
          :placeholder="['开始日期', '结束日期']"
          @change="(value) => selectChange('time', value)"
        />
      </div>
    </div>

    <a-table
      :columns="columns"
      :dataSource="dataList"
      :rowKey="(record) => record.id"
      :indentSize="10"
      :pagination="pagination"
      @change="paginationChange"
    >
      <template #bodyCell="{ record, column }">
        <template v-if="column.dataIndex === 'alarmLocation'">
          <span v-if="isEms">{{ `${record.longitude},${record.latitude}` }}</span>
          <Tooltip :title="record.alarmLocation" v-else>
            <environment-filled v-if="record.alarmLocation" />
            <span class="position">{{ record.alarmLocation || '--' }}</span>
          </Tooltip>
        </template>
        <template v-if="column.dataIndex === 'executionTaskStatus'">
          <div class="statusWrap">
            <template v-if="returnStatusObj(record).iconStatus"><a-badge :status="returnStatusObj(record).iconStatus" /></template>
            <div class="text">{{ returnStatusObj(record).label }}</div>
            <template v-if="returnStatusObj(record).iconStatus === 'error'">
              <div class="tips">
                <Tooltip :title="record.result">
                  <span>
                    <InfoCircleOutlined />
                  </span>
                </Tooltip>
              </div>
            </template>
          </div>
        </template>
        <template v-if="column.dataIndex === 'image'">
          <Image :width="60" :src="record.venueImageFileThumbnailUrl" :preview="{ src: record.venueImageFileUrl }" v-if="record.venueImageFileUrl" />
          <span v-else>--</span>
        </template>
        <template v-if="column.dataIndex === 'operation'">
          <template v-if="showOperation(record)">
            <a-button type="link" @click="viewTrack(record)" style="margin-right: 15px">查看轨迹</a-button>
            <a-button type="link" @click="viewData(record)" style="margin-right: 15px">查看数据</a-button>
          </template>
          <span v-else>--</span>
        </template>
        <template v-if="column.dataIndex === 'myDepartName'">
          <div>{{ getDepartNameByCode(record.sysOrgCode) }}</div>
        </template>
      </template>
    </a-table>
    <TrackPlaybackModal
      :isCollapsed="false"
      :open="trackPlaybackOpen"
      :trackPlaybackDeviceSn="trackPlaybackDeviceSn"
      :trackTaskId="trackTaskId"
      :sysOrgCode="sysOrgCodeRef"
      @close="
        () => {
          trackPlaybackOpen = false;
        }
      "
    />
  </div>
</template>

<script lang="ts" name="uav-job-emergencySituation" setup>
import { ref, computed, onMounted, inject, reactive, toRef, watch } from 'vue';
import { Image, message, Tooltip } from 'ant-design-vue';
import { EnvironmentFilled, InfoCircleOutlined } from '@ant-design/icons-vue';
import { situationApi } from './situation.api';
import dayjs from 'dayjs';
import ApiListOptions from '/@/api/type';
import { DataDirNavigation } from '/@/constants/index';
import { useRouter } from 'vue-router';
import TrackPlaybackModal from '/@/views/components/flightPathReplay/trackPlaybackModal.vue';
import { getDepartNameByCode } from '/@/utils/common/compUtils';
import { useMultipleTabStore } from '/@/store/modules/multipleTab';

const isEms = localStorage.getItem('environmentTag') === 'privateCloud' ? true : false;

const router = useRouter();

const $http = inject('api');
const columns = [
  {
    title: '警情编号',
    dataIndex: 'alarmNo',
    key: 'alarmNo',
    align: 'center',
  },
  {
    title: '上报设备',
    dataIndex: 'alarmDeviceName',
    key: 'alarmDeviceName',
    align: 'center',
  },
  {
    title: '部门',
    dataIndex: 'myDepartName',
    key: 'myDepartName',
    align: 'center',
  },
  {
    title: '执行状态',
    dataIndex: 'executionTaskStatus',
    key: 'executionTaskStatus',
    align: 'center',
  },
  {
    title: '警情位置',
    dataIndex: 'alarmLocation',
    key: 'alarmLocation',
    align: 'center',
  },
  {
    title: '现场照片',
    dataIndex: 'image',
    key: 'image',
    align: 'center',
  },
  {
    title: '上报时间',
    dataIndex: 'alarmTime',
    key: 'alarmTime',
    align: 'center',

    sorter: (a, b) => new Date(a.alarmTime) - new Date(b.alarmTime),
  },
  {
    title: '执行时间',
    dataIndex: 'executionTaskTime',
    key: 'executionTaskTime',
    align: 'center',

    sorter: (a, b) => new Date(a.executionTaskTime) - new Date(b.executionTaskTime),
  },
  {
    title: '执行机场',
    dataIndex: 'executionDeviceName',
    key: 'executionDeviceName',
    align: 'center',
  },
  {
    title: '操作',
    dataIndex: 'operation',
    key: 'operation',
    align: 'center',
  },
];
// 产品经理确认此处只用这3个状态
const TaskStatusEnumOptions = reactive([
  {
    value: '',
    label: '全部执行状态',
    iconStatus: '',
  },
  {
    value: 2,
    label: '执行中',
    iconStatus: 'processing',
  },
  {
    value: 3,
    label: '执行成功',
    iconStatus: 'success',
  },

  {
    value: 5,
    label: '执行失败',
    iconStatus: 'error',
  },
]);
const dataList = ref([]);
const alarmDevice = ref([]);
const executionDevice = ref([]);
const dateRange = ref([]);
const PAGE_SIZE = 10;
const dirList = ref([]); // 数据管理的目录路径
let trackPlaybackOpen = ref(false); //轨迹回放弹窗
let trackPlaybackDeviceSn = ref(); // 轨迹回放设备SN
let trackTaskId = ref(); // 轨迹回放任务ID
const selectedDepartment = ref<any[]>([]);
let lastValidValue = <any>[];
const departmentList = ref<any[]>([]);
let sysOrgCodeRef = ref();

const queryParam = ref({
  executionTaskStatus: '',
  executionDeviceId: '',
  alarmDeviceId: '',
  startAlarmTime: '',
  endAlarmTime: '',
  pageNo: 1,
  pageSize: PAGE_SIZE,
  sysMultiOrgCode: '',
  deptQueryType: 'MULTIPLE',
});

watch(
  selectedDepartment,
  (val) => {
    queryParam.value.sysMultiOrgCode = selectedDepartment.value.map((item) => item.value).join(',');
    console.log(selectedDepartment.value.map((item) => item.value).join(','), queryParam.value.sysMultiOrgCode);
  },
  { deep: true }
);

const returnStatusObj = (record) => {
  return TaskStatusEnumOptions.find((item) => item.value == record.executionTaskStatus);
};
// 执行中或者执行成功的数据才能查看轨迹
const showOperation = (record) => {
  return record.executionTaskStatus === 2 || record.executionTaskStatus === 3;
};
const selectChange = (type, value) => {
  switch (type) {
    case 'executionTaskStatus':
      queryParam.value.executionTaskStatus = value ?? '';
      break;
    case 'executionDeviceId':
      queryParam.value.executionDeviceId = value ?? '';
      break;
    case 'alarmDeviceId':
      queryParam.value.alarmDeviceId = value ?? '';
      break;
    case 'time':
      queryParam.value.startAlarmTime = value ? dayjs(value[0].$d).format('YYYY-MM-DD 00:00:00') : '';
      queryParam.value.endAlarmTime = value ? dayjs(value[1].$d).format('YYYY-MM-DD 23:59:59') : '';
      break;
    case 'sysMultiOrgCode':
      if (value.length === 0) {
        // 禁止清空，恢复上一次值
        selectedDepartment.value = [...lastValidValue];
        return;
      } else {
        lastValidValue = [...value];
      }
      queryParam.value.sysMultiOrgCode = value.map((item) => item.value).join(',');
      break;
  }
  getList(queryParam.value);
};
const getContainer = () => {
  return document.getElementById('situation-manage-wrapper');
};

// 列表翻页
const paginationChange = (pagination) => {
  const params = { ...queryParam.value, pageNo: pagination.current, pageSize: pagination.pageSize };
  getList(params);
};

const pagination = ref({
  total: 0,
  current: 1,
  pageSize: 10,
  change: paginationChange,
});
// 获取数据列表
const getList = (params) => {
  console.log(params);
  situationApi.getList(params).then((res) => {
    console.log('res=>>', res.records);
    pagination.value.total = res.total;
    pagination.value.current = res.current;
    pagination.value.pageSize = res.size;
    dataList.value = res.records;
  });
};
const getAlarmDeviceList = () => {
    let params = JSON.parse(localStorage.getItem('myDepartList') || '[]');
  situationApi.getAlarmDeviceList({ sysOrgCode: params.map((item) => item.orgCode).join(',') }).then((res) => {
    console.log('res=>>', res);
    alarmDevice.value = res;
  });
};
const getExecutionDeviceList = () => {
    let params = JSON.parse(localStorage.getItem('myDepartList') || '[]');
  situationApi.getExecutionDeviceList({ sysOrgCode: params.map((item) => item.orgCode).join(',') }).then((res) => {
    console.log('res=>>', res);
    executionDevice.value = res;
  });
};
const searchTree = (tree) => {
  dirList.value.push({
    name: tree?.directoryName,
    id: tree.id,
    dirType: tree.directoryType,
  });
  if (tree.children) {
    searchTree(tree.children);
  }
};
const viewData = async (record) => {
  const result = await $http?.getDirectoryGetDirByJob({ jobId: record.taskId || record.id, sysOrgCode: record.sysOrgCode });
  console.log('result', result);
  if (!result) {
    message.error('该任务未产生影像数据！');
    return;
  }
  dirList.value = [];
  searchTree(result);
  //console.log("跳转到数据管理=====", dirList.value)
  sessionStorage.setItem(DataDirNavigation, JSON.stringify(dirList.value));

  // 先关闭相同路径的选项卡，再打开新的选项卡
  const tabStore = useMultipleTabStore();
  const targetPath = '/dataManage/dataManage';
  const existingTab = tabStore.getTabList.find((item) => item.path === targetPath);
  if (existingTab) {
    tabStore.closeTab(existingTab, router);
  }

  router.push({
    path: '/dataManage/dataManage',
    query: {
      sysOrgCode: record.sysOrgCode,
    },
  });
};
const viewTrack = async (record) => {
  sysOrgCodeRef.value = record.sysOrgCode;
  const result = await $http?.getDeviceGet({
    // 获取机场设备型号
    deviceId: record.executionDeviceId,
  });
  if (result && result.deviceSn) {
    const resultSN = await $http?.getDeviceQuery({
      // 通过机场设备型号获取飞行器设备型号(一对一)
      deviceType: result.deviceType,
      deviceSn: result.deviceSn,
      sysOrgCode: record.sysOrgCode,
    });
    const { records } = resultSN;
    //console.log("resultSN", resultSN)
    if (records?.length && records[0]?.deviceList?.length) {
      trackPlaybackOpen.value = true;
      trackPlaybackDeviceSn.value = records[0]?.deviceList[0].deviceSn;
      trackTaskId.value = record.taskId;
    }
  }
};
function normalizeTree(list = []) {
  return list.map((item) => ({
    ...item,
    key: item.orgCode,
    value: item.orgCode,
    disabled: item.disableCheckbox === true,
    children: item.children ? normalizeTree(item.children) : [],
  }));
}
onMounted(() => {
  const myDepart = JSON.parse(localStorage.getItem('myDepartList') || '[]');
  const myDepartAndChildrenTree = localStorage.getItem('myDepartAndChildrenTree');
  // departmentList.value = myDepartAndChildrenTree && myDepartAndChildrenTree != 'null' ? JSON.parse(myDepartAndChildrenTree) : [];
  departmentList.value = myDepartAndChildrenTree && myDepartAndChildrenTree !== 'null' ? normalizeTree(JSON.parse(myDepartAndChildrenTree)) : [];
    const firstDepart = myDepart[0] || {};

    const option = {
      disabled: firstDepart.disableCheckbox ?? false,
      label: firstDepart.departName ?? '',
      value: firstDepart.orgCode ?? '',
    };
    // if (departmentList.value.length > 0) {
    selectedDepartment.value = [option];
    queryParam.value.sysMultiOrgCode = selectedDepartment.value.map((item) => item.value).join(',')
    lastValidValue = [...selectedDepartment.value];
    // }
    getList(queryParam.value);
    getAlarmDeviceList();
    getExecutionDeviceList();
  });
</script>

<style lang="less" scoped>
#situation-manage-wrapper {
  margin: 20px;
  padding: 10px;
  background-color: #fff;
  height: calc(100% - 40px);
  .btn-box {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    .expand-collapse {
      position: relative;
      display: flex;
      padding-left: 7px;
      font-size: 16px;
      font-weight: 500;
      line-height: 24px;
      color: rgba(0, 0, 0, 0.85);
    }
  }
  :deep(.ant-modal .ant-modal-body) {
    padding: 0px 20px 20px 20px;
  }
}
.position {
  vertical-align: middle;
  margin-left: 5px;
}
:deep(span.anticon:not(.app-iconify)) {
  vertical-align: middle !important;
}
:deep(.ant-image-mask-info) {
  display: flex;
  align-items: center;
}
:deep(.ant-btn-link) {
  padding: 0;
}

.search-condition {
  width: 180px;
  margin-right: 15px;
}
.statusWrap {
  display: flex;
  justify-content: center;
  align-items: center;

  .text {
  }

  .tips {
    margin-left: 5px;
  }
}
</style>
