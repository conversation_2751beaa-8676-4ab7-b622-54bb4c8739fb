import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { getAllRolesListNoByTenant, getAllTenantList } from './user.api';
import { rules } from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
export const columns: BasicColumn[] = [
  {
    title: '用户账号',
    dataIndex: 'username',
    width: 120,
  },
  {
    title: '用户姓名',
    dataIndex: 'realname',
    width: 100,
  },
  // {
  //   title: '头像',
  //   dataIndex: 'avatar',
  //   width: 120,
  //   customRender: render.renderAvatar,
  // },
  // {
  //   title: '性别',
  //   dataIndex: 'sex',
  //   width: 80,
  //   sorter: true,
  //   customRender: ({ text }) => {
  //     return render.renderDict(text, 'sex');
  //   },
  // },
  {
    title: '账号有效期',
    dataIndex: 'expiryDate',
    width: 100,
    customRender: ({ text }) => {
      return text || '长期有效';
    },
  },
  // {
  //   title: '生日',
  //   dataIndex: 'birthday',
  //   width: 100,
  // },
  {
    title: '手机号',
    dataIndex: 'phone',
    width: 100,
  },
  {
    title: '部门',
    width: 150,
    dataIndex: 'orgCodeTxt',
  },
  {
    title: '负责部门',
    width: 150,
    dataIndex: 'departIds_dictText',
  },
  {
    title: '角色',
    width: 150,
    dataIndex: 'roleCodeTxt',
  },
  {
    title: '状态',
    dataIndex: 'status_dictText',
    width: 80,
  },
];
export const departColumns: BasicColumn[] = [
  {
    title: '用户账号',
    dataIndex: 'username',
    width: 120,
  },
  {
    title: '用户名称',
    dataIndex: 'realname',
    width: 100,
  },
  // {
  //   title: '性别',
  //   dataIndex: 'sex',
  //   width: 80,
  //   sorter: true,
  //   customRender: ({ text }) => {
  //     return render.renderDict(text, 'sex');
  //   },
  // },
  {
    title: '手机号',
    dataIndex: 'phone',
    width: 100,
  },
  {
    title: '邮箱',
    dataIndex: 'email',
    width: 100,
  },
  {
    title: '状态',
    dataIndex: 'status_dictText',
    width: 80,
  },
];

export const recycleColumns: BasicColumn[] = [
  {
    title: '用户账号',
    dataIndex: 'username',
    width: 100,
  },
  {
    title: '用户姓名',
    dataIndex: 'realname',
    width: 100,
  },
  {
    title: '头像',
    dataIndex: 'avatar',
    width: 80,
    customRender: render.renderAvatar,
  },
  {
    title: '性别',
    dataIndex: 'sex',
    width: 80,
    sorter: true,
    customRender: ({ text }) => {
      return render.renderDict(text, 'sex');
    },
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    label: '账号',
    field: 'username',
    component: 'JInput',
    colProps: { span: 6 },
  },
  {
    label: '名字',
    field: 'realname',
    component: 'JInput',
    colProps: { span: 6 },
  },
  // {
  //   label: '性别',
  //   field: 'sex',
  //   component: 'JDictSelectTag',
  //   componentProps: {
  //     dictCode: 'sex',
  //     placeholder: '请选择性别',
  //     stringToNumber: true,
  //   },
  //   colProps: { span: 6 },
  // },
  {
    label: '手机号码',
    field: 'phone',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    label: '用户状态',
    field: 'status',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'user_status',
      placeholder: '请选择状态',
      stringToNumber: true,
    },
    colProps: { span: 6 },
  },
];
export const departSearchFormSchema: FormSchema[] = [
  {
    label: '账号',
    field: 'username',
    component: 'JInput',
    colProps: { span: 6 },
  },
  {
    label: '名字',
    field: 'realname',
    component: 'JInput',
    colProps: { span: 6 },
  },
];

export const formSchema: FormSchema[] = [
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
  {
    label: '用户账号',
    field: 'username',
    component: 'Input',
    dynamicDisabled: ({ values }) => {
      return !!values.id;
    },
    dynamicRules: ({ model, schema }) => rules.duplicateCheckRule('sys_user', 'username', model, schema, true),
  },
  {
    label: '登录密码',
    field: 'password',
    component: 'StrengthMeter',
    rules: [
      {
        required: true,
        message: '请输入登录密码',
      },
    ],
  },
  {
    label: '确认密码',
    field: 'confirmPassword',
    component: 'InputPassword',
    dynamicRules: ({ values }) => rules.confirmPassword(values, true),
  },
  {
    label: '用户姓名',
    field: 'realname',
    required: true,
    component: 'Input',
  },
  // {
  //   label: '工号',
  //   field: 'workNo',
  //   required: true,
  //   component: 'Input',
  //   dynamicRules: ({ model, schema }) => rules.duplicateCheckRule('sys_user', 'work_no', model, schema, true),
  // },
  // {
  //   label: '职务',
  //   field: 'post',
  //   required: false,
  //   component: 'JSelectPosition',
  //   componentProps: {
  //     rowKey: 'code',
  //     labelKey: 'name',
  //   },
  // },
  {
    label: '角色',
    field: 'selectedroles',
    component: 'ApiSelect',
    // required: true,
    componentProps: {
      mode: 'multiple',
      api: getAllRolesListNoByTenant,
      labelField: 'roleName',
      valueField: 'id',
    },
  },
  {
    label: '账号有效期',
    field: 'expiryDate',
    component: 'DatePicker',
  },
  {
    label: '所属部门',
    field: 'selecteddeparts',
    component: 'JSelectDept',
    ifShow: false,
    componentProps: ({ formActionType, formModel }) => {
      return {
        sync: false,
        checkStrictly: true,
        defaultExpandLevel: 2,
        // disabled:true,
        onSelect: (options, values) => {
          const { updateSchema } = formActionType;
          //所属部门修改后更新负责部门下拉框数据
          updateSchema([
            {
              field: 'departIds',
              componentProps: { options },
            },
          ]);
          //所属部门修改后更新负责部门数据
          formModel.departIds && (formModel.departIds = formModel.departIds.filter((item) => values.value.indexOf(item) > -1));
        },
      };
    },
  },
  // {
  //   label: '租户',
  //   field: 'relTenantIds',
  //   component: 'ApiSelect',
  //   componentProps: {
  //     mode: 'multiple',
  //     api: getAllTenantList,
  //     numberToString: true,
  //     labelField: 'name',
  //     valueField: 'id',
  //   },
  // },
  {
    label: '身份',
    field: 'userIdentity',
    component: 'RadioGroup',
    defaultValue: 1,
    ifShow: false,
    componentProps: ({ formModel }) => {
      return {
        options: [
          { label: '普通用户', value: 1, key: '1' },
          { label: '上级', value: 2, key: '2' },
        ],
        onChange: () => {
          formModel.userIdentity == 1 && (formModel.departIds = []);
        },
      };
    },
  },
  {
    label: '负责部门',
    field: 'departIds',
    component: 'Select',
    componentProps: {
      mode: 'multiple',
    },
    // ifShow: ({ values }) => values.userIdentity == 2,
    // 先屏蔽
    ifShow: false,
  },
  // {
  //   label: '头像',
  //   field: 'avatar',
  //   component: 'JImageUpload',
  //   componentProps: {
  //     fileMax: 1,
  //   },
  // },
  // {
  //   label: '生日',
  //   field: 'birthday',
  //   component: 'DatePicker',
  // },
  // {
  //   label: '性别',
  //   field: 'sex',
  //   component: 'JDictSelectTag',
  //   componentProps: {
  //     dictCode: 'sex',
  //     placeholder: '请选择性别',
  //     stringToNumber: true,
  //   },
  // },
  {
    label: '邮箱',
    field: 'email',
    // required: true,
    component: 'Input',
    // dynamicRules: ({ model, schema }) => {
    //   return [{ ...rules.duplicateCheckRule('sys_user', 'email', model, schema, true)[0] }, { ...rules.rule('email', false)[0] }];
    // },
  },
  {
    label: '手机号码',
    field: 'phone',
    required: true,
    component: 'Input',
    dynamicRules: ({ model, schema }) => {
      return [
        { ...rules.duplicateCheckRule('sys_user', 'phone', model, schema, true)[0] },
        { pattern: /^1[3456789]\d{9}$/, message: '手机号码格式有误' },
      ];
    },
  },
  // {
  //   label: '座机',
  //   field: 'telephone',
  //   component: 'Input',
  //   rules: [{ pattern: /^0\d{2,3}-[1-9]\d{6,7}$/, message: '请输入正确的座机号码' }],
  // },
  // {
  //   label: '工作流引擎',
  //   field: 'activitiSync',
  //   defaultValue: 1,
  //   component: 'JDictSelectTag',
  //   componentProps: {
  //     dictCode: 'activiti_sync',
  //     type: 'radio',
  //     stringToNumber: true,
  //   },
  // },
];

export const formPasswordSchema: FormSchema[] = [
  {
    label: '用户账号',
    field: 'username',
    component: 'Input',
    componentProps: { readOnly: true },
  },
  {
    label: '登录密码',
    field: 'password',
    component: 'StrengthMeter',
    componentProps: {
      placeholder: '请输入登录密码',
    },
    rules: [
      {
        required: true,
        message: '请输入登录密码',
      },
    ],
  },
  {
    label: '确认密码',
    field: 'confirmPassword',
    component: 'InputPassword',
    dynamicRules: ({ values }) => rules.confirmPassword(values, true),
  },
];

export const formAgentSchema: FormSchema[] = [
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
  {
    field: 'userName',
    label: '用户名',
    component: 'Input',
    componentProps: {
      readOnly: true,
      allowClear: false,
    },
  },
  {
    field: 'agentUserName',
    label: '代理人用户名',
    required: true,
    component: 'JSelectUser',
    componentProps: {
      rowKey: 'username',
      labelKey: 'realname',
      maxSelectCount: 10,
    },
  },
  {
    field: 'startTime',
    label: '代理开始时间',
    component: 'DatePicker',
    required: true,
    componentProps: {
      showTime: true,
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      placeholder: '请选择代理开始时间',
    },
  },
  {
    field: 'endTime',
    label: '代理结束时间',
    component: 'DatePicker',
    required: true,
    componentProps: {
      showTime: true,
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      placeholder: '请选择代理结束时间',
    },
  },
  {
    field: 'status',
    label: '状态',
    component: 'JDictSelectTag',
    defaultValue: '1',
    componentProps: {
      dictCode: 'valid_status',
      type: 'radioButton',
    },
  },
];

export const formQuitAgentSchema: FormSchema[] = [
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
  {
    field: 'userName',
    label: '用户名',
    component: 'Input',
    componentProps: {
      readOnly: true,
      allowClear: false,
    },
  },
  {
    field: 'agentUserName',
    label: '交接人员',
    required: true,
    component: 'JSelectUser',
    componentProps: {
      rowKey: 'username',
      labelKey: 'realname',
      maxSelectCount: 1,
    },
  },
  {
    field: 'startTime',
    label: '交接开始时间',
    component: 'DatePicker',
    required: true,
    componentProps: {
      showTime: true,
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      placeholder: '请选择交接开始时间',
      getPopupContainer: () => document.body,
    },
  },
  {
    field: 'endTime',
    label: '交接结束时间',
    component: 'DatePicker',
    required: true,
    componentProps: {
      showTime: true,
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      placeholder: '请选择交接结束时间',
      getPopupContainer: () => document.body,
    },
  },
  {
    field: 'status',
    label: '状态',
    component: 'JDictSelectTag',
    defaultValue: '1',
    componentProps: {
      dictCode: 'valid_status',
      type: 'radioButton',
    },
  },
];

//租户用户列表
export const userTenantColumns: BasicColumn[] = [
  {
    title: '用户账号',
    dataIndex: 'username',
    width: 120,
  },
  {
    title: '用户姓名',
    dataIndex: 'realname',
    width: 100,
  },
  {
    title: '头像',
    dataIndex: 'avatar',
    width: 120,
    customRender: render.renderAvatar,
  },
  {
    title: '手机号',
    dataIndex: 'phone',
    width: 100,
  },
  {
    title: '部门',
    width: 150,
    dataIndex: 'orgCodeTxt',
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 80,
    customRender: ({ text }) => {
      if (text === '1') {
        return '正常';
      } else if (text === '3') {
        return '审批中';
      } else {
        return '已拒绝';
      }
    },
  },
];

//用户租户搜索表单
export const userTenantFormSchema: FormSchema[] = [
  {
    label: '账号',
    field: 'username',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    label: '名字',
    field: 'realname',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    label: '性别',
    field: 'sex',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'sex',
      placeholder: '请选择性别',
      stringToNumber: true,
    },
    colProps: { span: 6 },
  },
];
