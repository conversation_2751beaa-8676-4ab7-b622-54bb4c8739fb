<template>
  <div>
    <div class="report-form">
      <a-form ref="formRef" :model="formValues1" :label-col="{ span: 3 }" :wrapper-col="{ span: 21 }" labelAlign="left" :colon="false">
        <a-form-item label="创建部门" name="chargeDepartment" :rules="{ required: true, message: '请选择部门' }">
          <a-select
            placeholder="请选择部门"
            v-model:value="formValues1.chargeDepartment"
            :options="departmentOptions"
            :fieldNames="{ label: 'departName', value: 'orgCode' }"
          >
          </a-select>
        </a-form-item>
        <a-form-item label="报告名称" name="reportName" :rules="{ required: true, message: '请输入报告名称' }">
          <a-input v-model:value="formValues1.reportName" />
        </a-form-item>
        <a-form-item label="巡检结论分析" name="conclusion" :rules="{ required: true, message: '请输入巡检结论分析' }">
          <a-textarea v-model:value="formValues1.conclusion" :rows="3" show-count :maxlength="100" />
        </a-form-item>
      </a-form>
      <a-divider />
      <div class="form-select">
        <a-form-item label="数据归属" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }" labelAlign="left" :colon="false">
          <a-tree-select
            v-if="treeData.length"
            v-model:value="treeValue"
            show-search
            :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
            placeholder="请选择部门"
            allow-clear
            tree-default-expand-all
            :tree-data="treeData"
            :fieldNames="{ label: 'departName', value: 'orgCode' }"
          >
          </a-tree-select>
        </a-form-item>

        <a-form-item label="拍摄日期" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }" labelAlign="left" :colon="false">
          <a-range-picker v-model:value="selectTime" format="YYYY-MM-DD HH:mm" />
        </a-form-item>
        <a-form-item label="所在区域" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }" labelAlign="left" :colon="false">
          <a-cascader max-tag-count="responsive" multiple v-model:value="selectArea" :options="options" placeholder="请选择所在区域" />
        </a-form-item>
        <a-form-item label="问题类型" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }" labelAlign="left" :colon="false">
          <a-tree-select
            :field-names="{ children: 'children', value: 'id', label: 'name' }"
            v-model:value="selectLabelIds"
            :tree-data="labelIdsOptions"
            tree-checkable
            allow-clear
            placeholder="请选择问题类型"
            tree-node-filter-prop="name"
            :getPopupContainer="(triggerNode) => triggerNode.parentNode"
          />
        </a-form-item>
        <a-form-item label="严重等级" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }" labelAlign="left" :colon="false">
          <a-select mode="multiple" v-model:value="selectLevels" placeholder="请选择严重等级">
            <a-select-option value="10">轻度</a-select-option>
            <a-select-option value="20">中度</a-select-option>
            <a-select-option value="30">重度</a-select-option>
            <a-select-option value="40">严重</a-select-option>
          </a-select>
        </a-form-item>
        <a-button type="primary" @click="getDataList">筛选</a-button>
      </div>
      <div class="report-form-list">
        <a-empty v-if="dataList.length == 0" style="width: 100%; margin-top: 80px" />
        <div :key="index" v-for="(item, index) in dataList" @click="checkedItem(item)" :class="item.active ? 'active' : ''" class="report-form-item">
          <div class="item-top">
            <div class="item-img">
              <img :src="item.fileUrl" />
            </div>
            <div class="item-info">
              <a-tooltip placement="topLeft" :title="item.fileName">
                <div class="item-name">{{ item.fileName }}</div>
              </a-tooltip>
              <div class="item-class">
                <!-- <a-tooltip placement="topLeft" :title="item.parentLabelName + '-' + item.labelName"> -->
                <a-tag color="green">{{ item.parentLabelName }}- {{ item.labelName }}</a-tag>
                <!-- </a-tooltip> -->
              </div>
            </div>
          </div>
          <a-tooltip placement="topLeft" :title="item.address">
            <div class="item-bottom">{{ item.address }}</div>
          </a-tooltip>
        </div>
      </div>
      <div class="report-modal-bottom">
        <div class="bottom-box">
          <div class="bottom-number"
            >已选<span class="number">{{ activeIndex }}</span
            >条数据</div
          >
          <div class="bottom-btn">
            <a-button @click="$emit('handleCancel')">取消</a-button>
            <a-button type="primary" @click="nextModal">下一步</a-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, computed, defineProps, onMounted, watch } from 'vue';
import { reportManageApi } from '../reportManage.api';
import { tagManageApi } from '../../tagManage/tagManage.api';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';
const props = defineProps({
  editRowId: {
    type: String,
    default: '',
  },
});

const formRef = ref(null);
const formValues1 = ref({
  chargeDepartment: undefined,
  reportName: 'xxx无人机巡检报告',
  conclusion: '共发现xx处问题!其中严重问题x项，重度问题x项，中度问题x项，轻度问题x项！',
  inspectionContents: [],
});
const dataList = ref([]);
// 计算属性得出选中数据总数
const activeIndex = computed(() => {
  return dataList.value.filter((item) => item.active).length;
});
const departmentOptions = ref([]);

const selectArea = ref([]);
const selectLevels = ref([]);
const selectLabelIds = ref([]);
const selectTime = ref([]);

const options = ref([]);
const labelIdsOptions = ref([]);
const treeData = ref([]);
const treeValue = ref(undefined);

const transformTreeData = (data) => {
  return data.map((node) => {
    const newNode = {
      ...node,
      disabled: node.disableCheckbox,
      label: node.departName,
      value: node.orgCode,
      key: node.orgCode,
    };

    if (node.children) {
      newNode.children = transformTreeData(node.children);
    }

    return newNode;
  });
};

// 获取类型列表
const getLabelList = () => {
  tagManageApi.getList({ sysOrgCode: treeValue.value }).then((res) => {
    console.log(res);
    labelIdsOptions.value = res;
  });
};
// 获取区域列表
const getOptionsList = () => {
  reportManageApi.getAreaList().then((res) => {
    // 格式化地区分级数据
    res.provinceList.forEach((e) => {
      e.value = e.provinceName;
      e.label = e.provinceName;
      e.cityList.forEach((i) => {
        i.value = i.cityCode;
        i.label = i.cityName;
        i.districtList.forEach((j) => {
          j.value = j.districtCode;
          j.label = j.districtName;
          if (j.streetList) {
            j.streetList.forEach((k) => {
              k.value = k.streetCode;
              k.label = k.streetName;
            });
            j.children = j.streetList;
          }
        });
        i.children = i.districtList;
      });
      e.children = e.cityList;
    });
    options.value = res.provinceList;
  });
};
// 获取可选数据列表
const getDataList = () => {
  let params = {};
  // 筛选条件 时间
  if (selectTime.value && selectTime.value.length > 0) {
    params.fileCreateTimeStart = dayjs(selectTime.value[0].$d).format('YYYY-MM-DD 00:00:00');
    params.fileCreateTimeEnd = dayjs(selectTime.value[1].$d).format('YYYY-MM-DD 23:59:59');
  }
  // 筛选条件 地区
  if (selectArea.value.length > 0) {
    selectArea.value.forEach((e) => {
      // 省份
      if (e.length == 1) {
        params.provinces ? '' : (params.provinces = []);
        params.provinces.push(e[0]);
      }
      // 市
      if (e.length == 2) {
        params.cityCodes ? '' : (params.cityCodes = []);
        params.cityCodes.push(e[1]);
      }
      // 区
      if (e.length == 3) {
        params.adCodes ? '' : (params.adCodes = []);
        params.adCodes.push(e[2]);
      }
      // 街道
      if (e.length == 4) {
        params.townCodes ? '' : (params.townCodes = []);
        params.townCodes.push(e[3]);
      }
    });
  }
  // 筛选条件 严重等级
  if (selectLevels.value.length > 0) {
    params.levels = selectLevels.value;
  }
  // 筛选条件 问题类型
  if (selectLabelIds.value.length > 0) {
    params.labelIds = selectLabelIds.value;
  }
  if (treeValue.value != '') {
    params.sysOrgCode = treeValue.value;
  }
  reportManageApi.getQueryLabel(params).then((res) => {
    console.log('9999999', res);
    if (res) {
      res.forEach((e) => {
        e.active = true;
      });
      dataList.value = res;
    } else {
      dataList.value = [];
    }
  });
};

const checkedItem = (item) => {
  item.active = !item.active;
};

const emit = defineEmits(['nextModal']);
// 下一步
const nextModal = () => {
  // 使用表单的验证功能进行统一验证
  formRef.value
    .validate()
    .then(() => {
      // 表单基础验证通过后，再验证自定义条件
      if (activeIndex.value == 0) {
        message.error('无有效数据，请重新筛选数据');
        return Promise.reject(new Error('无有效数据'));
      }

      formValues1.value.inspectionContents = dataList.value.filter((item) => item.active);
      let params = {
        sysOrgCode: formValues1.value.chargeDepartment,
        state: '0',
        reportName: formValues1.value.reportName,
        reportContent: {
          conclusion: formValues1.value.conclusion,
          inspectionContents: formValues1.value.inspectionContents,
        },
      };
      // 编辑模式传ID
      if (props.editRowId != '') {
        params.id = props.editRowId;
      }

      reportManageApi.save(params).then((res) => {
        emit('nextModal', res.id, res.sysOrgCode);
      });
    })
    .catch((error) => {
      // 表单验证失败，Ant Design Vue会自动显示错误信息
      console.log('表单验证失败:', error);
    });
};

// 编辑模式获取数据
const getData = () => {
  let params = {
    id: props.editRowId,
  };
  reportManageApi.get(params).then((res) => {
    res.reportContent.inspectionContents.forEach((e) => {
      e.active = true;
    });
    formValues1.value = res;
    formValues1.value.conclusion = res.reportContent.conclusion;
    dataList.value = res.reportContent.inspectionContents;
    formValues1.value.chargeDepartment = res.sysOrgCode;
  });
};
const getDepartmentList = () => {
  // 获取部门树
  const treeObj = localStorage.getItem('myDepartAndChildrenTree');
  const rawData = treeObj ? JSON.parse(treeObj) : [];
  treeData.value = transformTreeData(rawData);
  // 获取所属部门
  const departList = localStorage.getItem('myDepartList');
  departmentOptions.value = departList && departList !== 'null' ? JSON.parse(departList) : [];

  // treeValue.value = departmentOptions.value && departmentOptions.value.length > 0 ? departmentOptions.value[0].orgCode : '';
};
watch(treeValue, () => {
  getLabelList();
  // 清空问题类型下拉框选中项
  selectLabelIds.value = [];
});
onMounted(async () => {
  await getDepartmentList();
  getOptionsList();
  if (props.editRowId) {
    getData();
  }
});
</script>


<style lang="less" scoped>
.report-modal {
  width: 1200px;
  height: 650px;
  background-color: #fff;
  border-radius: 2px;
}
.report-form {
  padding-top: 20px;
}

.form-select {
  display: flex;
  flex-wrap: wrap;
  > div {
    width: 250px;
    margin-right: 10px;
  }
}
:deep(.ant-select-tree .ant-select-tree-node-content-wrapper) {
  max-width: 100%; /* 确保文字不溢出 */
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.report-form-list {
  display: flex;
  flex-wrap: wrap;
  padding: 12px 0 0 12px;
  border: 1px solid #eeeeee;
  border-radius: 2px;
  height: 346px;
  overflow-y: auto;
  align-content: flex-start;
  .report-form-item {
    width: 247px;
    height: 99px;
    border: 1px solid #eeeeee;
    margin-right: 12px;
    margin-bottom: 12px;
    padding: 8px;
    font-size: 12px;
    border-radius: 2px;
    cursor: pointer;
    &.active {
      border: 1px solid #1777ff;
      background-color: rgba(23, 119, 255, 0.05);
      .item-bottom {
        background-color: #fff;
      }
    }
    .item-top {
      display: flex;
      .item-img {
        width: 76px;
        height: 49px;
        margin-right: 10px;
        border-radius: 2px;
        overflow: hidden;
        img {
          width: 100%;
          height: 100%;
        }
      }
      .item-info {
        .item-name {
          max-width: 144px;
          margin-bottom: 8px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .item-class > span {
          max-width: 144px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
    .item-bottom {
      line-height: 24px;
      margin-top: 6px;
      background-color: #fafafa;
      color: #666;
      padding: 0 6px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

.report-modal-bottom {
  height: 74px;
  .bottom-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 74px;
    border-top: 1px solid #eaeaea;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 0 20px;
  }
  .number {
    color: #1777ff;
    margin: 0 4px;
  }
  .bottom-btn {
    display: flex;
    > button {
      margin-left: 15px;
    }
  }
}
</style>
<style>
.report-form .ant-select-selection-overflow {
  flex-wrap: nowrap;
  overflow: hidden;
  width: 200px;
}
</style>