<template>
  <BasicModal
    :width="550"
    :minHeight="180"
    :height="180"
    title="批量下载"
    @register="register"
    @cancel="cancelModal"
    @ok="okModal"
    :canFullscreen="false"
  >
    <div class="moveFileBox">
      <div class="title">批量下载会先进行压缩,后续请选择如下压缩包下载,可在“根目录-批量下载文件夹”中查看压缩进度</div>
      <a-form :model="modelForm" ref="modelFormRef" labelAlign="left">
        <a-form-item label="压缩包名称" name="packageName" :label-col="{ span: 5 }" :rules="[{ required: true, message: '请输入压缩包名称' }]">
          <a-input v-model:value="modelForm.packageName" />
        </a-form-item>
        <a-form-item name="imgOptions" :label-col="{ span: 6 }" :rules="[{ required: true, message: '请选择照片下载选项' }]">
          <template #label>
            <span>
              照片下载选项
              <Tooltip title="此设置仅对普通照片有效，全景图、视频仅可下载原始数据。" placement="bottom">
                <InfoCircleTwoTone />
              </Tooltip>
            </span>
          </template>
          <a-checkbox-group v-model:value="modelForm.imgOptions" style="width: 100%">
            <a-checkbox value="1">原照片</a-checkbox>
            <a-checkbox value="2">有标注的照片</a-checkbox>
          </a-checkbox-group>
        </a-form-item>
      </a-form>
    </div>
  </BasicModal>
</template>
<script lang="ts" setup>
import { onMounted, computed, ref, reactive, defineProps, watch, defineEmits } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import { mulDownload } from '../data.api';
import { useMessage } from '/@/hooks/web/useMessage';
import dayjs from 'dayjs';
import { Tooltip } from 'ant-design-vue';
import { InfoCircleTwoTone } from '@ant-design/icons-vue';
const dirIdList = ref([]);
const fileIdList = ref([]);
const modelFormRef = ref(null);
const sysOrgCode = ref('');
// const showImgOptions = ref(false);
const modelForm = ref({
  packageName: '【批量压缩】' + dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss'),
  imgOptions: [],
});

const { createMessage, createConfirm } = useMessage();
const [register, { closeModal }] = useModalInner(async (data) => {
  console.log('ModalInner', data);
  sysOrgCode.value = data[0].sysOrgCode;
  data?.forEach((item) => {
    if (item.isDir) {
      dirIdList.value.push(item.id);
    } else {
      fileIdList.value.push(item.id);
    }
  });
  // showImgOptions.value = data.find((item) => item.fileType === '1');
});
const emits = defineEmits(['success', 'register']);

onMounted(() => {
  //initData()
});

const okModal = async () => {
  modelFormRef.value
    .validate()
    .then(async () => {
      //创建压缩任务
      mulDownload({
        fileIdList: fileIdList.value,
        dirIdList: dirIdList.value,
        taskName: modelForm.value.packageName + '.zip',
        imgOptions: modelForm.value.imgOptions,
        sysOrgCode: sysOrgCode.value,
      });
      submitSuccess();
    })
    .catch((errorInfo) => {
      // 校验未通过
      console.error('校验未通过:', errorInfo);
    });
};
const submitSuccess = () => {
  emits('success');
  cancelModal();
};
const cancelModal = () => {
  modelFormRef.value.resetFields();
  // 手动重置 modelForm 的值
  modelForm.value = {
    packageName: '【批量压缩】' + dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss'),
    imgOptions: [],
  };
  dirIdList.value = [];
  fileIdList.value = [];
  closeModal();
};
</script>
<style lang="less" scoped>
/deep/.scroll-container {
  padding: 0;
}

.moveFileBox {
  .title {
    font-size: 14px;
    color: #666666;
    margin-bottom: 20px;
  }
}
</style>
