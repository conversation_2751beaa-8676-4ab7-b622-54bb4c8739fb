<template>
  <BasicModal
    @register="registerModal"
    :title="isModelDir ? '导入模型' : '上传文件'"
    :width="950"
    @cancel="okModal"
    @ok="okModal"
    :maskClosable="false"
    :canFullscreen="false"
  >
    <div class="head-title-box">
      <div class="title">上传路径</div>
      <div class="ericLine"></div>
      <div class="path">
        <FolderOpenFilled style="color: #ffb32b; font-size: 20px" />
        <div class="text">{{ dirNavigation }}</div>
      </div>
    </div>
    <div v-if="isModelDir" style="margin-top: 10px"
      >仅支持tif格式的文件(不超过3G)或包含pnts
      <Tooltip placement="right" color="white">
        <template #title>
          <p class="file-example">格式文件示例</p>
          <img src="@/assets/icons/dataManage/pnts.png" style="width: 150px; height: auto" />
        </template>
        <InfoCircleTwoTone /> </Tooltip
      >、b3dm
      <Tooltip placement="right" color="white">
        <template #title>
          <p class="file-example">格式文件示例</p>
          <img src="@/assets/icons/dataManage/b3dm.png" style="width: 150px; height: auto" />
        </template>
        <InfoCircleTwoTone /> </Tooltip
      >文件的zip压缩包(不超过10G)。</div
    >
    <div style="margin-top: 10px; overflow-y: scroll; height: 441px">
      <a-upload
        list-type="picture-card"
        v-model:fileList="fileList"
        :multiple="true"
        :beforeUpload="beforeUpload"
        :progress="uploadProgress"
        @change="handleChange"
        @remove="removeFile"
        @preview="handlePreview"
      >
        <div>
          <div class="text">选择文件</div>
        </div>
      </a-upload>
      <a-modal :open="previewVisible" :title="previewTitle" :footer="null" @cancel="handleCancel">
        <img alt="example" style="width: 100%" :src="previewImage" />
      </a-modal>
    </div>

    <template #footer>
      <div class="footerBox">
        <!-- <a-button @click="aaa123">上传文件</a-button> -->
        <div class="uploadResult">
          <!-- <CheckCircleOutlined class="icon" />
          <div class="complete">上传已完成</div> -->
          <div class="total"
            >共<span class="number">{{ fileList?.length || 0 }}</span
            >个文件</div
          >
          <div class="success"
            >成功上传<span class="number">{{ successNum }}</span
            >个</div
          >
          <div class="fail"
            >失败<span class="number">{{ failNum }}</span
            >个</div
          >
        </div>
      </div>
    </template>
  </BasicModal>
</template>

<script lang="ts" setup>
import { BasicModal, useModalInner } from '/@/components/Modal';
import { ref, computed, defineProps } from 'vue';
import { useGlobSetting } from '/@/hooks/setting';
import type { UploadProps } from 'ant-design-vue';
import { uploadMyFileTwo, uploadMyFile } from '/@/api/common/api';
import { delSingleFile, checkShard } from '../data.api';
import SparkMD5 from 'spark-md5';
import {
  FolderOpenFilled,
  CloudUploadOutlined,
  CloseCircleFilled,
  CheckCircleOutlined,
  CloseCircleOutlined,
  PlusOutlined,
  InfoCircleTwoTone,
} from '@ant-design/icons-vue';
import { error } from 'console';
import JSZip from 'jszip';
import { message, Tooltip, Upload } from 'ant-design-vue';

interface FileItem {
  uid: string;
  name?: string;
  status?: string;
  response?: string;
  url?: string;
}

interface FileInfo {
  file: FileItem;
  fileList: FileItem[];
}

const props = defineProps({
  dirList: {
    type: Array,
    default: [],
  },
  isModelDir: {
    type: Boolean,
    default: false,
  },
});

const [registerModal, { closeModal }] = useModalInner();
const fileList = ref<any[]>([]);
let fileMd5 = ref();
const previewVisible = ref(false);
const previewImage = ref('');
const previewTitle = ref('');
const emits = defineEmits(['success', 'register']);
const { uploadUrl, apiUrl } = useGlobSetting();
const uploadFileUrl = `/file/manage/uploadByDir`;

//const uploadFileUrl = `${uploadUrl}/file/manage/uploadByDir`;  // 本地local环境使用IP，不需要加上apiUrl ！

// const uploadShardUrl = `/file/manage/upload-shard`;
const uploadShardUrl = computed(() => (props.isModelDir ? '/skyline-uav/uav/modeling/file/upload-shard' : '/file/manage/upload-shard'));
const checkShardUrl = computed(() => (props.isModelDir ? '/skyline-uav/uav/modeling/file/check-shard' : '/file/manage/check-shard'));
// const uploadShardUrl = `${uploadUrl}/file/manage/upload-shard`;// 本地local环境使用IP，不需要加上apiUrl ！
let shardChecked = ref(false); //添加一个变量来保存 checkShard 的结果
let checkResult = ref(); //校验结果
console.log(uploadUrl, apiUrl);

const fileQueue = ref([]); // 用于存储待上传的文件
const concurrentLimit = ref(2); // 每次并发上传的文件数
const activeUploads = ref(0); // 当前正在上传的文件数
const uploadProgress = {
  strokeColor: {
    '0%': '#108ee9',
    '100%': '#87d068',
  },
  strokeWidth: 2,
  format: (percent) => `${parseFloat(percent.toFixed(2))}%`,
  class: 'progress-percent',
};
const dirNavigation = computed(() =>
  props.dirList.reduce((prev, next: any, index) => {
    console.log('props.dirList', props.dirList);
    console.log('prev', prev);
    console.log('next', next);
    console.log('index', index);
    const lastItem = index > 0 && index === props.dirList?.length - 1;
    prev += `${next?.name} ${!lastItem ? '/' : ''}`;
    return prev;
  }, '')
);

const successNum = computed(() =>
  fileList.value?.reduce((prev, next: any) => {
    console.log(prev, next, fileList.value);
    if (next?.response?.result?.successList) {
      prev += next?.response?.result?.successList[0]?.fileId ? 1 : 0;
    } else if (next?.status === 'done' && next?.percent) {
      prev += next.percent == 100 ? 1 : 0;
    } else {
      prev += next?.response?.data?.success == true ? 1 : 0;
    }

    return prev;
  }, 0)
);
const failNum = computed(() =>
  fileList.value?.reduce((prev, next: any) => {
    prev += next.status === 'error' || (next.status === 'done' && next?.response && next?.response?.result?.failList?.length) ? 1 : 0;
    return prev;
  }, 0)
);

const calculateChunkMD5 = (file, start, end) => {
  return new Promise((resolve, reject) => {
    const chunk = file.slice(start, end);
    const reader = new FileReader();
    const spark = new SparkMD5.ArrayBuffer();

    reader.onload = (e) => {
      spark.append(e.target.result);
      resolve(spark.end());
    };

    reader.onerror = (e) => {
      reject(e);
    };

    reader.readAsArrayBuffer(chunk);
  });
};

const uploadChunks = async (file, chunkSize, Suffix, e) => {
  const totalChunks = Math.ceil(file.size / chunkSize);
  for (let index = 0; index < totalChunks; index++) {
    console.log(index, totalChunks, 'index-----------------------');
    // 上传当前分片
    file.md5 !== '' && (await uploadFileChunk(file, index, chunkSize, Suffix, totalChunks, e));

    // 更新上传进度
    const progress = Math.round(((index + 1) / totalChunks) * 100);
    const fileIndex = fileList.value.findIndex((item) => item.uid === file.uid);
    if (fileIndex !== -1) {
      fileList.value[fileIndex].percent = progress; // 更新当前文件的上传进度
    }
  }
  shardChecked.value = false;
  e.onSuccess({ data: { success: true } });
  // fileMd5.value = '';
  file.md5 = ''; // 上传完成后清空当前文件的 MD5
  console.log('循环结束1231321131');
};

const uploadFileChunk = async (file, index, chunkSize, Suffix, totalChunks, e) => {
  const start = index * chunkSize;
  const end = Math.min(start + chunkSize, file.size);
  let url = uploadShardUrl.value;
  const curDirListTemp = props.dirList;
  const parentItem: any = curDirListTemp[curDirListTemp?.length - 1];
  const chunk = file.slice(start, end);
  const formData = new FormData();
  formData.append('file', chunk);
  const selectedDepart = localStorage.getItem('selectedDepart');
  const sysOrgCode = selectedDepart ? JSON.parse(selectedDepart)[0] : null;

  if (index == 0) {
    // fileMd5.value = await calculateChunkMD5(file, start, end);
    file.md5 = await calculateChunkMD5(file, start, end);
  }
  // formData.append('index', index);

  // 发送分片数据到服务器进行上传
  // console.log(`分片上传：正在上传第 ${index + 1} 片，大小为 ${end - start}，MD5：${Md5}，文件后缀：${Suffix}，文件总数：${totalChunks}，文件总大小：${file.size}`);

  // 模拟上传延迟
  // await new Promise((resolve) => setTimeout(resolve, 2000));
  await new Promise((resolve, reject) => {
    console.log(checkShardUrl, '999----------------------');
    if (!shardChecked.value) {
      checkShard(checkShardUrl.value, { md5: file.md5, sysOrgCode }).then((ress) => {
        shardChecked.value = true;
        checkResult.value = ress.data.result;
        if (ress.data.code != 200) {
          message.error(ress.data.message);
          return;
        }

        if (checkResult.value == null) {
          url =
            url +
            `?dirId=${parentItem.id}&fileName=${file.name.split('.').slice(0, -1).join('.')}&fileSize=${file.size}&fileSuffix=${Suffix}&md5=${
              file.md5
            }&shardIndex=${index + 1}&shardSize=${end - start}&shardTotal=${totalChunks}&sysOrgCode=${sysOrgCode}`;
          uploadMyFileTwo(url, formData).then((res) => {
            console.log(res);
            if (res.data.code == 200) {
              console.log(`分片上传：第 ${index + 1} 片上传成功`, res);
              resolve(true);
            } else {
              e.onError(res.data.message);
              reject(false);
            }
          });
        } else {
          //表示上传过
          if (checkResult.value.objectKey == null) {
            if (index + 1 >= checkResult.value.shardIndex + 1) {
              url =
                url +
                `?dirId=${parentItem.id}&fileName=${file.name.split('.').slice(0, -1).join('.')}&fileSize=${file.size}&fileSuffix=${Suffix}&md5=${
                  file.md5
                }&shardIndex=${index + 1}&shardSize=${end - start}&shardTotal=${totalChunks}&sysOrgCode=${sysOrgCode}`;
              uploadMyFileTwo(url, formData).then((res) => {
                console.log(res);
                if (res.data.code == 200) {
                  console.log(`分片上传：第 ${index + 1} 片上传成功`, res);
                  resolve(true);
                } else {
                  e.onError(res.data.message);
                  reject(false);
                }
              });
            } else {
              //这一片上传过跳过本次提交
              resolve(true);
            }
          } else {
            url =
              url +
              `?dirId=${parentItem.id}&fileName=${file.name.split('.').slice(0, -1).join('.')}&fileSize=${file.size}&fileSuffix=${Suffix}&md5=${
                file.md5
              }&shardIndex=${index + 1}&shardSize=${end - start}&shardTotal=${totalChunks}&objectKey=${
                checkResult.value.objectKey
              }&sysOrgCode=${sysOrgCode}`;
            uploadMyFileTwo(url, formData).then((res) => {
              console.log(res);
              if (res.data.code == 200) {
                shardChecked.value = false;
                e.onSuccess({ data: { success: true } });
                file.md5 = '';
                console.log('循环结束1231321131');
                resolve();
              } else {
                e.onError(res.data.message);
                reject(false);
              }
            });
          }
        }
      });
    } else {
      if (checkResult.value == null) {
        url =
          url +
          `?dirId=${parentItem.id}&fileName=${file.name.split('.').slice(0, -1).join('.')}&fileSize=${file.size}&fileSuffix=${Suffix}&md5=${
            file.md5
          }&shardIndex=${index + 1}&shardSize=${end - start}&shardTotal=${totalChunks}&sysOrgCode=${sysOrgCode}`;
        uploadMyFileTwo(url, formData).then((res) => {
          if (res.data.code == 200) {
            console.log(`分片上传：第 ${index + 1} 片上传成功`, res);
            resolve(true);
          } else {
            e.onError(res.data.message);
            reject(false);
          }
        });
      } else {
        //表示上传过
        if (checkResult.value.objectKey == null) {
          if (index + 1 >= checkResult.value.shardIndex + 1) {
            url =
              url +
              `?dirId=${parentItem.id}&fileName=${file.name.split('.').slice(0, -1).join('.')}&fileSize=${file.size}&fileSuffix=${Suffix}&md5=${
                file.md5
              }&shardIndex=${index + 1}&shardSize=${end - start}&shardTotal=${totalChunks}&sysOrgCode=${sysOrgCode}`;
            uploadMyFileTwo(url, formData).then((res) => {
              if (res.data.code == 200) {
                console.log(`分片上传：第 ${index + 1} 片上传成功`, res);
                resolve(true);
              } else {
                e.onError(res.data.message);
                reject(false);
              }
            });
          } else {
            //这一片上传过跳过本次提交
            resolve(true);
          }
        } else {
          url =
            url +
            `?dirId=${parentItem.id}&fileName=${file.name.split('.').slice(0, -1).join('.')}&fileSize=${file.size}&fileSuffix=${Suffix}&md5=${
              file.md5
            }&shardIndex=${index + 1}&shardSize=${end - start}&shardTotal=${totalChunks}&objectKey=${
              checkResult.value.objectKey
            }&sysOrgCode=${sysOrgCode}`;
          uploadMyFileTwo(url, formData).then((res) => {
            if (res.data.code == 200) {
              shardChecked.value = false;
              e.onSuccess({ data: { success: true } });
              file.md5 = '';
              console.log('循环结束1231321131');
              reject(false);
            } else {
              reject(false);
              e.onError(res.data.message);
            }
          });
        }
      }
    }
  });
};

const removeFile = (file) => {
  //console.log("删除文件--file", file)
  const fileId = file?.response?.result?.successList[0]?.fileId;
  return new Promise(async function (resolve, reject) {
    if (!fileId) {
      // 上传失败的文件直接删除本地的
      resolve(true);
      return;
    }
    // 删除服务器文件
    const res = await delSingleFile({ id: fileId });
    //console.log("删除文件==res", res)
    const { data } = res;
    if (data.code === 200) {
      resolve(true);
    } else {
      reject();
    }
  });
};

const handleCancel = () => {
  previewVisible.value = false;
  previewTitle.value = '';
};

const handlePreview = async (file: UploadProps['fileList'][number]) => {
  const fileUrl = file?.response?.result?.successList[0]?.url;
  if (!file.preview) {
    file.preview = fileUrl;
  }
  previewImage.value = fileUrl;
  previewVisible.value = true;
  previewTitle.value = file.name;
};

function okModal() {
  fileList.value = [];
  emits('success');
  closeModal();
}
const handleChange = async ({ file, fileList: newFileList }) => {
  fileList.value = newFileList.map((file) => {
    // 为每个文件设置初始状态
    return {
      ...file,
      status: file.status || 'uploading', // 如果没有状态则设置为上传中
      percent: file.percent || 0,
    };
  });
};
const beforeUpload = async (file) => {
  // 仅在模型库导入模块时校验，在用户文件夹上传文件无需校验
  if (props.isModelDir) {
    console.log('file==>>', file);
    const isValid = await validateFile(file);
    console.log('isValid==>>', isValid);
    if (!isValid) {
      return false || Upload.LIST_IGNORE;
    }
    // return isValid;
  }
  // 在添加到队列之前，将状态设置为“上传中”
  file.status = 'uploading';
  fileList.value.push(file); // 直接将文件添加到 fileList
  // 使用手动触发上传，将 file 和 e 对象加入队列
  const e = {
    file,
    onSuccess: (data) => {
      // 上传成功时更新文件状态
      const index = fileList.value.findIndex((item) => item.uid === file.uid);
      if (index !== -1) {
        fileList.value[index].status = 'done';
        fileList.value[index].percent = 100; // 设置完成百分比
      }
      console.log('上传成功', data);
    },
    onError: (err) => {
      // 上传失败时更新文件状态
      const index = fileList.value.findIndex((item) => item.uid === file.uid);
      if (index !== -1) {
        fileList.value[index].status = 'error'; // 设置为错误状态
      }
      console.log('上传失败', err);
    },
  };
  console.log('file==>>', file);
  fileQueue.value.push({ file, e }); // 将文件加入队列
  processUploadQueue(); // 开始处理上传队列
  return false; // 阻止自动上传
};
// 控制上传队列的处理逻辑
const processUploadQueue = async () => {
  const selectedDepart = localStorage.getItem('selectedDepart');
  const sysOrgCode = selectedDepart ? JSON.parse(selectedDepart)[0] : null;
  console.log('sysOrgCode==>>', sysOrgCode);
  while (activeUploads.value < concurrentLimit.value && fileQueue.value.length > 0) {
    const { file, e } = fileQueue.value.shift(); // 从队列中取出一个文件
    console.log('777file', file);
    console.log('777e', e);
    activeUploads.value++; // 增加当前上传文件数

    const formData = new FormData();
    formData.append('file', file);
    let url = uploadFileUrl;
    const curDirListTemp = props.dirList;
    const parentItem: any = curDirListTemp[curDirListTemp?.length - 1];
    url = url + `?dirId=${parentItem.id}&sysOrgCode=${sysOrgCode}&deptQueryType=CURRENT`;

    try {
      if (file.size < 10 * 1024 * 1024 && !props.isModelDir) {
        // 小于 10MB 直接上传
        const res = await uploadMyFile(url, formData);
        console.log('res==>>', res);
        if (res?.successList?.length) {
          e.onSuccess({ data: { success: true } }); // 上传成功回调
          // 更新 fileList 中对应文件的状态
          const index = fileList.value.findIndex((item) => item.uid === file.uid);
          if (index !== -1) {
            fileList.value[index] = {
              ...fileList.value[index],
              status: 'done',
              percent: 100,
              md5: '', // 如果需要，可以在这里设置实际的 md5 值
              xhr: {
                action: url,
                filename: 'file',
                data: {},
                file,
                headers: {},
                withCredentials: false,
                method: 'post',
              },
            };
          }
        } else {
          e.onError(); // 上传失败回调
        }
      } else {
        // 大于 10MB 进行分片上传
        shardChecked.value = false;
        await uploadChunks(file, 9 * 1024 * 1024, `${'.' + file.name.split('.').pop()}`, e);
      }
    } catch (err) {
      e.onError(err); // 捕捉上传错误

      const index = fileList.value.findIndex((item) => item.uid === file.uid);
      if (index !== -1) {
        fileList.value.splice(index, 1);
      } else {
        return false;
      }
      // 在失败的情况下，也可以考虑从 fileList 中移除文件
      // const index = fileList.value.indexOf(file);
      // if (index !== -1) {
      //   fileList.value.splice(index, 1); // 从 fileList 中移除该文件
      // }
    } finally {
      activeUploads.value--; // 上传完成，减少当前上传文件数
      processUploadQueue(); // 继续处理队列中的下一个文件
    }
  }
};
const validateFile = async (file) => {
  const allowedFormats = ['tif', 'zip'];
  const fileExtension = file.name.split('.').pop().toLowerCase();

  // 校验文件大小(zip不超过10G,其他不超过3G)
  if (fileExtension === 'zip') {
    if (file.size > 1024 * 1024 * 1024 * 10) {
      message.error('文件大小超出限制，请重新选择文件。');
      return false;
    }
  } else {
    if (file.size > 1024 * 1024 * 1024 * 3) {
      message.error('文件大小超出限制，请重新选择文件。');
      return false;
    }
  }

  // 校验文件后缀名
  if (!allowedFormats.includes(fileExtension)) {
    message.error('文件格式不正确。仅支持tif格式的文件或包含pnts、b3dm文件的zip压缩包。');
    return false;
  }

  // 如果是zip文件，则校验压缩包里的文件
  if (fileExtension === 'zip') {
    // 压缩包文件太大时，使用JSzip会因为性能限制直接进入catch返回false,前端会拦截下来。因此文件太大，放开限制上传给后台校验。
    if (file.size > 1024 * 1024 * 1024 * 2) {
      return true;
    }
    const containsRequiredFiles = await validateZipFile(file);
    if (!containsRequiredFiles) {
      message.error('文件格式不正确。仅支持tif格式的文件或包含pnts、b3dm文件的zip压缩包。');
      return false;
    }
  }

  return true;
};

const validateZipFile = async (file) => {
  const zip = new JSZip();
  try {
    const loadedZip = await zip.loadAsync(file, { createFolders: true });

    // 使用生成器函数逐个处理文件
    for (const fileName in loadedZip.files) {
      if (loadedZip.files[fileName].dir) {
        continue; // 如果是目录，跳过
      }

      // 直接检查文件名
      if (fileName.endsWith('.pnts') || fileName.endsWith('.b3dm')) {
        console.log('Found valid file:', fileName);
        return true; // 一旦找到目标文件，立即返回true
      }
    }

    console.log('No valid files found');
    return false; // 如果没有找到目标文件，返回false
  } catch (error) {
    console.error('Error reading zip file:', error);
    return false;
  }
};
</script>

<style lang="less" scoped>
/deep/.ant-upload-select-picture-card i {
  font-size: 32px;
  color: #999;
}

/deep/.ant-upload-select-picture-card .ant-upload-text {
  margin-top: 8px;
  color: #666;
}

.uploadState {
  background: rgba(27, 135, 194, 0.08);
  height: 40px;
  margin-bottom: 22px;
  display: flex;
  align-items: center;
  padding: 0 16px;

  .total {
    display: flex;
    align-items: center;
    margin-right: 17px;

    .icon {
      color: #7db7d6;
      margin-right: 5px;
    }

    .number {
      color: #1b719f;
      margin: 0 4px;
    }

    .fileSize {
      color: #1b719f;
      margin-left: 4px;
    }
  }

  .uploadProgress {
    display: flex;
    align-items: center;
    width: 225px;

    .icon {
      color: #499fff;
      margin-right: 9px;
    }

    .text {
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #666666;
      width: 100px;
    }

    .ProgressBox {
      width: 160px;
      display: flex;
      justify-content: center;
    }
  }

  .uploadSpeed {
    display: flex;
    align-items: center;
    margin-left: 11px;

    .size {
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #666666;
      margin-right: 22px;
    }

    .speed {
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #666666;
      margin-right: 31px;
    }

    .tiem {
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #666666;
    }
  }

  .cancelButton {
    width: 76px;
    height: 30px;
    border-radius: 16px;
    border: 1px solid rgba(22, 125, 181, 0.5);
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: rgba(22, 125, 181, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 6px;
    cursor: pointer;
  }
}

.head-title-box {
  display: flex;
  align-items: center;
  background: linear-gradient(180deg, #e8f3f8 0%, rgba(232, 243, 248, 0) 100%);
  height: 40px;
  border: 1px solid rgba(11, 121, 181, 0.6);
  padding: 0 13px;

  .title {
    color: #60a7ce;
    font-size: 14px;
    font-weight: 500;
    font-family: PingFangSC-Medium, PingFang SC;
  }

  .ericLine {
    width: 1px;
    height: 20px;
    background-color: #cccccc;
    margin: 0 14px;
  }

  .path {
    display: flex;

    .text {
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #666666;
      margin-left: 6px;
    }
  }
}
.file-example {
  color: #515a6e;
}
/deep/ .ant-upload {
  .prompt {
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .text {
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #60a7ce;
    }
  }
}

.fileList {
  padding: 0 20px;
  display: flex;
  flex-wrap: wrap;

  .fileBox {
    flex: 0 0 calc(14%);
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;

    .deleteIcon {
      position: absolute;
      top: -10px;
      right: 6px;
      z-index: 1;
      font-size: 22px;
      color: rgba(107, 108, 109, 0.8);
      background-color: #ffffff;
      border-radius: 50%;
      display: none;
    }

    .fileImg {
      position: relative;
      width: 88px;
      height: 86px;
      border-radius: 4px;
      overflow: hidden;

      .uploadStatus {
        position: absolute;
        width: 15px;
        height: 15px;
        bottom: 3px;
        right: 3px;

        .icon {
          color: #56c89f;
        }

        .icon2 {
          color: #eb2f96;
        }
      }
    }

    .text {
      margin-top: 8px;
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      width: 88px;
      height: 40px;
      overflow: hidden;
      white-space: pre-wrap;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }
  }

  .fileBox:hover {
    .deleteIcon {
      display: block;
    }
  }
}

.jeecg-modal-content /deep/ .scroll-container {
  padding: 0px;
}

.footerBox {
  display: flex;

  .uploadResult {
    color: #666666;
    display: flex;
    align-items: center;
    margin-left: 30px;

    .icon {
      color: #56c89f;
      margin-right: 5px;
    }

    .complete {
      margin-right: 18px;
    }

    .total {
      margin-right: 5px;

      .number {
        color: #156d9c;
      }
    }

    .success {
      margin-right: 5px;

      .number {
        color: #56c89f;
      }
    }

    .fail {
      .number {
        color: #ee5555;
      }
    }
  }
}

/deep/ .ant-upload-drag {
  background-color: #fafdff;
  border: 1px dashed #60a7ce;
}

/deep/ .ant-progress-line {
  line-height: 0px;
}
</style>
