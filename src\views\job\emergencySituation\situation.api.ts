import { defHttp } from '/@/utils/http/axios';

enum Api {
  getList = '/uav/alarm/record/list', //获取列表
  taskStatusEnum = '/uav/task/queryTaskStatusEnum', // 任务状态枚举
  alarmDeviceList = '/uav/alarm/record/select/alarmDeviceList', // 上报设备列表
  executionDeviceList = '/uav/alarm/record/select/executionDeviceList', // 执行设备列表
}

export const situationApi = {
  getList: (params) => defHttp.post({ url: Api.getList, params }),
  getTaskStatusEnum: (params) => defHttp.get({ url: Api.taskStatusEnum, params }),
  getAlarmDeviceList: (params) => defHttp.get({ url: Api.alarmDeviceList, headers: {'X-Sys-Multi-Org-Code': `${params.sysOrgCode}`} }),
  getExecutionDeviceList: (params) => defHttp.get({ url: Api.executionDeviceList, headers: {'X-Sys-Multi-Org-Code': `${params.sysOrgCode}`}}),
};
