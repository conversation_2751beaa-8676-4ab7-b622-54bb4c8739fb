<template>
  <BasicModal
    :width="600"
    :height="450"
    title="数据分享"
    @register="register"
    @cancel="cancelModal"
    @ok="okModal"
    :showCancelBtn="!showResult"
    :showOkBtn="!showResult"
    :canFullscreen="false"
    :maskClosable="false"
  >
    <div class="share-options" v-show="!showResult">
      <a-form :model="modelForm" ref="modelFormRef" labelAlign="left">
        <a-form-item label="选中数据" name="selectData" :label-col="{ span: 4 }">
          <div class="list-box">
            <p v-for="item in selectDataList" :key="item.fileId">{{ item.fileName }}</p>
          </div>
        </a-form-item>
        <a-form-item label="有效期" name="expireTime" :label-col="{ span: 4 }" :rules="[{ required: true, message: '请选择有效期！' }]">
          <a-radio-group v-model:value="modelForm.expireTime" style="width: 100%">
            <a-radio value="1">1天</a-radio>
            <a-radio value="7">7天</a-radio>
            <a-radio value="30">30天</a-radio>
            <a-radio value="90">90天</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="提取码" name="passWordType" :label-col="{ span: 4 }" :rules="[{ required: true, message: '请选择提取码类型！' }]">
          <a-radio-group v-model:value="modelForm.passWordType" style="width: 100%" @change="onExtractCodeChange">
            <a-radio value="0" :style="radioStyle">免密分享</a-radio>
            <a-radio value="1" :style="radioStyle">系统随机生成</a-radio>
            <a-radio value="2" :style="radioStyle"
              >自定义
              <a-form-item
                v-if="modelForm.passWordType === '2'"
                name="passWord"
                :label-col="{ span: 4 }"
                :rules="[
                  { required: true, message: '请输入提取码！' },
                  { pattern: /^[A-Za-z0-9]{4}$/, message: '提取码必须为4位数字或字母！' },
                ]"
                style="display: inline-block"
              >
                <a-input v-model:value="modelForm.passWord" style="width: 216px" placeholder="4位数字或字母,字母区分大小写" :maxlength="4" />
              </a-form-item>
            </a-radio>
          </a-radio-group>
        </a-form-item>
      </a-form>
    </div>
    <div class="share-result" v-show="showResult">
      <div class="link-psd">
        <div>链接：{{ shareLink }}</div>
        <div>密码：{{ sharePsd }}</div>
        <span>链接和二维码 {{ shareExpireTime }} 天 后失效，请在有效期内使用。</span>
        <a-button type="primary" @click="copyLink" block style="width: 100px">复制链接</a-button>
      </div>
      <div class="qr-code-box">
        <img class="qr" :src="qrCode" />
        <span>扫码在移动端查看数据</span>
      </div>
    </div>
  </BasicModal>
</template>

<script lang="ts" setup>
import { onMounted, ref, reactive } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import { mulDownload, addShare, queryAllFile } from '../data.api';
import { useMessage } from '/@/hooks/web/useMessage';
import QRCode from 'qrcode'; // 引入qrcode库
import { useRoute, useRouter } from 'vue-router';

const router = useRouter();
const showResult = ref(false);
const qrCode = ref('');
const selectDataList = ref([]);
const modelFormRef = ref(null);
const modelForm = ref({
  expireTime: '1',
  passWordType: '0',
  passWord: null,
});
const fileIdList = ref([]);
const dirIdList = ref([]);
const shareLink = ref('');
const sharePsd = ref('');
const shareExpireTime = ref('');
const sysOrgCode = ref('');

const { createMessage, createConfirm } = useMessage();
const [register, { closeModal }] = useModalInner(async (data) => {
  console.log('data', data);
  sysOrgCode.value = data[0].sysOrgCode;
  data.forEach((item) => {
    if (item.isDir) {
      dirIdList.value.push(item.dirId);
    } else {
      fileIdList.value.push(item.fileId);
    }
  });
  const res = await queryAllFile({
    fileIdList: fileIdList.value,
    dirIdList: dirIdList.value,
  });
  console.log('res===', res);

  selectDataList.value = res;
});
const emits = defineEmits(['success', 'register']);
const radioStyle = reactive({
  display: 'flex',
  height: '30px',
  lineHeight: '30px',
});
onMounted(() => {
  // initData()
});

// 提取码变化时触发
const onExtractCodeChange = () => {
  if (modelForm.value.passWordType !== '2') {
    modelForm.value.passWord = null; // 如果选非自定义提取码，清空自定义密码
  }
};

// 确认按钮点击
const okModal = async () => {
  try {
    await modelFormRef.value.validate();
    const res = await addShare({
      fileIdList: fileIdList.value,
      dirIdList: dirIdList.value,
      expireTime: modelForm.value.expireTime,
      passWordType: modelForm.value.passWordType,
      passWord: modelForm.value.passWord,
      sysOrgCode: sysOrgCode.value,
    });
    console.log('res===', res);
    showResult.value = true;

    shareLink.value = res.shareUrl;
    sharePsd.value = res.passWord;
    shareExpireTime.value = res.expireTime;
    qrCode.value = await QRCode.toDataURL(res.shareUrl);

    // submitSuccess();
  } catch (errorInfo) {
    console.error('校验未通过:', errorInfo);
  }
};

// 提交成功
const submitSuccess = () => {
  emits('success');
  cancelModal();
};

// 取消按钮点击
const cancelModal = () => {
  modelFormRef.value.resetFields();
  modelForm.value.passWord = null; //清空自定义密码
  showResult.value = false;
  fileIdList.value = [];
  dirIdList.value = [];
  closeModal();
};

const copyLink = () => {
  const textToCopy = sharePsd.value ? `链接：${shareLink.value} 密码：${sharePsd.value}` : `链接：${shareLink.value}`;
  // navigator.clipboard.writeText(textToCopy);
  // createMessage.success('复制成功！');
  if (navigator.clipboard && navigator.clipboard.writeText) {
    navigator.clipboard
      .writeText(textToCopy)
      .then(() => {
        createMessage.success('复制成功！');
      })
      .catch(() => {
        fallbackCopyTextToClipboard(textToCopy);
        console.log('使用了备用方案复制');
      });
  } else {
    fallbackCopyTextToClipboard(textToCopy);
    console.log('使用了备用方案复制2');
  }
};

const fallbackCopyTextToClipboard = (text) => {
  const textArea = document.createElement('textarea');
  textArea.value = text;
  document.body.appendChild(textArea);
  textArea.select();
  try {
    const successful = document.execCommand('copy');
    if (successful) {
      createMessage.success('复制成功！');
    } else {
      createMessage.error('复制失败，请手动复制。');
    }
  } catch (err) {
    console.error('备选方案复制失败：', err);
    createMessage.error('复制失败，请手动复制。');
  }
  document.body.removeChild(textArea);
};
</script>

<style lang="less" scoped>
/deep/.scroll-container {
  padding: 0;
}
.share-options {
  .list-box {
    height: 120px;
    overflow-y: auto;
    background: #f3f8fd;
    p {
      margin-bottom: 0;
    }
  }
  .custom-password {
    display: flex;
    align-items: center;

    input {
      width: 20%;
      margin-left: 17%;
    }
    span {
      margin-left: 10px;
      color: #ed6f6f;
    }
  }
}
.share-result {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  .link-psd {
    width: 320px;
    height: 250px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
  .qr-code-box {
    display: flex;
    flex-direction: column;
    align-items: center;
    .qr {
      width: 200px;
      height: 200px;
      background-color: #ccc;
    }
  }
}
</style>
