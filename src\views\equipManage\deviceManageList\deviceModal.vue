<template>
  <a-modal :visible="props.visible" :title="title" @ok="handleOk" @cancel="cancel" width="750px">
    <div class="ant-modal-body">
      <a-form ref="formRef" :model="formState" :rules="rules" :label-col="labelCol" :wrapper-col="wrapperCol" labelAlign="left">
        <a-row>
          <a-col :span="24">
            <a-form-item label="设备类型" name="deviceType" :wrapper-col="{ span: 20 }">
              <a-select
                v-model:value="formState.deviceType"
                :options="deviceTypeOptions"
                placeholder="请选择"
                allowClear
                :disabled="title == '编辑设备'"
              ></a-select> </a-form-item
          ></a-col>
          <a-col :span="24">
            <a-form-item label="所属部门" name="sysOrgCode" :wrapper-col="{ span: 20 }">
              <a-select v-model:value="formState.sysOrgCode" :options="departlist" placeholder="请选择" allowClear></a-select> </a-form-item
          ></a-col>
        </a-row>
        <a-divider />
        <a-row>
          <a-col :span="12">
            <a-form-item label="名称" name="deviceName"> <a-input v-model:value="formState.deviceName" placeholder="请输入内容" /> </a-form-item
          ></a-col>
          <a-col :span="12">
            <a-form-item label="品牌" name="producerId">
              <a-select
                v-model:value="formState.producerId"
                :options="deviceProducerOptions"
                placeholder="请选择"
                allowClear
                @change="handleBrandChange"
                :disabled="title == '编辑设备'"
              ></a-select> </a-form-item
          ></a-col>
        </a-row>
        <a-row>
          <a-col :span="12">
            <a-form-item label="型号" name="deviceModel">
              <a-select
                v-model:value="formState.deviceModel"
                :options="deviceModelOptions"
                :placeholder="!formState.producerId ? '请先选择品牌' : '请选择型号'"
                allowClear
                :disabled="!formState.producerId || title == '编辑设备'"
              ></a-select> </a-form-item
          ></a-col>
          <a-col :span="12">
            <a-form-item label="SN" name="deviceSn">
              <a-input v-model:value="formState.deviceSn" placeholder="请输入内容" :disabled="title == '编辑设备'" /> </a-form-item
          ></a-col>
        </a-row>
        <a-row>
          <a-col :span="24">
            <a-form-item label="安装位置" name="location" :wrapper-col="{ span: 20 }">
              <a-select
                ref="mapSelect"
                v-model:value="formState.location"
                show-search
                :placeholder="isInnerNet ? '点击选择安装位置' : '请选择或输入安装位置'"
                :default-active-first-option="false"
                :show-arrow="false"
                :filter-option="false"
                :not-found-content="null"
                :allowClear="false"
                @search="handleSearch"
                @click="toggleMap"
                :disabled="isInnerNet && showMap"
              >
                <a-select-option v-for="item in options" :key="item.gbCode" :value="item.gbCode" @click="currentSelect(item)">{{
                  item.address + item.name
                }}</a-select-option>
              </a-select>
              <!-- <a-input v-model:value="formState.installationSite" placeholder="请输入地址" @click="toggleMap" @change="changeVal" /> -->
              <div v-if="showMap" style="display: block; width: 100%; margin-top: 20px">
                <!-- 这里是地图组件或内容 -->
                <div id="MapContainerDevice"></div>
                <div class="input" style="width: 50%; position: absolute; top: 60px; left: 10px" v-if="isInnerNet">
                  <a-row>
                    <a-col :span="11" style="margin-right: 10px; height: 32px">
                      <a-form-item name="longitude" style="margin-bottom: 0">
                        <a-input v-model:value="formState.longitude" placeholder="请输入经度" @blur="validateMarker" /> </a-form-item
                    ></a-col>
                    <a-col :span="11" style="height: 32px">
                      <a-form-item name="latitude" style="margin-bottom: 0">
                        <a-input v-model:value="formState.latitude" placeholder="请输入纬度" @blur="validateMarker" /> </a-form-item
                    ></a-col>
                  </a-row>
                </div>
              </div> </a-form-item
          ></a-col>
        </a-row>
      </a-form>
    </div>
  </a-modal>
</template>
<script lang="ts" setup>
import { ref, nextTick, onMounted, watch, onUnmounted, computed, toRaw } from 'vue';
import * as mars3d from 'mars3d';
import * as Cesium from 'mars3d-cesium';
import defaultMapConfig from '../../mapView/map3d.config.json';
import { useBasemapConfig } from '@/hooks/web/useBasemapConfig';
import { message } from 'ant-design-vue';
import { getIconUrl } from '/@/utils';
import { validatorLongitude, validatorLatitude, exceedsSixDecimalPlaces } from '/@/utils/validateLngLat';
import type { SelectProps } from 'ant-design-vue';

import { deviceApi } from './data.api';
import { ValidateErrorEntity } from 'ant-design-vue/lib/form/interface';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  deviceDialogData: {
    type: Object,
  },
  departlist: {
    type: Array,
    default: () => [],
  },
});
const emit = defineEmits(['handleclose', 'handleSearch']);
const { configMap } = useBasemapConfig();
let isInnerNet = ref<boolean>(false); // 内外网标识

let formState = ref<any>({});
const showMap = ref<boolean>(false); //地图显隐
let formRef = ref();
let mapSelect = ref();
let map: any = null;
let mapbacklayer: any = null;
let currentMarker: any = null;
let QueryPOI: any = null;
let options: any = ref([]); // 搜索提示信息
// let isInnerNet = ref<boolean>(true); // 内外网标识
const svg = (name) => {
  return `/mapView/${name}.svg`; //拼接文件路径
};
const jobImgUrl = (name: string, suffix = 'svg') => {
  return getIconUrl(name, 'icons/remoteOverTwo/', suffix);
};

const validateDeviceName = (_rule: any, value: string): Promise<void> => {
  // 调用接口查用户输入的名称是否已存在，存在则提示用户该名称已存在
  return new Promise((resolve, reject) => {
    // 编辑，input为空的时候不进行验证
    if (!value || title.value == '编辑设备') {
      resolve();
    } else {
      deviceApi
        .cameraList({ deviceName: value, deviceType: '4' })
        .then((res: any) => {
          if (res.records.length > 0) {
            // res.records.forEach((item: any) => {
            //   if (item.deviceName === value) {
            //     reject('该设备名称已存在，请重新输入！');
            //   }
            // });
            // 寻找是否有跟value相同的名称，如果有则提示用户该名称已存在
            const idx = res.records.findIndex((item: any) => item.deviceName === value);
            if (idx !== -1) {
              reject('该设备名称已存在，请重新输入！');
            } else {
              resolve();
            }
          } else {
            resolve();
          }
        })
        .catch((error) => {
          reject(`验证设备名称时出错：${error.message}`);
        });
    }
  });
};
// const validatorLongitude = (_rule: any, value): Promise<void> => {
//   const reg = /^(\-|\+)?(((\d|[1-9]\d|1[0-7]\d|0{1,3})\.\d{0,6})|(\d|[1-9]\d|1[0-7]\d|0{1,3})|180\.0{0,15}|180)$/;
//   return new Promise((resolve, reject) => {
//     if (!value) {
//       reject(new Error('请输入经度'));
//     } else {
//       if (!reg.test(value)) {
//         reject(new Error('经度范围：-180~180（保留小数点后六位）'));
//       } else {

//         resolve();
//       }
//     }
//   });
// };
// const validatorLatitude = (_rule, value): Promise<void> => {
//   const reg = /^(\-|\+)?([0-8]?\d{1}\.\d{0,6}|90\.0{0,15}|[0-8]?\d{1}|90)$/;
//   return new Promise((resolve, reject) => {
//     if (!value) {
//       reject(new Error('请输入纬度'));
//     } else {
//       if (!reg.test(value)) {
//         reject(new Error('纬度范围：-90~90（保留小数点后六位）'));
//       } else {

//         resolve();
//       }
//     }
//   });
// };
// function exceedsSixDecimalPlaces(number) {
//   const parts = number.toString().split('.');
//   if (parts.length === 1) return false;
//   return parts[1].length > 6;
// }
// 表单验证规则
const rules = {
  deviceType: [{ required: true, message: '请选择设备类型', trigger: 'change' }],
  deviceName: [
    { required: true, message: '请输入设备名称', trigger: 'blur' },
    { pattern: new RegExp('^.{1,20}$'), message: '长度必须在1到20个字符之间', trigger: 'blur' },
    { validator: validateDeviceName, trigger: 'blur' },
  ],
  producerId: [{ required: true, message: '请选择品牌', trigger: 'change' }],
  deviceModel: [{ required: true, message: '请选择型号', trigger: 'change' }],
  sysOrgCode: [{ required: true, message: '请选择部门', trigger: 'change' }],
  deviceSn: [{ required: true, message: '请输入SN', trigger: 'blur' }],
  longitude: [
    // { required: true, message: '请输入经度', trigger: 'blur' },
    { validator: validatorLongitude, trigger: 'blur' },
  ],
  latitude: [
    // { required: true, message: '请输入纬度', trigger: 'blur' },
    { validator: validatorLatitude, trigger: 'blur' },
  ],
  location: [{ required: true, message: '安装位置不能为空', trigger: 'change' }],
};
// 布局
const labelCol = ref({
  style: { width: '100px' },
});
const wrapperCol = ref({
  span: 16,
});
const deviceTypeOptions = ref<SelectProps['options']>([
  {
    value: '4',
    label: '摄像头',
  },
]);
const deviceProducerOptions = ref<SelectProps['options']>([]);
// 型号下拉选择
const deviceModelOptions = ref<SelectProps['options']>([]);

const handleBrandChange = (value: string, option: any) => {
  // 品牌清空时，清空型号
  if (!value) {
    formState.value.deviceModel = undefined;
  } else {
    formState.value.deviceModel = undefined;
    deviceModelOptions.value = [];
    const index = deviceProducerList.value.findIndex((item) => item.id === value);
    if (index !== -1) {
      deviceProducerList.value[index].categoryList.forEach((item: any) => {
        deviceModelOptions.value?.push({ label: item.name, value: item.name });
      });
    }
    formState.value.deviceProducer = option.label;
  }
};
// 封装打点方法
const markerFun = (longitude, latitude, tag) => {
  if (currentMarker) {
    map.graphicLayer.removeGraphic(currentMarker); // 移除之前的标点
  }
  // 添加标点
  currentMarker = new mars3d.graphic.BillboardEntity({
    position: [longitude, latitude, 0], // 位置使用经纬度
    flyTo: tag, // 飞向该点
    style: {
      image: jobImgUrl('点', 'png'),
      clampToGround: true,
      horizontalOrigin: mars3d.Cesium.HorizontalOrigin.CENTER,
      verticalOrigin: mars3d.Cesium.VerticalOrigin.BOTTOM,
      pixelOffsetX: 8,
    },
  });

  map.graphicLayer.addGraphic(currentMarker);
  // 清除经纬度的校验
  if (formRef.value) {
    formRef.value.clearValidate(['longitude', 'latitude', 'location']);
  }
};
// 表单打点验证
const validateMarker = () => {
  console.log(formState.value.longitude, formState.value.latitude);

  if (
    formState.value.longitude &&
    formState.value.latitude &&
    !exceedsSixDecimalPlaces(formState.value.longitude) &&
    !exceedsSixDecimalPlaces(formState.value.latitude)
  ) {
    markerFun(formState.value.longitude, formState.value.latitude, true);
    formState.value.location = formState.value.longitude + '，' + formState.value.latitude;
  } else {
    if (currentMarker) {
      map.graphicLayer.removeGraphic(currentMarker); // 移除之前的标点
    }
  }
};
const currentSelect = (val) => {
  formState.value.location = val.address + val.name;
  let longitude,
    latitude = val.lonlat.split(',');
  formState.value.longitude = longitude;
  formState.value.latitude = latitude;
  markerFun(longitude, latitude, true);
  options.value = [];
};
let timer: any = null;
// 联网搜素
const handleSearch = (query: string) => {
  options.value = [];
  if (timer !== null) {
    clearTimeout(timer);
  }

  timer = setTimeout(() => {
    console.log('11111111', query);

    // 构造符合天地图官方接口的参数
    const postStr = {
      yingjiType: 1,
      sourceType: 0,
      keyWord: query,
      level: 3,
      mapBound: '61.179687499998835,-13.397389032035306,149.07031249999858,58.5010020353065',
      queryType: '4',
      start: 0,
      count: 10,
      queryTerminal: 10000,
    };

    // 天地图 POI 查询 URL
    const url = 'https://api.tianditu.gov.cn/v2/search';
    const tk = 'dc2ede760d49216f0ab31b140f032ed8';

    const params = new URLSearchParams({
      postStr: JSON.stringify(postStr),
      type: 'query',
      tk: tk,
    });

    // 发送请求
    fetch(`${url}?${params}`)
      .then((response) => response.json())
      .then((data) => {
        console.log('天地图POI查询结果:', data);
        options.value = data?.suggests || [];
      })
      .catch((error) => {
        console.error('查询失败:', error);
      });
  }, 500);
};
// 点击地图
const handleMapClick = (event: any) => {
  console.log('我点击地图了！', event);
  // map?.clear(true); // 清空地图上的所有标注
  // 获取点击的笛卡尔坐标
  const cartesian = event.cartesian;
  if (!cartesian) {
    console.log('无效的点击位置');
    return;
  }

  // 将笛卡尔坐标转换为经纬度
  const cartographic = Cesium.Cartographic.fromCartesian(cartesian);
  const longitude = Number(Cesium.Math.toDegrees(cartographic.longitude).toFixed(6));
  const latitude = Number(Cesium.Math.toDegrees(cartographic.latitude).toFixed(6));

  // 输出经纬度
  console.log('点击的经度:', longitude);
  console.log('点击的纬度:', latitude);
  markerFun(longitude, latitude, false);
  formState.value.longitude = longitude;
  formState.value.latitude = latitude;
  // 判断是否是内网部署
  // 不是内网
  console.log('是否内网部署', isInnerNet.value);

  if (!isInnerNet.value) {
    QueryPOI.getAddress({
      location: [longitude, latitude],
      success: function (data) {
        console.log('逆解析', data);
        formState.value.location = data.address;
      },
    });
  } else {
    formState.value.location = `${longitude}，${latitude}`;
  }
};
// 初始化地图
const toggleMap = async () => {
  options.value = [];
  if (showMap.value) {
    return;
  }

  try {
    if (map) {
      map.destroy();
      map = null;
    }

    showMap.value = true;
    await nextTick(); // 确保DOM更新完成

    const container = document.getElementById('MapContainerDevice');
    if (!container) {
      throw new Error('Map MapContainerDevice not found');
    }
    const mars3dConfig = mars3d.Util.merge(defaultMapConfig.map3d, toRaw(configMap));
    map = new mars3d.Map('MapContainerDevice', mars3dConfig);

    mapbacklayer = new mars3d.layer.GraphicLayer({
      allowDrillPick: true, // 如果存在坐标完全相同的图标点，可以打开该属性，click事件通过graphics判断
    });
    map.addLayer(mapbacklayer);
    map.on(mars3d.EventType.click, handleMapClick);
    // 判断是否是内网部署
    if (!isInnerNet.value) {
      QueryPOI = new mars3d.query.QueryPOI({ service: 'tdt', key: 'dc2ede760d49216f0ab31b140f032ed8' });
    }
    if (formState.value.longitude && formState.value.latitude && formState.value.location) {
      markerFun(formState.value.longitude, formState.value.latitude, true);
      map.setCameraView({ lng: formState.value.longitude, lat: formState.value.latitude, alt: 4000 }, { duration: 0.1 });
    } else {
      let latitudeLongitude = localStorage.getItem('latitudeLongitude'),
        center = latitudeLongitude ? JSON.parse(latitudeLongitude) : [116.478935, 39.997761];
      map.setCameraView({ lng: center[0], lat: center[1], alt: 4000 }, { duration: 0.1 });
    }
    // if (JSON.stringify(props.deviceDialogData) === '{}') {
    //   let latitudeLongitude = localStorage.getItem('latitudeLongitude'),
    //     center = latitudeLongitude ? JSON.parse(latitudeLongitude) : [116.478935, 39.997761];
    //   map.setCameraView({ lng: center[0], lat: center[1], alt: 4000 }, { duration: 0.1 });
    // } else {
    //   markerFun(formState.value.longitude, formState.value.latitude, true);
    //   map.setCameraView({ lng: formState.value.longitude, lat: formState.value.latitude, alt: 4000 }, { duration: 0.1 });
    // }
  } catch (error) {
    console.error('Map initialization failed:', error);
    showMap.value = false;
    message.error('地图初始化失败');
  }
};
// 确定按钮
const handleOk = () => {
  formRef.value
    .validate()
    .then(() => {
      // delete formState.value?.installationSite;
      console.log('values', formState.value);
      if (title.value === '新增设备') {
        deviceApi.addcamera(formState.value).then(() => {
          message.success('新增成功！');
          emit('handleclose');
          emit('handleSearch');
          showMap.value = false;
        });
      } else {
        deviceApi.editcamera(formState.value).then(() => {
          message.success('编辑成功！');
          emit('handleclose');
          emit('handleSearch');
          showMap.value = false;
        });
      }
    })
    .catch((error: ValidateErrorEntity<any>) => {
      console.log('error', error);
    });
};
// 关闭弹窗，
const cancel = () => {
  // showMap.value = false;
  emit('handleclose');
  //清空地图搜索提示信息，重置表单字段及校验，清除地图点标记
  options.value = [];
  formRef.value.clearValidate();
  if (map) {
    map?.destroy();
    map = null;
    showMap.value = false;
  }
};
let deviceProducerList = ref<any[]>([]);
onMounted(async () => {
  isInnerNet.value = localStorage.getItem('environmentTag') === 'privateCloud' ? true : false;
  // 获取摄像头信息
  console.log('当前环境：', localStorage.getItem('environmentTag'));
  const res = await deviceApi.getBrandList({});
  deviceProducerList.value = res?.brandList;
  // console.log('摄像头信息', res.brandList);
  res?.brandList.forEach((item: any) => {
    deviceProducerOptions.value?.push({ label: item.name, value: item.id });
  });
});

onUnmounted(() => {
  console.log('销毁了');
  // 目前设置有缓存组件不会销毁
  map?.destroy();
  map = null;
});
defineExpose({
  deviceProducerOptions,
  deviceProducerList,
});
let title = ref('');
watch(
  () => props.deviceDialogData,
  () => {
    formState.value = props.deviceDialogData;
    if (JSON.stringify(props.deviceDialogData) === '{}') {
      title.value = '新增设备';
    } else {
      title.value = '编辑设备';
    }
    console.log('弹窗数据', formState.value);
  },
  { deep: true }
);
</script>
<style lang="less" scoped>
.ant-modal-body {
  padding: 20px;
}
#container {
  width: 100%;
  height: 300px;
}
.ant-divider-horizontal {
  margin-top: 0;
  position: relative;
  left: -17px;
  width: 105%;
}
.ant-input[disabled] {
  color: rgba(0, 0, 0, 0.65);
}
</style>
