import { defHttp } from '/@/utils/http/axios';

enum Api {
  delete = '/uav/wayLine/v2/delete', // 删除航线
  create = '/uav/wayLine/v2/create', // 创建航线
  edit = '/uav/wayLine/v2/edit', // 编辑航线
  put = '/uav/wayLine/v2/edit', // 编辑航线
  webList = '/uav/wayLine/v2/webList', // 航线列表
  webQueryById = '/uav/wayLine/v2/webQueryById', // 航线列表
  duplicateNames = '/uav/wayLine/v2/duplicateNames', // 校验航线名称
  upload = '/uav/wayLine/v2/upload', // 校验航线名称
  rename = '/uav/wayLine/v2/rename', // 校验航线名称
}

export const waylineApiV2 = {
  delete: (params) => {
    const { id, sysOrgCode } = params;
    const url = `${Api.delete}?id=${id}&sysOrgCode=${sysOrgCode}`; // 拼接路径
    return defHttp.delete({ url });
  },
  upload: (params, orgCode) => defHttp.post({ url: Api.upload, params, headers: { 'Content-Type': 'multipart/form-data', 'X-Sys-Org-Code': `${orgCode}` }, timeout: 10 * 60 * 1000 }),
  create: (params) => defHttp.post({ url: Api.create, params }),
  edit: (params) => defHttp.post({ url: Api.edit, params }),
  rename: (params) => defHttp.post({ url: Api.rename, params }),
  put: (params) => defHttp.put({ url: Api.put, params }),
  webList: (params) => defHttp.get({ url: Api.webList, params }),
  webQueryById: (params) => defHttp.get({ url: Api.webQueryById, params }),
  duplicateNames: (params) => defHttp.get({ url: Api.duplicateNames, params }),
};
