<template>
  <div class="tackBox">
    <div class="header">
      <a-input class="tackOffInput" v-model:value="search" @change="onInputChange">
        <template #prefix>
          <search-outlined />
        </template>
      </a-input>
    </div>
    <div class="tackListBox">
      <div
        class="list"
        :class="{ listSelect: item.id === selectedRouteData?.id }"
        v-for="item in wayLineList"
        :key="item.id"
        @click="selectedRoute(item)"
      >
        <div class="listTopImg">
          <div class="position">
            <img :src="jobImgUrl('航线类型')" />
          </div>
          <div class="aircraft"
            ><img :src="jobImgUrl('机型')" /> <span :title="dronelist[item.droneModel]">{{ dronelist[item.droneModel] || '未知机型' }}</span>
          </div>
          <div class="Lens"
            ><img :src="jobImgUrl('相机')" />
            <span :title="(payloadlist[item.droneModel] || []).filter((obj) => obj.code == item.payloadModel)[0]?.model">{{
              (payloadlist[item.droneModel] || []).filter((obj) => obj.code == item.payloadModel)[0]?.model || '未知负载'
            }}</span>
          </div>
        </div>
        <div class="listBottomText">{{ item.name }}</div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { ref, onMounted, nextTick, reactive, defineProps, defineEmits } from 'vue';
  import { getIconUrl } from '/@/utils';
  import { SearchOutlined } from '@ant-design/icons-vue';
  import { taskApi } from './data.api';
  import { waylineApi } from '/@/views/wayline/data.api';
  import { message } from 'ant-design-vue';
  import { debounce } from 'lodash-es';
  import { waylineApiV2 } from '../../wayline/data.api3';

  const emit = defineEmits(['plotCourse', 'closePopup']);

  let search = ref('');
  let selectedRouteData = ref();
  let wayLineId = ref(null);

  const props = defineProps({
    TaskConf: {
      type: Object,
      default: () => ({}),
    },
    selectDevice: {
      type: Object,
      default: () => ({}),
    },
    sysOrgCodeList: {
      type: String,
    },
  });

  // 获取图片路径
  const jobImgUrl = (name: string, suffix = 'svg') => {
    return getIconUrl(name, 'icons/FastTask/', suffix);
  };

  const payloadlist = {
    '89': [
      { code: 42, model: 'H20' },
      { code: 43, model: 'H20T' },
      { code: 61, model: 'H20N' },
      { code: 65534, model: 'PSDK 负载' },
    ],
    '60': [
      { code: 42, model: 'H20' },
      { code: 43, model: 'H20T' },
      { code: 61, model: 'H20N' },
      { code: 65534, model: 'PSDK 负载' },
    ],
    '67': [
      { code: 52, model: 'M30双光相机' },
      { code: 53, model: 'M30T三光相机' },
      { code: 65534, model: 'PSDK 负载' },
    ],
    '77': [
      { code: 66, model: 'Mavic 3E 相机' },
      { code: 67, model: 'Mavic 3T 相机' },
      { code: 68, model: 'Mavic 3M 相机' },
      { code: 65534, model: 'PSDK 负载' },
    ],
    '91': [
      { code: 80, model: 'Matrice 3D 相机' },
      { code: 81, model: 'Matrice 3TD 相机' },
    ],
    '100': [
      { code: 98, model: 'Matrice 4D 相机' },
      { code: 99, model: 'Matrice 4TD 相机' },
    ],
  };
  const dronelist = { '89': 'M350 RTK', '60': 'M300 RTK', '67': 'M30/M30T', '77': 'M3E/M3T/M3M', '91': 'M3D/M3TD', '100': 'M4D/M4TD' };

  let wayLineList = ref([]);
  let params = reactive({
    name: '',
    column: 'createTime',
    order: 'desc',
    pageNo: 1,
    pageSize: 200,
  });

  const getWaylineQuery = async () => {
    const res = await waylineApiV2.webList({ ...params, sysMultiOrgCode: props?.selectDevice?.option?.sysOrgCode, deptQueryType: 'MULTIPLE' });
    console.log(res, '航线');
    wayLineList.value = res.records;
  };

  const onInputChange = () => {
    params.name = search.value;
    debouncedSearchQuery();
  };
  const debouncedSearchQuery = debounce(() => {
    getWaylineQuery();
  }, 800);
  const selectedRoute = async (item: { id: string }) => {
    selectedRouteData.value = item;
    wayLineId.value = item.id;
    console.log(item, '点击');

    try {
      const waylineRes = await taskApi.waylineGet({
        id: item.id,
        workspaceId: localStorage.getItem('Project_Id'),
      });

      const positions = await new Promise((resolve, reject) => {
        waylineApi.resolveFile(waylineRes.fileUrl, (waylines_wpml) => {
          const points = waylines_wpml.Folder.Placemark;
          const coords = points.map((point) => [Number(point.lng), Number(point.lat), 0.0]);
          resolve(coords);
        });
      });
      emit('plotCourse', positions);
      console.log(positions, '获取航线坐标');
    } catch (error) {
      console.error('获取航线或解析文件时出错:', error);
    }
  };

  const routeDeletion = () => {
    selectedRouteData.value = null;
  };

  const carryOut = async () => {
    if (!wayLineId.value) {
      message.warning('请选择航线');
      return;
    }
    message.warning('任务下发中！');
    const params = {
      // ...props.TaskConf.takeoffConfig,
      ...JSON.parse(JSON.stringify(props.TaskConf.takeoffConfig)),
      ...JSON.parse(JSON.stringify(props.TaskConf.taskConfig)),
      // ...props.TaskConf.taskConfig,
      wayLineId: wayLineId.value,
      deviceId: props.selectDevice.option.deviceId,
      souceType: 1,
      jobType: 1,
      sysOrgCode: props?.selectDevice?.option?.sysOrgCode
    };
    console.log(params, '执行任务');

    const res = await taskApi.deviceQuery({
      deviceType: 3,
      deviceSn: props.selectDevice.option.deviceSn,
      pageNo: 1,
      pageSize: 200,
      sysMultiOrgCode: props.sysOrgCodeList,
      deptQueryType: 'MULTIPLE',
    });
    // dockState.value = res.records[0].state == 4 ? true : false; //机场是否作业中  4 为机场作业中
    if (res.records[0].state != 4 && res.records[0].state != 5) {
      taskApi.waylinejobSave(params).then((res) => {
        if (res.data.code == 200) {
          message.success('任务下发成功');
        } else {
          message.error(`任务下发失败:${res.data.message}`);
        }
      });
    } else {
      message.warning('该设备正在执行任务，请稍后再试！');
    }

    // emit('closePopup');
  };

  defineExpose({
    carryOut,
    routeDeletion,
    getWaylineQuery,
  });

  onMounted(async () => {
    await nextTick();
    getWaylineQuery();
  });
</script>
<style lang="less" scoped>
  .tackBox {
    width: 301px;
    height: 609px;
    background: rgba(0, 0, 0, 0.6);
    border-radius: 4px;
    backdrop-filter: blur(5px);

    .header {
      padding: 15px 13px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      :deep(.ant-input) {
        background: rgba(35, 37, 38, 0.03);
        color: #ffffff;
      }
      .tackOffInput {
        background: rgba(35, 37, 38, 0.03);
        border-radius: 2px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: #888b8b;
        :deep(.ant-input-suffix) {
          color: #ffffff;
        }
      }
    }
    .tackListBox {
      padding: 12px;
      background-color: #212121;
      height: 520px;
      overflow: scroll;
      .list {
        background: rgba(255, 255, 255, 0.05);
        border-radius: 2px;
        padding: 13px 12px 10px 14px;
        border: 1px solid transparent;
        margin-bottom: 12px;
        // overflow: scroll;
        cursor: pointer;
        .listTopImg {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 11px;
          .position {
            width: 26px;
            height: 26px;
            background: rgba(104, 213, 189, 0.2);
            border-radius: 2px;
            border: 1px solid rgba(104, 213, 189, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            img {
            }
          }
          .aircraft {
            width: 91px;
            height: 26px;
            background: rgba(255, 202, 101, 0.2);
            border-radius: 2px;
            border: 1px solid rgba(255, 202, 101, 0.5);
            display: flex;
            align-items: center;
            font-weight: 400;
            font-size: 12px;
            color: #ffffff;
            padding: 0 8px;
            gap: 2px;
            img {
            }
            span {
              width: 100%;
              white-space: nowrap; /* 强制文本在一行内显示 */
              overflow: hidden; /* 隐藏超出容器的内容 */
              text-overflow: ellipsis; /* 使用省略号表示被截断的文本 */
            }
          }
          .Lens {
            width: 91px;
            height: 26px;
            background: rgba(89, 165, 255, 0.2);
            border-radius: 2px;
            border: 1px solid rgba(89, 165, 255, 0.5);
            font-weight: 400;
            font-size: 12px;
            color: #ffffff;
            padding: 0 8px;
            gap: 2px;
            display: flex;
            align-items: center;
            img {
            }
            span {
              width: 100%;
              white-space: nowrap; /* 强制文本在一行内显示 */
              overflow: hidden; /* 隐藏超出容器的内容 */
              text-overflow: ellipsis; /* 使用省略号表示被截断的文本 */
            }
          }
        }
        .listBottomText {
          font-weight: 500;
          font-size: 12px;
          color: #ffffff;
          width: 100%;
          white-space: nowrap; /* 禁止文本换行 */
          overflow: hidden; /* 隐藏超出范围的文本 */
          text-overflow: ellipsis; /* 超出部分用省略号表示 */
        }
      }
      .listSelect {
        border: 1px solid #5889ff;
        background: rgba(88, 137, 255, 0.2);
      }
    }
    .tackList {
    }
  }
</style>
