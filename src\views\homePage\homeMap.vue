<template>
  <div class="homeMapBox">
    <mars-map :options="mars3dConfig" mapKey="homeMap" @onload="marsOnload"></mars-map>
    <slot></slot>
  </div>
</template>
<script setup lang="ts">
import { ref, reactive, onMounted, watch, defineEmits } from 'vue';
import MarsMap from '@/views/components/mars-work/mars-map.vue';
import * as mars3d from 'mars3d';
import * as Cesium from 'mars3d-cesium';

let latitudeLongitude = localStorage.getItem('latitudeLongitude'),
  center = latitudeLongitude ? JSON.parse(latitudeLongitude) : [116.478935, 39.997761];
const mars3dConfig = {
  scene: {
    center: { lng: center[0], lat: center[1], alt: 3000 },
  },
  control: {
    homeButton: false,
    baseLayerPicker: false,
    sceneModePicker: false,
    navigationHelpButton: false,
    locationBar: {
      template: '',
    },
    zoom: {
      enabled: false,
    },
    compass: {
      enabled: false,
    },
    contextmenu: { hasDefault: false },
  },
};

const props = defineProps({
  options: {
    type: Array,
    default: () => [],
  },
});

let lnt = ref();
let graphicLayer = null;
let Map = null;
const emit = defineEmits();
watch(
  () => props.options,
  (newVal) => {
    console.log(newVal,'传过来的经纬度')
    info()
  },
  { deep: true }
);
const png = (name) => {
  return `/mapView/${name}.png`; //拼接文件路径
};
const info = () => {
  if(graphicLayer){
    graphicLayer.clear();
  }
  // 安全检查
  if (!props.options || !Array.isArray(props.options) || props.options.length === 0) return;
  if (!graphicLayer) return;



  // 飞行器的
  const filterOptions = props.options.filter((item) => {
    if (item.deviceList && item.deviceList.length > 0) {
      return (
        (item.deviceList[0].deviceType == 2 && item.deviceList[0].state != 0) ||
        (item.deviceList[0].deviceType == 4 && item.deviceList[0].state != 0) ||
        (item.deviceList[0].deviceType == 3 && item.deviceList[0].state != 99) ||
        (item.deviceList[0].deviceType == 0 && item.deviceList[0].state != 18)
      );
    }
  });
  // 机场的
  const filterOptions1 = props.options.filter((item) => {
    return (
      (item.deviceType == 3 && item.state != 99) ||
      (item.deviceType == 2 && item.state != 0) ||
      (item.deviceType == 4 && item.state != 0) ||
      (item.deviceType == 0 && item.state != 18)
    );
  });
  filterOptions1.forEach((item) => {
    const graphic = new mars3d.graphic.BillboardEntity({
      // position: [114.1237466550476, 22.69519062104972],
      position: [item.longitude, item.latitude],
      style: {
        image: item.deviceType != '2' ? png('deviceModel' + item.deviceModel) : png('地图设备' + item.deviceType),
        horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
        verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
        width: 60,
        height: 60,
        clampToGround: true,
      },
      attr: { data: item },
    });
    graphicLayer.addGraphic(graphic);
  });
  // console.log('111111111111',filterOptions);

  filterOptions.forEach((item) => {
    const graphic = new mars3d.graphic.BillboardEntity({
      // position: [114.1237466550476, 22.69519062104972],
      position: [item.deviceList[0].longitude, item.deviceList[0].latitude],
      style: {
        image: item.deviceModel == 'DJI Dock' ? png('m30') : item.deviceModel == 'DJI Dock2' ? png('M3D') : png('地图设备0'),
        width: 60,
        height: 60,
        // image: 'http://mars3d.cn/img/marker/point-orange.png',
        horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
        verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
        clampToGround: true,
      },
      attr: { data: item },
    });
    graphicLayer.addGraphic(graphic);
  });

  graphicLayer.on(mars3d.EventType.click, function (event) {
    // console.log("监听layer，单击了矢量对象", event)
    // console.log(event.propagatedFrom.options.attr.data)
    emit('info', event.propagatedFrom.options.attr.data);
  });

  // 添加安全检查，确保props.options存在且不为空数组
  if (Map && props.options && props.options.length > 0 && props.options[0].deviceList && props.options[0].deviceList.length > 0) {
    Map.setCameraView(
      {
        lng: props.options[0].deviceList[0].longitude,
        lat: props.options[0].deviceList[0].latitude,
        alt: 3000,
      },
      {
        duration: 0.1,
      }
    );
  }
};

info();

const marsOnload = (map: any) => {
  Map = map;
  graphicLayer = new mars3d.layer.GraphicLayer();
  map.addLayer(graphicLayer);
  emit('marsOnload', map);

  // 初始化数据
  info();
};
</script>
<style lang="less" scoped>
.homeMapBox {
  width: 100%;
  // height: 107%;
  height: 100%;
  position: absolute;
}
/deep/ .mars3d-locationbar {
  display: none;
}
/deep/ .mars3d-distance-legend {
  bottom: 2% !important;
  left: auto !important;
  right: 28.5rem;
}
</style>
