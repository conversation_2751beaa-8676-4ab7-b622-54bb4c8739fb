import { defHttp } from '/@/utils/http/axios';

enum Api {
  queryLocationFiles = '/uav/file/query-location-files',
  queryLocationFileCount = '/uav/file/query-location-file-count',
}

export const imageComparison = {
  queryLocationFiles: (params) => defHttp.post({ url: Api.queryLocationFiles, params }),
  queryLocationFileCount: (params) => defHttp.post({ url: Api.queryLocationFileCount, params }),
};
