<template>
  <div class="p-4">
    <template v-if="tabIndex === 1">
      <div class="job-container-wrap">
        <div class="table-wrap">
          <div class="title-wrap">
            <div class="title">计划管理</div>
            <!-- <a-select
              :dropdown-match-select-width="false"
              class="my-year-select"
              :value="String(dateValue.year())"
              @change="
                (newYear) => {
                  outCalendarChange('year', +newYear);
                  //onChange(dateValue.year(+newYear));
                }
              "
            >
              <a-select-option v-for="val in getYears(dateValue)" :key="String(val)" class="year-item">
                {{ val }}
              </a-select-option>
            </a-select>
            <a-select
              style="margin-left: 8px"
              :dropdown-match-select-width="false"
              :value="String(dateValue.month())"
              @change="
                (selectedMonth) => {
                  outCalendarChange('month', parseInt(String(selectedMonth), 10));
                  //onChange(dateValue.month(parseInt(String(selectedMonth), 10)));
                }
              "
            >
              <a-select-option v-for="(val, index) in getMonths(dateValue)" :key="String(index)" class="month-item">
                {{ val }}
              </a-select-option>
            </a-select>
            <a-select
              style="margin-left: 8px"
              :open="false"
              @blur="selectBlur"
              :value="dateValue.date() ? String(dateValue.date()) + '日' : ''"
              @dropdownVisibleChange="dropdownVisibleChange"
            >
            </a-select> -->
          </div>
          <!-- <div class="calendar" :class="{ visible: calendarVisible }">
            <a-calendar v-model:value="dateValue" @panelChange="onPanelListChange" @change="calendarChange">
              <template #headerRender="{ value: current, onChange }">
                <div class="calendar-top"> </div>
              </template>
            </a-calendar>
          </div> -->
          <BasicTable @register="registerTable" :searchInfo="queryListParam">
            <template #bodyCell="{ record, column }">
              <!--  -->
              <template v-if="column.dataIndex === 'result'">
                <div class="resultWrap" @click="openDataManage(record)">
                  <template v-if="[null, 0].includes(record.mediaUploadStatus)">
                    <div class="resultNum"> -- </div>
                  </template>
                  <template v-else>
                    <div class="resultStatus"> <a-badge :status="resultIcon('icon1', record)" />{{ resultIcon('resultStatus', record) }} </div>
                    <div class="resultNum">{{ `(${record.mediaSuccessCount || '0'}/${record.mediaCount || '0'})` }}</div>
                  </template>
                </div>
              </template>
              <template v-if="column.dataIndex === 'status'">
                <div class="statusWrap">
                  <template v-if="resultIcon('isShowIcon', record)"><a-badge :status="resultIcon('icon', record)" /></template>
                  <div class="text">{{ resultIcon('statusTxt', record) }}</div>
                  <template v-if="record.status === TaskStatus.Failed">
                    <div class="tips">
                      <Tooltip :title="record.result">
                        <span>
                          <InfoCircleOutlined />
                        </span>
                      </Tooltip>
                    </div>
                  </template>
                </div>
              </template>
              <template v-if="column.dataIndex === 'waylineName'">
                <div class="waylineNameWrap">
                  <Tooltip :title="record.waylineName">
                    <div class="waylineNameItem" @click="openWayline(record)"> {{ record.waylineName }}</div>
                  </Tooltip>
                </div>
              </template>
              <template v-if="column.dataIndex === 'jobType'">
                <div class="jobTypeWrap">
                  <div
                    :style="{ backgroundColor: jobTypeBgColor(record.jobType) }"
                    class="jobTypeItem"
                    :class="{ icon: getJobTypeTxt(record.jobType) !== '-' }"
                  >
                    {{ getJobTypeTxt(record.jobType) }}</div
                  >
                </div>
              </template>
            </template>
            <template #toolbar>
              <div class="toolbarIcon">
                <div class="icon-calendar" @click="() => clickTab(2)">
                  <Icon icon="lucide:calendar"></Icon>
                </div>
                <div class="icon-list">
                  <Icon icon="gg:list"></Icon>
                </div>
              </div>
              <div class="toolbar">
                <a-button v-if="myDepartListOrgCode.includes(sysOrgCode)" class="addTask" preIcon="ant-design:plus-outlined" @click="addTask">
                  创建计划
                </a-button>
                <a-tree-select
                  v-model:value="sysOrgCode"
                  show-search
                  tree-default-expand-all
                  style="width: 143px; margin-left: 8px"
                  :dropdown-style="{ width: 'auto' }"
                  :tree-data="departmentList"
                  :field-names="{ children: 'children', label: 'departName', value: 'orgCode' }"
                  :dropdownMatchSelectWidth="false"
                  placeholder="请选择部门"
                  @change="searchQuery()"
                />
                <a-select
                  style="width: 143px; margin-left: 8px"
                  v-model:value="queryListParam.status"
                  allowClear
                  :options="optionsList.status"
                  @change="(value) => selectChangeList('status', value)"
                />
                <a-select
                  style="width: 143px; margin-right: 8px; margin-left: 8px"
                  v-model:value="queryListParam.jobType"
                  allowClear
                  :options="optionsList.jobType"
                  @change="(value) => selectChangeList('jobType', value)"
                />

                <a-range-picker v-model:value="dateRange" @change="handleDateRange" style="margin-right: 8px" />
                <a-input-search
                  allowClear
                  v-model:value="queryListParam.jobName"
                  placeholder="请搜索计划名称"
                  style="width: 200px"
                  @search="enterSearchList"
                />
              </div>
            </template>
            <template #action="{ record, column }">
              <TableAction :actions="createActions(record, column)" :dropDownActions="getDropDownActions(record)" />
            </template>

            <template #createBy="{ record, text }">
              {{ memberQuery(text) || text }}
            </template>
          </BasicTable>
        </div>
      </div>
    </template>
    <template v-if="tabIndex === 2">
      <div class="job-wrap">
        <div class="job-calendar" id="job-calendar">
          <a-calendar v-model:value="dateValue" @panelChange="onPanelChange">
            <template #headerRender="{ value: current, onChange }">
              <div class="calendar-top">
                <div class="title-wrap">
                  <div class="title">计划管理</div>
                  <a-select
                    :dropdown-match-select-width="false"
                    class="my-year-select"
                    :value="String(current.year())"
                    @change="
                      (newYear) => {
                        onChange(current.year(+newYear));
                      }
                    "
                  >
                    <a-select-option v-for="val in getYears(current)" :key="String(val)" class="year-item">
                      {{ val }}
                    </a-select-option>
                  </a-select>
                  <a-select
                    class="my-year-select"
                    style="margin-left: 8px"
                    :dropdown-match-select-width="false"
                    :value="String(current.month())"
                    @change="
                      (selectedMonth) => {
                        onChange(current.month(parseInt(String(selectedMonth), 10)));
                      }
                    "
                  >
                    <a-select-option v-for="(val, index) in getMonths(current)" :key="String(index)" class="month-item">
                      {{ val }}
                    </a-select-option>
                  </a-select>
                </div>
                <div class="tabs-icon">
                  <div class="icon-calendar">
                    <Icon icon="lucide:calendar"></Icon>
                  </div>
                  <div class="icon-list" @click="() => clickTab(1)">
                    <Icon icon="gg:list"></Icon>
                  </div>
                </div>
                <div class="search-mod">
                  <a-button
                    v-if="myDepartListOrgCode.includes(sysOrgCode)"
                    class="addTask"
                    preIcon="ant-design:plus-outlined"
                    @click="() => openTaskModal(PageType.Add)"
                  >
                    创建计划
                  </a-button>
                  <a-tree-select
                    v-model:value="sysOrgCode"
                    show-search
                    tree-default-expand-all
                    style="width: 143px; margin-left: 8px"
                    :dropdown-style="{ width: 'auto' }"
                    :tree-data="departmentList"
                    :field-names="{ children: 'children', label: 'departName', value: 'orgCode' }"
                    :dropdownMatchSelectWidth="false"
                    placeholder="请选择部门"
                    @change="getData()"
                  />
                  <a-select
                    style="width: 143px; margin-left: 8px"
                    v-model:value="queryParam.status"
                    allowClear
                    :options="optionsList.status"
                    @change="(value) => selectChange('status', value)"
                  />
                  <a-select
                    style="width: 143px; margin-left: 8px; margin-right: 8px"
                    v-model:value="queryParam.jobType"
                    allowClear
                    :options="optionsList.jobType"
                    @change="(value) => selectChange('jobType', value)"
                  />
                  <a-input-search
                    allowClear
                    v-model:value="queryParam.jobName"
                    placeholder="请搜索计划名称"
                    style="width: 200px"
                    @search="enterSearch"
                  />
                </div>
              </div>
            </template>
            <template #dateCellRender="{ current }">
              <ul class="events">
                <li
                  v-for="item in getListData(current)"
                  @click="() => switchList(current)"
                  :key="item.content"
                  class="job-calendar-item"
                  :class="activeItem.id && item?.id && activeItem.id === item?.id && 'active'"
                >
                  <a-popover v-model:open="popoverVisible" :destroyTooltipOnHide="true" :getPopupContainer="getPopupContainer1">
                    <template #content>
                      <div class="popover-content">
                        <div class="title-wrap">
                          <div class="icon-wrap">
                            <img class="icon" :src="getTaskTypeIcon" />
                            <div class="desc">{{ getTaskTypeName }}</div>
                          </div>
                          <div class="title">
                            <span>
                              {{ activeItem.taskName }}
                            </span>
                            <span> 归属部门：{{ activeItem.sysOrgName }} </span>
                          </div>
                        </div>
                        <div class="middle">
                          <div class="item status">
                            <div class="icon-wrap">
                              <img class="title-icon" :src="jobImgUrl('执行状态-气泡')" />
                            </div>
                            <div class="item-content">
                              <div class="label">执行状态</div>
                              <div class="value"> {{ TaskStatusOps.find((item) => item.value === activeItem.status)?.label || '' }}</div>
                            </div>
                          </div>
                          <div class="item wayLine">
                            <div class="icon-wrap">
                              <img class="title-icon" :src="jobImgUrl('关联航线-气泡')" />
                            </div>
                            <div class="item-content">
                              <div class="label">关联航线</div>
                              <div class="value" @click="() => popoverHandleClick('viewWayLine')">
                                <div class="icon">
                                  <Icon class="icon" :size="14" icon="bx:map"></Icon>
                                </div>
                                <div class="txt">{{ activeItem.waylineName }}</div>
                              </div>
                            </div>
                          </div>
                          <div class="item device">
                            <div class="icon-wrap">
                              <img class="title-icon" :src="jobImgUrl('关联设备-气泡')" />
                            </div>
                            <div class="item-content">
                              <div class="label">关联设备</div>
                              <div class="value"> {{ activeItem.deviceName }}</div>
                            </div>
                          </div>
                          <div class="item medium">
                            <div class="icon-wrap">
                              <img class="title-icon" :src="jobImgUrl('媒体上传-气泡')" />
                            </div>
                            <div class="item-content">
                              <div class="label">媒体上传</div>
                              <Tooltip :title="activeItem.result" :getPopupContainer="getPopupContainer">
                                <div class="value"> {{ activeItem.result }}</div>
                              </Tooltip>
                            </div>
                          </div>
                        </div>
                        <div class="footer">
                          <div class="left"> 创建人：{{ memberQuery(activeItem.createBy) || activeItem.createBy }} </div>
                          <div class="right" id="modal-right">
                            <!-- <Icon class="icon" :class="!btnAuthTemplate('查看轨迹') && 'disabled'" :size="18" icon="carbon:map" @click="() => popoverHandleClick('viewWayLine')"></Icon>
                            <Icon class="icon" :class="!btnAuthTemplate('复制') && 'disabled'" :size="18" icon="radix-icons:copy" @click="() => popoverHandleClick('copy')"></Icon>
                            <Icon class="icon" :class="!btnAuthTemplate('删除') && 'disabled'" :size="18" icon="uiw:delete" @click="() => popoverHandleClick('del')"></Icon> -->
                          </div>
                        </div>
                      </div>
                    </template>
                    <a-badge class="badge" :status="item.type" :text="item.content" @mouseover="hoverDetail(item, current)" />
                  </a-popover>
                </li>
              </ul>
            </template>
          </a-calendar>
        </div>
      </div>
    </template>
    <AddJob :pageType="pageType" :jobData="jobData" :sysOrgCode="sysOrgCode" @success="success" @register="registerAddJob" />
    <TrackPlaybackModal
      :isCollapsed="false"
      :open="trackPlaybackOpen"
      :trackPlaybackDeviceSn="trackPlaybackDeviceSn"
      :trackTaskId="trackTaskId"
      :sysOrgCode="sysOrgCodeRef"
      @close="
        () => {
          trackPlaybackOpen = false;
        }
      "
    />
  </div>
</template>
<script lang="ts" name="uav-job" setup>
import AddJob from './addJob.vue';
import TrackPlaybackModal from '/@/views/components/flightPathReplay/trackPlaybackModal.vue';
import { inject, onMounted, reactive, watch, ref, computed, nextTick, toRaw, toRef } from 'vue';
import { BasicTable, useTable, TableAction, EditRecordRow, BasicColumn, ActionItem } from '/@/components/Table';
import { useMessage } from '/@/hooks/web/useMessage';
import ApiListOptions from '/@/api/type';
import { useRouter } from 'vue-router';
import { render } from '/@/utils/common/renderUtils';
import { TaskStatusOps, TaskStatus, TaskType, TaskTypeOps, PageType, mediaUploadStatus } from '/@/constants/job';
import Icon from '/@/components/Icon/index';
import { useModal } from '/@/components/Modal';
import { Tooltip } from 'ant-design-vue';
import { InfoCircleOutlined } from '@ant-design/icons-vue';
import { DataDirNavigation } from '/@/constants/index';
import { throttle } from 'lodash-es';
import { getIconUrl } from '/@/utils';
import dayjs, { Dayjs } from 'dayjs';
import { getUserList } from '/@/api/common/api';

const router = useRouter();
let user = ref();

const optionsList = ref<any>({
  status: TaskStatusOps,
  jobType: TaskTypeOps,
});
let sysOrgCode = ref<string>('');
let myDepartListOrgCode = ref([]);
let queryListParam = reactive<any>({
  year: '',
  month: '',
  day: '',
  jobType: '',
  status: '',
  jobName: '',
  sysOrgCode: toRef(sysOrgCode), //当前选择的部门
});
const resultStatus: any[] = [
  {
    value: mediaUploadStatus.Success,
    label: '上传成功',
  },
  {
    value: mediaUploadStatus.PartialSuccess,
    label: '部分完成',
  },
  {
    value: mediaUploadStatus.Progress,
    label: '上传中',
  },
  {
    value: mediaUploadStatus.Failed,
    label: '上传失败',
  },
];
const iconList: any[] = [
  {
    value: TaskStatus.Success,
    icon: 'success',
  },
  {
    value: TaskStatus.PartialSuccess,
    icon: 'success',
  },
  {
    value: TaskStatus.Progress,
    icon: 'processing',
  },
  {
    value: TaskStatus.Failed,
    icon: 'error',
  },
];
const iconList1: any[] = [
  {
    value: mediaUploadStatus.Success,
    icon: 'success',
  },
  {
    value: mediaUploadStatus.PartialSuccess,
    icon: 'success',
  },
  {
    value: mediaUploadStatus.Progress,
    icon: 'processing',
  },
  {
    value: mediaUploadStatus.Failed,
    icon: 'error',
  },
];
const columns: BasicColumn[] = [
  {
    title: '计划时间',
    dataIndex: 'planTime',
    key: 'planTime',
    align: 'center',
    width: 150,
  },
  {
    title: '计划名称',
    dataIndex: 'taskName',
    key: 'taskName',
    align: 'center',
    // width: 200,
    customRender: ({ text }) => {
      return render.renderTip(text);
    },
  },
  {
    title: '执行状态',
    dataIndex: 'status',
    key: 'status',
    align: 'center',
    width: 200,
  },
  {
    title: '计划类型',
    dataIndex: 'jobType',
    key: 'jobType',
    align: 'center',
    width: 150,
  },
  {
    title: '关联航线',
    dataIndex: 'waylineName',
    key: 'waylineName',
    align: 'center',
  },
  {
    title: '关联设备',
    dataIndex: 'deviceName',
    key: 'deviceName',
    align: 'center',
  },
  {
    title: '媒体上传',
    dataIndex: 'result',
    key: 'result',
    width: 180,
    align: 'center',
  },
  {
    title: '创建人',
    dataIndex: 'createBy',
    key: 'createBy',
    width: '150px',
    align: 'center',
    slots: { customRender: 'createBy' },
  },
];
const $http: ApiListOptions | undefined = inject('api');
const { createMessage, createConfirm } = useMessage();
const tabIndex = ref<number>(2); // 1: 列表模式  2: 日历模式
let trackPlaybackOpen = ref<boolean>(false); //轨迹回放弹窗
let trackPlaybackDeviceSn = ref(); // 轨迹回放设备SN
let trackTaskId = ref(); // 轨迹回放任务IDweewqeqw
let sysOrgCodeRef = ref();
const dateValue = ref<Dayjs>(dayjs());
const dateRange = ref([dayjs().startOf('day'), dayjs().endOf('day')]);
const calendarVisible = ref<boolean>(false);
//注册table数据
const [registerTable, { reload, setProps, setTableData }] = useTable({
  title: '',
  api: $http?.getTaskQureyTaskListByRange,
  rowKey: 'id',
  columns: columns,
  // searchInfo: {
  //   ...queryListParam
  // },
  //pagination: false,
  clickToRowSelect: false,
  striped: true,
  useSearchForm: false,
  showTableSetting: false,
  bordered: true,
  showIndexColumn: false,
  canResize: false,
  showActionColumn: true,
  actionColumn: {
    width: 260,
    title: '操作',
    fixed: 'right',
    dataIndex: 'action',
    slots: { customRender: 'action' },
  },
});
const pageType = ref<string>('');
const jobData = ref<any>();
const [registerAddJob, { openModal: modalAddJob }] = useModal();
const dirList = ref<any[]>([]); // 数据管理的目录路径
const departmentList = ref<any[]>([]); //部门列表
/** 日历模式-数据 */
const imgTaskTypeList = [
  {
    value: TaskType.InstantTask,
    label: '即时计划',
  },
  {
    value: TaskType.ScheduledTask,
    label: '定时计划',
  },
  {
    value: TaskType.RepeatingTask,
    label: '重复计划',
  },
  {
    value: TaskType.ConditionalTask,
    label: '接警任务',
  },
  {
    value: TaskType.PilotFlightTask,
    label: 'pilot飞行',
  },
  {
    value: TaskType.ManualFlightTask,
    label: '手动飞行',
  },
  {
    value: TaskType.PoliceTask,
    label: '警情任务',
  },
];
const getPopupContainer = (triggerNode) => triggerNode.parentNode;
const getPopupContainer1 = (triggerNode) => document.body;

let queryParam = reactive<any>({
  year: '',
  month: '',
  jobType: '',
  status: '',
  jobName: '',
  sysOrgCode: toRef(sysOrgCode), //当前选择的部门
});
const dataList = ref<any>(); // 当前月份数据
const activeItem = ref<any>({}); // 点击选中的数据
const popoverVisible = ref<boolean>(false);

const resultIcon = computed(() => (type: string, record): any => {
  switch (type) {
    case 'resultStatus': {
      return resultStatus.find((item) => item.value === record.mediaUploadStatus)?.label || '-';
    }
    case 'isShowIcon': {
      return iconList.find((item) => item.value === record.status)?.icon ? true : false;
    }
    case 'icon': {
      return iconList.find((item) => item.value === record.status)?.icon;
    }
    case 'icon1': {
      return iconList1.find((item) => item.value === record.mediaUploadStatus)?.icon;
    }
    case 'statusTxt': {
      return optionsList.value.status.find((item) => item.value === record.status)?.label || '-';
    }
  }
});

const jobTypeBgColor = computed(() => (jobType) => {
  const list = [
    { value: TaskType.InstantTask, bg: '#3D9EF1' },
    { value: TaskType.ScheduledTask, bg: '#FFAF2B' },
    { value: TaskType.RepeatingTask, bg: '#3DC6F1' },
    { value: TaskType.ConditionalTask, bg: '#FF6A2B' },
    { value: TaskType.PilotFlightTask, bg: '#3D9EF1' },
    { value: TaskType.ManualFlightTask, bg: '#3D9EF1' },
    { value: TaskType.PoliceTask, bg: '#3D9EF1' },
  ];
  return list.find((item) => item.value === jobType)?.bg || '';
});

const getJobTypeTxt = computed(() => (jobType) => {
  const jobTypeList = optionsList.value.jobType;
  return jobTypeList.find((item) => item.value === jobType)?.label || '-';
});

function memberQuery(val) {
  let name = '';
  if (user.value && user.value.length > 0) {
    user.value.forEach((item) => {
      if (item.username == val) {
        name = item.realname;
        // return item.realname
      }
    });
    return name;
  }
}

function normalizeTree(list = []) {
  return list.map((item) => ({
    ...item,
    key: item.orgCode,
    value: item.orgCode,
    disabled: item.disableCheckbox === true,
    children: item.children ? normalizeTree(item.children) : [],
  }));
}

onMounted(async () => {
  //console.log("初始化")
  // queryListParam.year = dayjs().year()
  // queryListParam.month = dayjs().month() + 1
  // queryListParam.day = dayjs().date()
  // searchQuery()
  const myDepart = localStorage.getItem('myDepartList');
  const myDepartAndChildrenTree = localStorage.getItem('myDepartAndChildrenTree');
  // departmentList.value = myDepartAndChildrenTree ? JSON.parse(myDepartAndChildrenTree) : [];
  departmentList.value = myDepartAndChildrenTree && myDepartAndChildrenTree !== 'null' ? normalizeTree(JSON.parse(myDepartAndChildrenTree)) : [];
  sysOrgCode.value = myDepart && myDepart !== 'null' ? JSON.parse(myDepart)[0].orgCode : '';
  myDepartListOrgCode.value.push(...JSON.parse(myDepart).map((item) => item.orgCode));
  queryParam.year = dayjs().year();
  queryParam.month = dayjs().month() + 1;
  // queryParam.sysOrgCode = myDepart ? JSON.parse(myDepart)[0].orgCode : '';
  const res = await getUserList({
    pageNo: 1,
    pageSize: 1000,
  });
  user.value = res.records;

  getData();
});

const clickTab = (index: number) => {
  tabIndex.value = index;
  if (index === 1) {
    if (dateValue.value) {
      queryListParam.year = dateValue.value.year();
      queryListParam.month = dateValue.value.month() + 1;
      queryListParam.day = dateValue.value.date();
    } else {
      queryListParam.year = dayjs().year();
      queryListParam.month = dayjs().month() + 1;
      queryListParam.day = dayjs().date();
    }
    queryListParam.startDate = dayjs(dateRange.value[0].$d).format('YYYY-MM-DD');
    queryListParam.endDate = dayjs(dateRange.value[1].$d).format('YYYY-MM-DD');
    console.log('range', dateRange.value);

    // queryListParam.day = dayjs().date();
    searchQuery();
  }
  if (index === 2) {
    // 初始化日历模式
    if (dateValue.value) {
      queryParam.year = dateValue.value.year();
      queryParam.month = dateValue.value.month() + 1;
    } else {
      queryParam.year = dayjs().year();
      queryParam.month = dayjs().month() + 1;
    }
    getData();
  }
};

// const outCalendarChange = async (type: string, value) => {
//   //console.log("outCalendarChange--", value)
//   let clone = dateValue.value.clone();
//   switch (type) {
//     case 'year':
//       {
//         clone = clone.year(value);
//         calendarChange(clone);
//       }
//       break;
//     case 'month':
//       {
//         clone = clone.month(value);
//         calendarChange(clone);
//       }
//       break;
//   }
// };

const selectBlur = () => {
  setTimeout(() => {
    dropdownVisibleChange(false);
  }, 100);
};

const dropdownVisibleChange = (value?: boolean) => {
  //console.log("dropdownVisibleChange--value", value)
  if (typeof value !== 'undefined') {
    calendarVisible.value = value;
  } else {
    calendarVisible.value = true;
  }
};

// const onPanelListChange = (value: Dayjs) => {
//   console.log('日历触发');
//   dateValue.value = value;
//   queryListParam.year = value.year();
//   queryListParam.month = value.month() + 1;
//   queryListParam.day = value.date();
//   searchQuery();
// };

const handleDateRange = (val) => {
  console.log('val', val);
  queryListParam.startDate = val ? dayjs(val[0].$d).format('YYYY-MM-DD') : '';
  queryListParam.endDate = val ? dayjs(val[1].$d).format('YYYY-MM-DD') : '';
  console.log('beginTime==>>', queryListParam);
  searchQuery();
};

// const calendarChange = (value: Dayjs) => {
//   console.log('日历改变', value.date(), value.month() + 1);
//   calendarVisible.value = false;
//   dateValue.value = value;
//   queryListParam.year = value.year();
//   queryListParam.month = value.month() + 1;
//   queryListParam.day = value.date();
//   searchQuery();
// };

const getMonths = (value: Dayjs) => {
  const localeData = value.localeData();
  const months = [];
  for (let i = 0; i < 12; i++) {
    months.push(localeData.monthsShort(value.month(i)));
  }
  return months;
};

const getYears = (value: Dayjs) => {
  const year = value.year();
  const years: any[] = [];
  for (let i = year - 10; i < year + 10; i += 1) {
    years.push(i);
  }
  return years;
};

const searchQuery = throttle(() => {
  const params = { ...queryListParam };
  Object.keys(params).forEach((key) => {
    if (params[key] === '' || params[key] === null) {
      params[key] = undefined;
    }
  });
  //console.log("列表参数--params", params)
  nextTick(() => {
    setProps({ searchInfo: toRaw(params) });
    reload();
  });
}, 1000);

const addTask = () => {
  pageType.value = PageType.Add;
  modalAddJob(true);
};

//权限控制  true: 有权限  false: 无权限
const btnAuth = (type: string, record): boolean => {
  const { status, virtual } = record;
  const btnAccess = {
    编辑: [TaskStatus.NotStart, TaskStatus.Pending].includes(status),
    复制: true,
    删除: virtual, // 仅虚拟任务可以删除!
    挂起: [TaskStatus.NotStart].includes(status),
    恢复: [TaskStatus.Pending].includes(status),
    返航: [TaskStatus.Progress].includes(status),
    断点续飞: [TaskStatus.PartialSuccess].includes(status),
    查看轨迹: [TaskStatus.Success, TaskStatus.PartialSuccess].includes(status),
  };
  return btnAccess[type];
};

const createActions = (record: EditRecordRow, column: BasicColumn): ActionItem[] => {
  return [
    {
      label: '编辑',
      icon: 'ep:edit',
      onClick: handleAction.bind(null, PageType.Edit, record),
      ifShow: btnAuth('编辑', record) && record.status != 0 && myDepartListOrgCode.value.includes(sysOrgCode),
    },
    {
      label: '复制',
      icon: 'radix-icons:copy',
      onClick: handleAction.bind(null, PageType.COPY, record),
      ifShow:
        btnAuth('复制', record) &&
        record.jobType != 6 &&
        record.jobType != 5 &&
        record.jobType != 7 &&
        record.status != 0 &&
        myDepartListOrgCode.value.includes(sysOrgCode),
    },
    {
      label: '删除',
      icon: 'icon-park-outline:delete',
      onClick: handleAction.bind(null, 'del', record),
      ifShow: btnAuth('删除', record) && record.status != 0 && myDepartListOrgCode.value.includes(sysOrgCode),
    },
  ];
};

const getDropDownActions = (record) => {
  const { jobId, deviceId, id } = record;
  let arr = [
    {
      label: '挂起计划',
      popConfirm: {
        title: '是否挂起计划？',
        confirm: async () => {
          await $http?.postWaylinejobPause(jobId);
          searchQuery();
        },
      },
      ifShow: btnAuth('挂起', record) && myDepartListOrgCode.value.includes(sysOrgCode),
    },
    {
      label: '恢复计划',
      popConfirm: {
        title: '是否恢复计划？',
        confirm: async () => {
          await $http?.postWaylinejobResume(jobId);
          searchQuery();
        },
      },
      ifShow: btnAuth('恢复', record) && myDepartListOrgCode.value.includes(sysOrgCode),
    },
    {
      label: '断点续飞',
      popConfirm: {
        title: '是否断点续飞？',
        confirm: async () => {
          await $http?.postWaylinejobContinue(id);
          searchQuery();
        },
      },
      ifShow: btnAuth('断点续飞', record) && myDepartListOrgCode.value.includes(sysOrgCode),
    },
    {
      label: '返航',
      popConfirm: {
        title: '是否返航？',
        confirm: async () => {
          const result = await $http?.getDeviceShadowGet(deviceId);
          //console.log("getDeviceShadowGet--result", result)
          if (result) {
            const { deviceSn } = result;
            await $http?.postDeviceDebugInstructPost({
              deviceId: deviceId,
              deviceSn: deviceSn,
              instuct: 'return_home',
              reqId: localStorage.getItem('wsClientId'),
            });
            searchQuery();
          }
        },
      },
      ifShow: btnAuth('返航', record) && myDepartListOrgCode.value.includes(sysOrgCode),
    },
    {
      label: '查看轨迹',
      onClick: handleAction.bind(null, 'viewWayLine', record),
      ifShow: btnAuth('查看轨迹', record),
    },
  ];
  return arr;
};

const handleAction = async (type: string, record) => {
  //console.log("功能操作--record", type, record)
  const { jobId, deviceId, id, taskSubId, sysOrgCode } = record;
  switch (type) {
    case PageType.Edit:
      {
        const result = await $http?.getWaylinejobGet({ jobId });
        if (result) {
          pageType.value = PageType.Edit;
          jobData.value = { ...result, jobParameters: undefined };
          modalAddJob(true);
        }
      }
      break;
    case PageType.COPY:
      {
        const result = await $http?.getWaylinejobGet({ jobId });
        if (result) {
          pageType.value = PageType.COPY;
          jobData.value = { ...result, jobParameters: undefined };
          modalAddJob(true);
        }
      }
      break;
    case 'del':
      {
        createConfirm({
          title: '删除计划',
          content: '确定删除当前计划吗？',
          iconType: 'warning',
          onOk: async () => {
            await $http?.deleteWaylinejobDel(jobId);
            searchQuery();
          },
          onCancel: () => {},
        });
      }
      break;
    case 'viewWayLine': // TODO: 需要杨柳那边做好航线详情!
      {
        sysOrgCodeRef.value = sysOrgCode;
        const result = await $http?.getDeviceGet({
          // 获取机场设备型号
          deviceId: deviceId,
          sysOrgCode,
        });
        if (result && result.deviceSn) {
          const resultSN = await $http?.getDeviceQuery({
            // 通过机场设备型号获取飞行器设备型号(一对一)
            deviceType: result.deviceType,
            deviceSn: result.deviceSn,
            sysOrgCode,
          });
          const { records } = resultSN;
          //console.log("resultSN", resultSN)
          if (records?.length && records[0]?.deviceList?.length) {
            trackPlaybackOpen.value = true;
            trackPlaybackDeviceSn.value = records[0]?.deviceList[0].deviceSn;
            trackTaskId.value = taskSubId ? taskSubId : id;
          }
        }
      }
      break;
  }
};

const enterSearchList = (value) => {
  //console.log("查询文件==value", value)
  queryListParam.jobName = value;
  searchQuery();
};

const selectChangeList = (type: string, value?: any) => {
  //console.log("value222222222", value)
  switch (type) {
    case 'status':
      {
        queryListParam.status = value ?? '';
        searchQuery();
      }
      break;
    case 'jobType':
      {
        queryListParam.jobType = value ?? '';
        searchQuery();
      }
      break;
  }
};

const searchTree = (tree: any) => {
  dirList.value.push({
    name: tree?.directoryName,
    id: tree.id,
    dirType: tree.directoryType,
  });
  if (tree.children) {
    searchTree(tree.children);
  }
};

const openDataManage = async (record) => {
  if (!resultIcon.value('isShowIcon', record)) return;
  // const result = await $http?.getDirectoryGetDirByJob({ jobId: record.children ? record.id : record.taskSubId })
  const result = await $http?.getDirectoryGetDirByJob({ jobId: record.taskSubId || record.id, sysOrgCode: record.sysOrgCode });
  dirList.value = [];
  searchTree(result);
  //console.log("跳转到数据管理=====", dirList.value)
  sessionStorage.setItem(DataDirNavigation, JSON.stringify(dirList.value));
  router.push({
    path: '/dataManage/dataManage',
    query: {
      sysOrgCode: record.sysOrgCode,
    },
  });
};

const openWayline = (record) => {
  router.push({
    path: '/wayline',
    query: { workspaceId: localStorage.getItem('Project_Id'), waylineId: record.waylineId },
  });
};

function success() {
  searchQuery();
}

/** 日历模式-方法 */
const jobImgUrl = (name: string, suffix = 'png') => {
  return getIconUrl(name, 'icons/job/', suffix);
};

//权限控制  true: 有权限  false: 无权限 (用在template上)
const btnAuthTemplate = computed(() => (type: string): boolean => {
  const { status } = activeItem.value;
  const btnAccess = {
    复制: true,
    删除: ![TaskStatus.Progress].includes(status),
    查看轨迹: [TaskStatus.Success].includes(status),
  };
  return btnAccess[type] || false;
});

const getTaskTypeIcon = computed((): string => {
  let imgName = imgTaskTypeList.find((item) => item.value === activeItem.value.jobType)?.label || '';
  // console.log('imgName', imgName);

  switch (imgName) {
    case '即时计划':
      imgName = '即时任务';
      break;
    case '定时计划':
      imgName = '定时任务';
      break;
    case '重复计划':
      imgName = '重复任务';
      break;
    case '接警任务':
      imgName = '条件任务';
      break;

    default:
      imgName = '即时任务';
      break;
  }
  return imgName ? jobImgUrl(imgName) : jobImgUrl('即时任务');
});

const getTaskTypeName = computed((): string => {
  return imgTaskTypeList.find((item) => item.value === activeItem.value.jobType)?.label || '';
});

const onPanelChange = (value: Dayjs) => {
  console.log('日历模式-日历触发', value);
  dateValue.value = value;
  queryParam.year = value.year();
  queryParam.month = value.month() + 1;
  queryParam.day = value.date();
  getData();
};

async function getData() {
  const params = { ...queryParam };
  Object.keys(params).forEach((key) => {
    if (params[key] === '' || params[key] === null) {
      params[key] = undefined;
    }
  });
  const result = await $http?.getTaskQureyTaskListByMonth({
    ...params,
  });
  //console.log("result", result)
  if (result) {
    dataList.value = result;
  }
}

const getListData = (value: Dayjs) => {
  let renderList;
  const date = value.date();
  const month = value.month() + 1;
  //console.log("getListData--valueTemp", month, date)
  if (date >= 0 && dataList.value?.length && queryParam.month === month) {
    const dataListTemp = dataList.value;
    const { wayLineTaskVoList } = dataListTemp[date - 1] || {};
    //console.log("getListData--wayLineTaskVoList", wayLineTaskVoList)
    renderList = wayLineTaskVoList?.length
      ? wayLineTaskVoList.map((item: any) => {
          return {
            type: item?.status === TaskStatus.Success ? 'success' : 'warning',
            content: `${item?.planTime || ''}  ${item?.taskName || ''}`,
            id: item.id, // 唯一标识
            initialData: { ...item },
          };
        })
      : [];
  }
  return renderList || [];
};

// 切换到list模式
const switchList = (value: Dayjs) => {
  dateRange.value = [dayjs(value.$d).startOf('day'), dayjs(value.$d).endOf('day')];
  clickTab(1);
};

const openTaskModal = async (type) => {
  popoverVisible.value = false;
  await nextTick();
  pageType.value = type;
  modalAddJob(true);
};

const hoverDetail = (item, value: Dayjs) => {
  //console.log("当天的当前任务数据--item", item.initialData)
  dateValue.value = value; // 这里即时更新当前天数，用于解决弹框被遮盖问题 !
  activeItem.value = item.initialData;
  popoverVisible.value = true;
};

const selectChange = (type: string, value?: any) => {
  //console.log("value", value)
  switch (type) {
    case 'status':
      {
        queryParam.status = value ?? '';
        getData();
      }
      break;
    case 'jobType':
      {
        queryParam.jobType = value ?? '';
        getData();
      }
      break;
  }
};

const enterSearch = (value) => {
  //console.log("查询文件==value", value)
  queryParam.jobName = value;
  getData();
};

const popoverHandleClick = async (type: string) => {
  switch (type) {
    case 'viewWayLine': // TODO: 需要杨柳那边做好航线详情!
      {
        if (!btnAuthTemplate.value('查看轨迹')) return;
        popoverVisible.value = false;
        await nextTick();
      }
      break;
    case 'copy':
      {
        if (!btnAuthTemplate.value('复制')) return;
        const result = await $http?.getWaylinejobGet({ jobId: activeItem.value.jobId });
        if (result) {
          //console.log("开始复制---打开弹框")
          popoverVisible.value = false;
          //await nextTick()
          pageType.value = PageType.COPY;
          jobData.value = result;
          modalAddJob(true);
        }
      }
      break;
    case 'del':
      {
        if (!btnAuthTemplate.value('删除')) return;
        //console.log("删除任务--activeItem", activeItem.value)
        createConfirm({
          title: '删除计划',
          content: '确定删除当前计划吗？',
          iconType: 'warning',
          getContainer: (): any => {
            return document.getElementById('modal-right');
          },
          //getContainer: triggerNode => triggerNode.parentNode,
          onOk: async () => {
            await $http?.deleteWaylinejobDel(activeItem.value.jobId);
            popoverVisible.value = false;
            await nextTick();
            getData();
          },
          onCancel: () => {},
        });
      }
      break;
  }
};
</script>
<style lang="less" scoped>
/deep/ .job-container-wrap .jeecg-basic-table-header__toolbar > * {
  margin-right: 0px;
}

/deep/ .job-container-wrap .jeecg-basic-table-header__toolbar {
  position: relative;
  width: 100%;
  justify-content: flex-end;
  z-index: 1;
}

/deep/ .cell-content::before {
  content: url('/@/assets/icons/FolderOpenFilled.svg');
  margin-right: 10px;
  vertical-align: middle;
}

/deep/ .cell-content {
  cursor: pointer;
}

/deep/ .ant-table-thead > tr > th {
  background: #edf6fb;
  color: #0f699a;
}

/deep/ .ant-table-tbody > tr.ant-table-row:hover > td,
.ant-table-tbody > tr > td.ant-table-cell-row-hover {
  background: #f7fbfc;
}

/deep/ .ant-popover-buttons {
  display: flex;
}

.p-4 {
  padding: 20px;

  .job-container-wrap {
    padding: 16px;
    background-color: #fff;
    position: relative;
    display: flex;
    justify-content: space-between;

    .table-wrap {
      position: relative;
      width: 100%;

      .title-wrap {
        position: absolute;
        left: 0;
        top: 6px;
        z-index: 9;
        display: flex;
        align-items: center;

        .title {
          width: 100px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #333333;
          text-align: center;
        }
      }

      .calendar {
        display: none;

        &.visible {
          position: absolute;
          left: 0;
          top: 45px;
          display: block;
          width: 30%;
          height: 300px;
          z-index: 10;
        }

        .calendar-top {
          height: 45px;
          display: none;

          .title-wrap {
            display: flex;
            align-items: center;

            .title {
              width: 100px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 500;
              color: #333333;
              text-align: center;
            }
          }
        }
      }

      .customCol {
        .openDir:hover {
          cursor: pointer;
        }
      }

      .resultWrap {
        display: flex;
        justify-content: center;
        cursor: pointer;

        .resultNum {
          margin-left: 5px;
          color: #3eacef;
        }
      }

      .statusWrap {
        display: flex;
        justify-content: center;
        align-items: center;

        .text {
        }

        .tips {
          margin-top: 8px;
          margin-left: 5px;
        }
      }

      .waylineNameWrap {
        color: #1890ff;
        cursor: pointer;

        .waylineNameItem {
          width: 90%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      .jobTypeWrap {
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 12px;
        color: #fff;

        .jobTypeItem {
          width: 64px;
          height: 22px;
          display: flex;
          justify-content: center;
          align-items: center;

          &.icon {
            border-radius: 11px;
          }
        }
      }

      .toolbarIcon {
        width: 120px;
        display: flex;
        align-items: center;
        z-index: 1000;
        .icon-calendar {
          width: 38px;
          height: 32px;
          display: flex;
          justify-content: center;
          align-items: center;
          cursor: pointer;
        }

        .icon-list {
          width: 38px;
          height: 32px;
          display: flex;
          justify-content: center;
          align-items: center;
          color: #fff;
          background: linear-gradient(56deg, #2c9ad6 0%, #167db5 100%);
          border-radius: 0px 16px 16px 0px;
        }
      }

      .toolbar {
        display: flex;

        // margin-top: 3px;
        .addTask {
          background: linear-gradient(315deg, #2997d2 0%, #1880b9 100%);
          box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.16);
          border-radius: 2px;
          color: white;
        }
      }
    }
  }

  .job-wrap {
    padding: 16px;
    background-color: #fff;

    .job-calendar {
      width: 100%;

      .calendar-top {
        position: relative;
        padding: 6px;
        padding-right: 0px;
        padding-left: 0px;
        display: flex;
        justify-content: space-between;

        .title-wrap {
          display: flex;
          height: 30px;
          align-items: center;

          .title {
            width: 100px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            color: #333333;
            text-align: center;
          }
        }

        .tabs-icon {
          width: 120px;
          height: 100%;
          display: flex;
          align-items: center;

          .icon-calendar {
            width: 38px;
            height: 30px;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #fff;
            background: linear-gradient(56deg, #2c9ad6 0%, #167db5 100%);
            border-radius: 16px 0px 0px 16px;
          }

          .icon-list {
            width: 38px;
            height: 30px;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
          }
        }

        .search-mod {
          .addTask {
            background: linear-gradient(315deg, #2997d2 0%, #1880b9 100%);
            box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.16);
            border-radius: 2px;
            color: white;
          }
        }
      }

      .events {
        .job-calendar-item {
          &.active {
            background: linear-gradient(180deg, #e7f4f9 0%, rgba(197, 224, 237, 0) 100%);
            border-radius: 16px;
            border: 1px solid rgba(11, 121, 181, 0.6);
          }

          .badge {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
    }
  }
}
.job-container-wrap {
  .ant-picker-body {
    background: rgba(237, 248, 255, 0.5);
    border-radius: 4px;
  }
}

.job-wrap {
  .ant-picker-body {
    background: rgba(237, 248, 255, 0.5);
    border-radius: 4px;

    .ant-picker-cell-selected {
      z-index: 99;
    }
  }
}
</style>
<style lang="less">
.popover-content {
  width: 486px;
  height: 300px;
  background: #ffffff;
  border-radius: 2px;

  .title-wrap {
    width: 100%;
    height: 70px;
    padding: 0px 28px;
    display: flex;
    align-items: center;
    background: rgba(147, 197, 253, 0.5);

    .icon-wrap {
      display: flex;
      flex-direction: column;

      .icon {
        width: 41px;
        height: 36px;
      }

      .desc {
        color: #333;
      }
    }

    .title {
      // height: 22px;
      margin-left: 30px;
      font-size: 16px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      color: #333333;
      line-height: 22px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-between;
      gap: 5px;
    }
  }

  .middle {
    width: 100%;
    height: 200px;
    padding: 0px 28px;
    display: flex;
    align-items: center;
    flex-wrap: wrap;

    .item {
      width: 50%;
      display: flex;

      .icon-wrap {
        .title-icon {
          width: 45px;
          height: 45px;
        }
      }

      .item-content {
        width: 80%;
        margin-left: 20px;
        display: flex;
        flex-direction: column;

        .label {
          font-size: 14px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 600;
          color: #333;
        }

        .value {
          font-size: 14px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #666;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      &.status {
      }

      &.wayLine {
        .value {
          display: flex;

          .icon {
            margin-right: 1px;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #167db5;
          }

          .txt {
            font-size: 14px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            // color: #167DB5;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;

            :hover {
              border-bottom: 1px solid #167db5;
            }
          }
        }
      }

      &.device {
      }

      &.medium {
      }
    }
  }

  .footer {
    width: 100%;
    height: 30px;
    padding: 0px 28px;
    display: flex;
    justify-content: space-between;

    .left {
      font-size: 14px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #666666;
    }

    .right {
      display: flex;
      font-size: 18px;

      .icon {
        width: 24px;
        height: 24px;
        margin-left: 10px;

        &.disabled {
          cursor: not-allowed;
        }
      }
    }
  }
}
</style>
