<template>
  <BasicModal
    :width="650"
    :height="250"
    :title="moveTypeMap[props.moveType]"
    @visible-change="handleVisibleChange"
    @register="register"
    @cancel="cancelModal"
    @ok="okModal"
    :canFullscreen="false"
  >
    <div class="moveFileBox">
      <div class="head-title-box" v-if="props.moveType !== ''">
        <template v-if="props.moveType === 'file'">
          <div class="title">文件名称：{{ props.fileName }}</div>
        </template>
        <template v-if="props.moveType === 'dir'">
          <div class="title">当前文件夹路径</div>
          <div class="ericLine"></div>
          <div class="path">
            <FolderOpenFilled style="color: #ffb32b; font-size: 20px" />
            <div class="text">{{ dirNavigation }}</div>
          </div>
        </template>
      </div>
      <div class="select-dir">
        <div class="left">移动到：</div>
        <div class="right">
          <a-cascader
            v-model:value="valueRef"
            :options="dirTree"
            expand-trigger="hover"
            placeholder="请选择文件夹"
            change-on-select
            style="width: 60%"
            :fieldNames="{ label: 'directoryName', value: 'id', children: 'children' }"
          />
        </div>
      </div>
    </div>
  </BasicModal>
</template>
<script lang="ts" setup>
import { onMounted, computed, ref, reactive, defineProps, watch, defineEmits } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import { getProjectDirList, postDirMove, putFileMove, putFileBatchMove } from '../data.api';
import { useMessage } from '/@/hooks/web/useMessage';
import { FolderOpenFilled, CloseOutlined, CaretRightFilled } from '@ant-design/icons-vue';
import { Item } from 'ant-design-vue/lib/menu';

const props = defineProps({
  moveType: {
    // 移动类型: 'file' | 'dir'
    type: String,
    default: '',
  },
  dirList: {
    type: Array,
    default: [],
  },
  dirId: { type: [String, Number] },
  fileId: { type: [String, Number] },
  fileName: { type: String, default: '' },
  fileIdList: {
    type: Array,
    default: [],
  },
  oldDirIdList: {
    type: Array,
    default: [],
  },
  sysOrgCode: {
    type: String,
    default: '',
  },
});

const moveTypeMap = {
  file: '移动文件',
  dir: '移动文件夹',
  '': '批量移动',
};

const { createMessage, createConfirm } = useMessage();
const [register, { closeModal }] = useModalInner();
const emits = defineEmits(['success', 'register']);
const dirTree = ref<any[]>([]); // 文件夹树(过滤后的)
const valueRef = ref<string[]>([]);

const dirNavigation = computed(() =>
  props.dirList.reduce((prev, next: any, index) => {
    const lastItem = index > 0 && index === props.dirList?.length - 1;
    prev += `${next?.name} ${!lastItem ? '/' : ''}`;
    return prev;
  }, '')
);

onMounted(() => {
  //initData()
});

const initData = async () => {
  const result = await getProjectDirList({
    dirType: '3',
    sysOrgCode: props.sysOrgCode,
  });
  //console.log("获取文件夹树--", result, props)
  if (result?.length) {
    dirTree.value = result || [];

    // 移动目录时需要置灰自身目录和父目录，移动文件时置灰父目录
    // dirTree.value = disabledTree(result)
    // console.log("更新文件夹树--", dirTree.value)
  }
};

// const disabledTree = (tree: any[]) => {
//   if(!tree?.length) return []
//   for(let item of tree){
//     if(props.moveType === 'file'){
//       if(item.id === props.dirList[props.dirList?.length -1]?.id){  // 移动文件还是当前父目录则置灰不可选
//         item.disabled = true
//       }else{
//         item.disabled = false
//       }
//     }else if(props.moveType === 'dir'){    // 移动目录还是当前父目录或者自身目录，则置灰不可选
//       if(item.id === props.dirList[props.dirList?.length -1]?.id || item.id === props.dirId ){  // 移动文件还是当前父目录则置灰不可选
//         item.disabled = true
//       }else{
//         item.disabled = false
//       }
//     }
//     if(item.children && item.children?.length ){
//       item.children = disabledTree(item.children)
//     }
//   }
//   return tree
// }

const handleVisibleChange = (visible) => {
  //console.log("打开文件夹弹框--", visible)
  if (visible) {
    initData();
  }
};

const okModal = async () => {
  //console.log("移动文件/文件夹--value", valueRef.value)
  const curDirList = valueRef.value;
  if (!curDirList?.length) {
    createMessage.warning('请选择新文件夹');
    return;
  }
  const dirListTemp = props.dirList;
  const oldParentId = dirListTemp[dirListTemp?.length - 1]?.id;
  const newDirId = curDirList[curDirList?.length - 1];
  console.log('props.moveType', props.moveType);

  console.log('移动文件/文件夹--', 'oldParentId', oldParentId, 'newDirId', newDirId, 'props.dirId', props.dirId);
  if (props.moveType === 'file') {
    if (newDirId === oldParentId) {
      createMessage.warning('相同路径移动无效');
      return;
    }

    await putFileMove({ id: props.fileId, newDirId: newDirId, oldDirId: oldParentId, sysOrgCode: props.sysOrgCode });
  } else if (props.moveType === 'dir') {
    if (newDirId === oldParentId) {
      createMessage.warning('相同路径移动无效');
      return;
    }
    if (newDirId === props.dirId) {
      createMessage.warning('自身路径移动无效');
      return;
    }
    await postDirMove({
      id: props.dirId,
      newParentId: newDirId,
      oldParentId: oldParentId,
      sysOrgCode: props.sysOrgCode,
    });
  } else {
    if (newDirId === oldParentId) {
      createMessage.warning('自身路径移动无效');
      return;
    }
    await putFileBatchMove({
      fileIdList: props.fileIdList,
      newDirId: newDirId,
      oldDirIdList: props.oldDirIdList,
      sysOrgCode: props.sysOrgCode,
    });
  }
  cancelModal();
};

const cancelModal = () => {
  valueRef.value = [];
  emits('success');
  closeModal();
};
</script>
<style lang="less" scoped>
/deep/.scroll-container {
  padding: 0;
}

.moveFileBox {
  .head-title-box {
    display: flex;
    align-items: center;
    background: linear-gradient(180deg, #e8f3f8 0%, rgba(232, 243, 248, 0) 100%);
    height: 40px;
    border: 1px solid rgba(11, 121, 181, 0.6);
    padding: 0 13px;
    .title {
      color: #60a7ce;
      font-size: 14px;
      font-weight: 500;
      font-family: PingFangSC-Medium, PingFang SC;
    }
    .ericLine {
      width: 1px;
      height: 20px;
      background-color: #cccccc;
      margin: 0 14px;
    }
    .path {
      display: flex;
      .text {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #666666;
        margin-left: 6px;
      }
    }
  }
  .select-dir {
    margin-top: 50px;
    margin-left: 30px;
    display: flex;
    .left {
      width: 70px;
      margin-left: 5px;
    }
    .right {
      width: 450px;
    }
  }
  .rightBox {
    flex: 1;
    padding-left: 12px;
    .title {
    }
    .Box {
      display: flex;
      justify-content: flex-start;
      width: 692px;
      overflow-x: auto;
      .listBox {
        background: linear-gradient(180deg, #ffffff 0%, #f9fcfd 100%);
        padding: 8px 6px;
        min-height: 464px;
        // width: 218px;
        margin-right: 12px;
        border: 2px solid #d3e7f1;
        .list {
          width: 206px;
          height: 32px;
          // background-color: #fcfdfe;
          border-radius: 2px;
          // border: 1px solid rgba(11, 121, 181, 0.6);
          display: flex;
          align-items: center;
          justify-content: space-between;
          cursor: pointer;
          .text {
            display: flex;
            align-items: center;

            span {
              font-size: 14px;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: #333333;
              white-space: nowrap;
              text-emphasis: none;
              overflow: hidden;
            }
          }
          .CaretRight {
            color: #999999;
            margin-right: 6px;
          }
        }
        .list:hover {
          background-color: #167db5;
          span {
            color: #ffffff;
          }
          .CaretRight {
            color: white;
          }
        }
      }
    }
  }
}
</style>
