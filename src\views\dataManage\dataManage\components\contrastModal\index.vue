<template>
  <Modal v-model:visible="visible" :centered="true" width="100%" :afterClose="afterClose" :footer="null">
    <template #closeIcon>
      <close-outlined style="color: #fff" />
    </template>
    <div class="ModalBox">
      <div class="headBox">
        <div class="title">时间轴</div>
        <a-slider class="slider" :step="null" v-model:value="value2" :marks="marks" :tooltipVisible="false" range @afterChange="onAfterChange" />
      </div>
      <div class="contentBox">
        <div class="FunctionSwitchingBox">
          <div
            class="but"
            :class="{ selectBut: CurrentMode === item.key, show: ImageInfo.leftImage.fileType == '4' && (item.key == 1 || item.key == 2) }"
            v-for="item in butList"
            @click="pattern(item)"
            >{{ item.name }}</div
          >
        </div>
        <div class="sameScreen" v-show="CurrentMode == 0">
          <div class="pictureLeft" v-show="props.data.fileType != 4">
            <img ref="image1" :src="ImageInfo.leftImage.labelUrl || ImageInfo.leftImage.hdUrl" />
          </div>
          <div class="pictureRight" v-show="props.data.fileType != 4">
            <img ref="image2" :src="ImageInfo.rightImage.labelUrl || ImageInfo.rightImage.hdUrl" />
          </div>
          <orthoimage ref="orthoimageRef" v-if="props.data.fileType == 4" :options="ImageInfo"></orthoimage>
        </div>
        <div class="contrastBox" v-show="CurrentMode == 1">
          <!-- <ImageCompare :leftImage="ImageInfo.leftImage.hdUrl" :rightImage="ImageInfo.rightImage.hdUrl"></ImageCompare> -->
          <!-- <ImageCompareTwo :upperImg="ImageInfo.leftImage.hdUrl" :bottomImg="ImageInfo.rightImage.hdUrl"></ImageCompareTwo> -->
          <ImageCompareCopy
            ref="ImageCompareCopyRef"
            :upperImg="ImageInfo.leftImage.labelUrl || ImageInfo.leftImage.hdUrl"
            :bottomImg="ImageInfo.rightImage.labelUrl || ImageInfo.rightImage.hdUrl"
          ></ImageCompareCopy>
        </div>
        <div class="FusionBox" v-show="CurrentMode == 2">
          <ImageFusion
            ref="ImageFusionRef"
            :data="[ImageInfo.leftImage.labelUrl || ImageInfo.leftImage.hdUrl, ImageInfo.rightImage.labelUrl || ImageInfo.rightImage.hdUrl]"
          ></ImageFusion>
        </div>
      </div>
    </div>
  </Modal>
</template>
<script lang="ts" setup>
import { ref, watch, reactive, onUnmounted } from 'vue';
import { Modal } from 'ant-design-vue';
import ImageCompare from './ImageCompare.vue';
import ImageCompareTwo from './ImageCompareTwo.vue';
import ImageCompareCopy from './ImageCompareCopy.vue';
import ImageFusion from './ImageFusion.vue';
import { imageComparison } from './data.api';
import { CloseOutlined } from '@ant-design/icons-vue';
import orthoimage from './orthoimage.vue';

const props = defineProps({
  data: Object,
  default: () => ({}),
});

const image1 = ref(null);
const image2 = ref(null);
const scaleFactor = ref(1); // 当前缩放因子
const translateX = ref(0); // 当前水平位移
const translateY = ref(0); // 当前垂直位移
const ImageCompareCopyRef = ref();
const ImageFusionRef = ref();
const orthoimageRef = ref();

const syncImages = () => {
  if (!image1.value || !image2.value) return;
  console.log('img 执行成功');
  const handleWheel = (event) => {
    // 基于滚轮滚动量计算缩放因子
    const scaleChange = event.deltaY < 0 ? 0.1 : -0.1;
    scaleFactor.value = Math.max(0.1, Math.min(10, scaleFactor.value + scaleChange)); // 限制最小和最大缩放因子

    // 应用缩放因子
    // image1.value.style.transform = `scale(${scaleFactor.value})`;
    // image2.value.style.transform = `scale(${scaleFactor.value})`;
    updateTransform();
    // 阻止默认滚轮行为
    event.preventDefault();
  };

  // 更新图片的 transform 样式
  const updateTransform = () => {
    const transformValue = `translate(${translateX.value}px, ${translateY.value}px) scale(${scaleFactor.value})`;
    image1.value.style.transform = transformValue;
    image2.value.style.transform = transformValue;
  };

  const onMouseMove = (e) => {
    // if (isDragging.value) {
    //   const dx = e.clientX - startX.value;
    //   const dy = e.clientY - startY.value;

    //   image1.value.style.transform = `translate(${dx}px, ${dy}px) scale(${scaleFactor.value})`;
    //   image2.value.style.transform = `translate(${dx}px, ${dy}px) scale(${scaleFactor.value})`;
    // }

    if (isDragging.value) {
      const dx = e.clientX - startX.value;
      const dy = e.clientY - startY.value;

      // 更新累积位移
      translateX.value += dx;
      translateY.value += dy;

      // 更新起始点位置
      startX.value = e.clientX;
      startY.value = e.clientY;

      // 应用缩放和当前位移
      updateTransform();
    }
  };

  const onMouseDown = (e) => {
    isDragging.value = true;
    startX.value = e.clientX;
    startY.value = e.clientY;
  };

  const onMouseUp = () => {
    isDragging.value = false;
  };

  const preventDefaultDrag = (e) => {
    e.preventDefault();
  };

  image1.value.addEventListener('wheel', handleWheel);
  image2.value.addEventListener('wheel', handleWheel);

  image1.value.addEventListener('mousedown', onMouseDown);
  image2.value.addEventListener('mousedown', onMouseDown);
  document.addEventListener('mousemove', onMouseMove);
  document.addEventListener('mouseup', onMouseUp);

  // 禁用默认拖动行为
  image1.value.addEventListener('dragstart', preventDefaultDrag);
  image2.value.addEventListener('dragstart', preventDefaultDrag);
};

// 设置拖拽相关的响应式引用
const isDragging = ref(false);
const startX = ref(0);
const startY = ref(0);

let marsksList = ref();
let marks = ref<Record<number, any>>({});

const visible = ref<boolean>(false);
const value2 = ref<[number, number]>([0, 50]);
let ImageInfo = reactive({
  leftImage: '',
  rightImage: '',
});
const open = () => {
  CurrentMode.value = 0;
  const params = {
    fileId: props.data?.fileId,
    position: {
      lat: props.data?.shootPosition?.lat,
      lng: props.data?.shootPosition?.lng,
    },
    sysOrgCode: props.data?.sysOrgCode,
  };
  imageComparison.queryLocationFiles(params).then((res) => {
    // marskTwo.value = res.reduce((acc, file, index) => {
    //   acc[index] = file.createTime; // 使用 createTime 作为 mark 的显示值
    //   return acc;
    // }, {});
    marsksList.value = res;
    // const step = Math.floor(100 / (res.length - 1)); // 计算每个 mark 的步长
    // marks.value = res.reduce((acc, file, index) => {
    //   const markPosition = index * step; // 计算每个时间点的 marks 位置
    //   acc[markPosition] = file.createTime; // 使用 createTime 作为 mark 的显示值
    //   return acc;
    // }, {});

    const totalMarks = res.length - 1; // 总的标记数
    marks.value = res.reduce((acc, file, index) => {
      const markPosition = (index / totalMarks) * 100; // 计算每个标记的精确位置
      if (index === 0 || index === totalMarks) {
        acc[markPosition] = file.createTime; // 使用 createTime 作为 mark 的显示值
      } else {
        acc[markPosition] = '';
      }

      return acc;
    }, {});

    findCurrentAndPreviousData(props.data?.fileId);

    console.log(marks.value, ' marks.value ');
    console.log('newVal', props.data);
  });
  visible.value = true;
  setTimeout(() => {
    syncImages();
    ImageCompareCopyRef.value.getSliderValue(50);
    ImageFusionRef.value.getValue1(50);
    console.log(ImageCompareCopyRef.value);
  }, 1000);
};
const onAfterChange = (value: number) => {
  console.log('afterChange: ', value);
  const [startVal, endVal] = value;

  // 根据滑块的位置反算出文件索引
  // const startIndex = Math.round(startVal / (100 / (marsksList.value.length - 1)));
  // const endIndex = Math.round(endVal / (100 / (marsksList.value.length - 1)));

  // 根据滑块位置查找最近的文件索引
  const findClosestIndex = (val) => {
    return marsksList.value.reduce((closestIndex, _, currentIndex) => {
      const currentMarkPosition = (currentIndex / (marsksList.value.length - 1)) * 100;
      const closestMarkPosition = (closestIndex / (marsksList.value.length - 1)) * 100;
      return Math.abs(currentMarkPosition - val) < Math.abs(closestMarkPosition - val) ? currentIndex : closestIndex;
    }, 0);
  };
  const startIndex = findClosestIndex(startVal);
  const endIndex = findClosestIndex(endVal);

  const startFile = marsksList.value[startIndex];
  const endFile = marsksList.value[endIndex];

  ImageInfo.leftImage = startFile;
  ImageInfo.rightImage = endFile;
  console.log('选中的起始文件数据：', startFile);
  console.log('选中的结束文件数据：', endFile);
  if (orthoimageRef.value) {
    orthoimageRef.value.addTileLayer();
    orthoimageRef.value.addTileLayerTwo();
  }

  const totalMarks = marsksList.value.length - 1;
  marks.value = marsksList.value.reduce((acc, file, index) => {
    const markPosition = (index / totalMarks) * 100;
    if (index === 0 || index === totalMarks) {
      acc[markPosition] = file.createTime; // 保持首尾
    } else if (markPosition === startVal || markPosition === endVal) {
      acc[markPosition] = file.createTime; // 选中位置显示名称
    } else {
      acc[markPosition] = ''; // 其他位置保持空
    }
    return acc;
  }, {});
  console.log('当前滑块 marks:', marks.value);
};

// 找到当前和上一个 fileId 对应的数据
const findCurrentAndPreviousData = (currentFileId) => {
  const currentIndex = marsksList.value.findIndex((file) => file.fileId === currentFileId);
  if (currentIndex !== -1) {
    const currentFile = marsksList.value[currentIndex];
    // const previousFile = currentIndex > 0 ? marsksList.value[currentIndex - 1] : marsksList.value[currentIndex + 1];
    const previousFile = marsksList.value[0]; //需求修改默认为第一张

    // 计算 marks 索引位置
    // const step = Math.floor(100 / (marsksList.value.length - 1));
    const totalMarks = marsksList.value.length - 1; // 总的标记数
    // const currentMarkPosition = currentIndex * totalMarks;
    // const previousMarkPosition = previousFile ? (currentIndex - 1) * totalMarks : (currentIndex + 1) * totalMarks;
    const currentMarkPosition = (currentIndex / totalMarks) * 100; // 当前文件的标记位置
    // const previousMarkPosition = (previousFile ? (currentIndex - 1) / totalMarks : (currentIndex + 1) / totalMarks) * 100; // 上一个文件的标记位置
    const previousMarkPosition = 0;
    if (marsksList.value.length == 2) {
      value2.value = [0, 100];
    } else {
      value2.value = [previousMarkPosition, currentMarkPosition];
    }

    marks.value = marsksList.value.reduce((acc, file, index) => {
      const markPosition = (index / totalMarks) * 100;
      if (index === 0 || index === totalMarks) {
        acc[markPosition] = file.createTime; // 保持首尾
      } else if (markPosition === previousMarkPosition || markPosition === currentMarkPosition) {
        acc[markPosition] = file.createTime; // 选中位置显示名称
      } else {
        acc[markPosition] = ''; // 其他位置保持空
      }
      return acc;
    }, {});

    ImageInfo.leftImage = previousFile;
    ImageInfo.rightImage = currentFile;
    console.log('当前文件数据：', currentFile);
    console.log('上一个文件数据：', previousFile);
    console.log('value2.value', value2.value);
    if (orthoimageRef.value) {
      orthoimageRef.value.addTileLayer();
      orthoimageRef.value.addTileLayerTwo();
    }

    return { currentFile, previousFile };
  } else {
    console.warn('未找到对应的 fileId:', currentFileId);
    return { currentFile: null, previousFile: null };
  }
};

const butList = [
  {
    name: '同屏',
    key: 0,
  },
  {
    name: '卷帘',
    key: 1,
  },
  {
    name: '融合',
    key: 2,
  },
];

const CurrentMode = ref(0);

const pattern = (item) => {
  if (ImageInfo.leftImage.fileType == '4') {
    CurrentMode.value = 0;
  } else {
    CurrentMode.value = item.key;
  }
};

watch(
  () => props.data,
  (newVal) => {
    console.log('newVal', newVal);
  }
);

const afterClose = () => {
  if (props.data.fileType == 4) return;
  scaleFactor.value = 1;
  translateX.value = 0;
  translateY.value = 0;
  startX.value = 0;
  startY.value = 0;
  const transformValue = `translate(${translateX.value}px, ${translateY.value}px) scale(${scaleFactor.value})`;
  image1.value.style.transform = transformValue;
  image2.value.style.transform = transformValue;
};

defineExpose({
  open,
});
</script>
<style lang="less" scoped>
.ModalBox {
  width: 100%;
  height: 880px;
  background-color: #1d1d1d;
  .headBox {
    height: 123px;
    width: 100%;
    background: #0e0e0e;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding: 0 38px;
    .title {
      font-weight: 500;
      font-size: 14px;
      color: rgba(255, 255, 255, 0.8);
      width: 110px;
    }
    .slider {
      width: 90%;
    }
    :deep(.ant-slider-mark-text) {
      font-weight: 400;
      font-size: 12px;
      color: rgba(255, 255, 255, 0.6);
      width: 124px;
    }
    :deep(.ant-slider-mark) {
      top: 22px;
    }
    :deep(.ant-slider-step) {
      background: #3e3e3e;
      height: 8px;
      .ant-slider-dot {
        top: -2px;
        width: 13px;
        height: 13px;
        background-color: #868686;
        border: 2px solid #eaeaea;
      }
      .ant-slider-dot-active {
        border-color: #eaeaea;
      }
    }
    :deep(.ant-slider-handle) {
      width: 25px;
      height: 25px;
      margin-top: -8px;
      position: relative;
    }
    :deep(.ant-slider-handle-1) {
      background-color: #6a9f9b;
      border: solid 2px #1dffed;
    }
    :deep(.ant-slider-handle-1::before) {
      content: '左';
      position: absolute;
      top: 50%;
      left: 50%;
      font-weight: 500;
      font-size: 12px;
      color: #ffffff;
      transform: translate(-50%, -50%);
    }
    :deep(.ant-slider-handle-2) {
      background-color: #637ba1;
      border: solid 2px #5999ff;
      margin-top: -25px;
    }
    :deep(.ant-slider-handle-2::before) {
      content: '右';
      position: absolute;
      top: 50%;
      left: 50%;
      font-weight: 500;
      font-size: 12px;
      color: #ffffff;
      transform: translate(-50%, -50%);
    }
  }
  .contentBox {
    .FunctionSwitchingBox {
      display: flex;
      justify-content: center;
      gap: 42px;
      margin-top: 29px;
      margin-bottom: 35px;
      .but {
        border-radius: 2px;
        padding: 10px 40px;
        background-color: #343434;
        border: 1px solid 343434;
        font-weight: 500;
        font-size: 14px;
        color: rgba(255, 255, 255, 0.8);
        cursor: pointer;
      }
      .selectBut {
        border-radius: 2px;
        border: 1px solid #3473ff;
        background-color: #263f77;
      }

      .show {
        display: none;
      }
    }
    .sameScreen {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 35px;
      .pictureLeft {
        width: 46%;
        height: 471px;
        overflow: hidden;
        cursor: grab; /* 设置拖拽时的光标样式 */
        // border: 1px solid red;
        img {
          width: 100%;
          height: 100%;
        }
      }
      .pictureRight {
        width: 46%;
        height: 471px;
        overflow: hidden;
        cursor: grab; /* 设置拖拽时的光标样式 */
        // border: 1px solid red;
        img {
          width: 100%;
          height: 100%;
        }
      }
    }
    .contrastBox {
      width: 100%;
      height: 500px;
    }
    .FusionBox {
      height: 600px;
      width: 900px;
      margin: 0 auto;
    }
  }
}
</style>
