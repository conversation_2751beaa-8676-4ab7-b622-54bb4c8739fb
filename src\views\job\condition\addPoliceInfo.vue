<template>
  <a-modal :visible="visible" :title="title" @ok="handleOk" @cancel="cancel" width="600px">
    <div class="ant-modal-body">
      <a-form ref="formRef" :model="formState" :rules="rules" :label-col="labelCol" :wrapper-col="wrapperCol" labelAlign="left">
        <a-row>
          <a-col :span="24">
            <a-form-item label="部门" name="sysOrgCode">
              <!-- <a-tree-select
                v-model:value="formState.sysOrgCode"
                show-search
                tree-default-expand-all
                :dropdown-style="{ width: 'auto' }"
                :tree-data="myDepartList"
                :field-names="{ children: 'children', label: 'departName', value: 'orgCode', key: 'orgCode' }"
                :dropdownMatchSelectWidth="false"
                placeholder="请选择部门"
                @change="getDevice"
              >
              </a-tree-select> -->
              <a-select
                v-model:value="formState.sysOrgCode"
                @change="getDevice"
                placeholder="请选择部门"
              >
                <a-select-option v-for="item in myDepartList" :value="item.orgCode">{{ item.departName }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="24">
            <a-form-item label="联动名称" name="name"> <a-input v-model:value="formState.name" placeholder="请输入内容" allowClear /> </a-form-item
          ></a-col>
        </a-row>
        <a-row>
          <a-col :span="24">
            <a-form-item label="触发设备" name="triggerDevicesStr">
              <!-- <a-tree-select
                :field-names="{ children: 'children', value: 'id', label: 'name' }"
                v-model:value="formState.triggerDevicesStr"
                :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                style="width: 100%"
                :tree-data="triggerEquipmentOptions"
                placeholder="请选择"
                allow-clear
                multiple
                :show-search="true"
                :filter-tree-node="filterTreeNode"
                tree-default-expand-all
                tree-checkable
                :show-checked-strategy="SHOW_CHILD"
              >
              </a-tree-select> -->
              <a-select
                v-model:value="formState.triggerDevicesStr"
                :options="triggerEquipmentOptions"
                placeholder="请选择"
                allowClear
                mode="tags"
                :disabled="!formState.sysOrgCode"
              ></a-select> </a-form-item
          ></a-col>
        </a-row>
        <a-row style="display: flex; justify-content: space-between">
          <a-col :span="12">
            <a-form-item label="执行条件" name="key">
              <a-select
                v-model:value="formState.key"
                :options="keyOptions"
                placeholder="请选择告警名称"
                allowClear
                @change="handleKeyChange"
              ></a-select> </a-form-item
          ></a-col>
          <a-col :span="4">
            <a-form-item name="symbol" :wrapper-col="{ span: 24 }">
              <a-select v-model:value="formState.symbol" :options="symbolOptions" placeholder="请选择" allowClear></a-select> </a-form-item
          ></a-col>
          <a-col :span="7">
            <a-form-item name="value" :wrapper-col="{ span: 24 }">
              <a-select
                v-model:value="formState.value"
                :options="valueOptions"
                :placeholder="!formState.key ? '请先选择告警名称' : '请选择条件'"
                :disabled="!formState.key"
                allowClear
              ></a-select> </a-form-item
          ></a-col>
        </a-row>
        <a-row>
          <a-col :span="24">
            <a-form-item label="执行设备" name="executionDeviceId">
              <a-select
                v-model:value="formState.executionDeviceId"
                :options="actuatingEquipmentOptions"
                placeholder="请选择"
                allowClear
                :disabled="!formState.sysOrgCode"
              ></a-select> </a-form-item
          ></a-col>
        </a-row>
        <a-row>
          <a-col :span="24">
            <a-form-item label="立即启用" name="checked" style="padding-left: 11px"> <a-switch v-model:checked="formState.statusFlag" /> </a-form-item
          ></a-col>
        </a-row>
      </a-form>
    </div>
  </a-modal>
</template>
<script lang="ts" setup>
  import { defineProps, defineEmits, ref, watch, onMounted, inject } from 'vue';
  import { ValidateErrorEntity } from 'ant-design-vue/lib/form/interface';
  import type { SelectProps } from 'ant-design-vue';
  import { message } from 'ant-design-vue';
  import ApiListOptions from '/@/api/type';
  import { deviceApi } from '/@/views/equipManage/deviceManageList/data.api';

  const $http: ApiListOptions | undefined = inject('api');
  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '',
    },
    policeInfoDialogData: {
      type: Object,
    },
    sysMultiOrgCode: {
      type: Array,
    },
  });
  let myDepartList = ref<any>([]); //当前用户所在部门
  let formState = ref<any>({});
  // const SHOW_CHILD = TreeSelect.SHOW_CHILD;
  // 数据来源
  const triggerEquipmentOptions = ref<SelectProps['options']>([]);
  // const filterTreeNode = (input: string, treeNode: { name: string }): boolean => {
  //   return treeNode.name.indexOf(input) > -1;
  // };
  // 执行条件
  const keyOptions = ref<SelectProps['options']>([]);
  const symbolOptions = ref<SelectProps['options']>([
    {
      value: '=',
      label: '=',
    },
    // {
    //   value: '>',
    //   label: '>',
    // },
    // {
    //   value: '≥',
    //   label: '≥',
    // },
    // {
    //   value: '<',
    //   label: '<',
    // },
    // {
    //   value: '≤',
    //   label: '≤',
    // },
  ]);
  const valueOptions = ref<SelectProps['options']>([]);
  // 执行设备 取机场设备
  const actuatingEquipmentOptions = ref<SelectProps['options']>([]);

  const handleKeyChange = (value: string) => {
    if (value) {
      valueOptions.value = [];
      const index = result.value.findIndex((item) => item.id === value);
      if (index !== -1) {
        result.value[index].valueData.forEach((item: any) => {
          valueOptions.value?.push({ label: item.value, value: item.key });
        });
      }
    } else {
      valueOptions.value = [];
      formState.value.value = undefined;
    }
  };

  const validateConName = (_rule: any, value: string): Promise<void> => {
    // 调用接口查用户输入的名称是否已存在，存在则提示用户该名称已存在
    return new Promise((resolve, reject) => {
      // 编辑，input为空的时候不进行验证
      if (!value || props.title == '编辑接警配置') {
        resolve();
      } else {
        $http
          ?.alarmConfigListPost({ name: value })
          .then((res: any) => {
            if (res.records.length > 0) {
              res.records.forEach((item: any) => {
                if (item.name === value) {
                  reject('该接警名称已存在，请重新输入！');
                }
              });
            } else {
              resolve();
            }
          })
          .catch((error) => {
            reject(`验证接警名称时出错：${error.message}`);
          });
      }
    });
  };
  // 表单验证规则
  const rules = {
    sysOrgCode: [{ required: true, message: '请选择部门', trigger: 'change' }],
    name: [
      { required: true, message: '请输入设备名称', trigger: 'blur' },
      { pattern: new RegExp('^.{1,20}$'), message: '长度必须在1到20个字符之间', trigger: 'blur' },
      { validator: validateConName, trigger: 'blur' },
    ],
    triggerDevicesStr: [{ required: true, message: '请选择触发设备', trigger: 'change' }],
    key: [{ required: true, message: '请选择执行条件', trigger: 'change' }],
    symbol: [{ required: true, message: '请选择执行条件', trigger: 'change' }],
    value: [{ required: true, message: '请选择执行条件', trigger: 'change' }],
    executionDeviceId: [{ required: true, message: '请选择执行设备', trigger: 'change' }],
  };
  const emit = defineEmits(['handleclose', 'handleSearch']);

  let formRef = ref();

  const labelCol = ref({
    style: { width: '100px' },
  });
  const wrapperCol = ref({
    span: 20,
  });
  // 确定按钮
  const handleOk = () => {
    formRef.value
      .validate()
      .then(() => {
        // 判断选中设备是否绑定机场，以及当前接警名称是否已经存在
        // 先判断启用状态

        if (formState.value.statusFlag) {
          // 后端做判断
          console.log('需要判断触发设备是否绑定机场');
        }
        formState.value.triggerConditionList = [];
        formState.value.triggerConditionList.push({
          key: formState.value.key,
          symbol: formState.value.symbol,
          value: formState.value.value,
        });
        console.log('values', formState.value, props.title);
        if (props.title === '新建接警配置') {
          $http?.alarmConfigAddPost(formState.value).then(() => {
            message.success('新增成功！');
            emit('handleclose');
            emit('handleSearch');
          });
        } else {
          $http?.alarmConfigEditPost(formState.value).then(() => {
            message.success('编辑成功！');
            emit('handleclose');
            emit('handleSearch');
          });
        }
      })
      .catch((error: ValidateErrorEntity<any>) => {
        console.log('error', error);
      });
  };
  // 关闭弹窗，重置校验规则
  const cancel = () => {
    emit('handleclose');
    formRef.value.clearValidate();
    valueOptions.value = [];
  };

  const initData = async () => {
    const result = await deviceApi.list({
      deviceType: '3',
      sysOrgCode: formState.value.sysOrgCode,
      pageNo: 1,
      pageSize: 200,
    });
    const { records = [] } = result;
    if (records?.length) {
      const resData: any[] = records.map((item) => {
        return {
          ...item,
          value: item.deviceId,
          label: item.deviceName,
        };
      });
      actuatingEquipmentOptions.value = resData;
    } else {
      formState.value.executionDeviceId = [];
      actuatingEquipmentOptions.value = [];
    }
  };
  const initDataCamera = async () => {
    const result = await deviceApi.cameraList({
      deviceType: '4',
      sysOrgCode: formState.value.sysOrgCode,
      pageNo: 1,
      pageSize: 200,
    });
    const { records = [] } = result;
    if (records?.length) {
      const resData: any[] = records.map((item) => {
        return {
          ...item,
          value: item.deviceId,
          label: item.deviceName,
        };
      });
      triggerEquipmentOptions.value = resData;
    } else {
      formState.value.triggerDevicesStr = [];
      triggerEquipmentOptions.value = [];
    }
  };
  const getDevice = () => {
    formState.value.executionDeviceId = [];
    formState.value.triggerDevicesStr = [];
    initData();
    initDataCamera();
  };
  let result = ref<any>([]);
  const initDataConfigLondition = async () => {
    result.value = await $http?.postAlarmConfigLondition({});
    if (result.value?.length > 0) {
      const resData: any[] = result.value.map((item) => {
        return {
          ...item,
          value: item.id,
          label: item.conName,
        };
      });
      keyOptions.value = resData;
    }
  };
  onMounted(() => {
    myDepartList.value.push(...JSON.parse(localStorage.getItem('myDepartList') || '[]'));
    // 获取执行设备列表
    // initData();
    // 获取摄像头列表
    // initDataCamera();
    // 执行条件
    initDataConfigLondition();
  });
  defineExpose({
    triggerEquipmentOptions,
    actuatingEquipmentOptions,
  });

  // 监听父组件传来的表单数据，触发视图更新
  watch(
    () => props.policeInfoDialogData,
    () => {
      formState.value = props.policeInfoDialogData;
      console.log('弹窗数据', formState.value);
      // 编辑时，拿当前的key，获取value数据
      if (formState.value.key) {
        valueOptions.value = [];
        const index = result.value.findIndex((item) => item.id === formState.value.key);
        if (index !== -1) {
          result.value[index].valueData.forEach((item: any) => {
            valueOptions.value?.push({ label: item.value, value: item.key });
          });
        }
      }
    },
    { deep: true }
  );
</script>
<style lang="less" scoped>
  .ant-modal-body {
    padding: 20px;
  }
  #container {
    width: 100%;
    height: 300px;
  }
  .custom-class :deep(.ant-form-item-control-input-content) {
    display: flex;
    justify-content: space-between;
  }
</style>
