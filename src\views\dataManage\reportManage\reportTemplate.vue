<template>
  <div class="report-template-wrapper">
    <div class="title-box">
      <span>选择模板</span>
    </div>
    <div class="content-box">
      <div class="select-template">
        <div class="template-item" @click="selectTemplate('0')">
          <img :class="templateIndex === '0' ? 'active' : ''" :src="weekReport2" />
          <span>周报模板</span>
        </div>
        <div class="template-item" @click="selectTemplate('1')">
          <img :class="templateIndex === '1' ? 'active' : ''" :src="monthReport2" />
          <span>月报模板</span>
        </div>
      </div>
      <week-report v-if="templateIndex === '0'" ref="weekComponent" :dataInfo="dataInfo" />
      <month-report v-else ref="monthComponent" :dataInfo="dataInfo" />
    </div>
    <div class="report-modal-bottom">
      <div class="bottom-box">
        <div></div>
        <div class="bottom-btn">
          <a-button @click="saveTemplate()">存为草稿</a-button>
          <a-button type="primary" @click="PDFExport">导出</a-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted, ref } from 'vue';
import { reportManageApi } from './reportManage.api';
import weekReport from './components/weekReport.vue';
import monthReport from './components/monthReport.vue';
import weekReport2 from '/@/assets/svg/week_report_2.svg';
import monthReport2 from '/@/assets/svg/month_report_2.svg';
import html2pdf from 'html2pdf.js';
import html2canvas from 'html2canvas';
import { message } from 'ant-design-vue';
const props = defineProps({
  editRowId: {
    type: String,
    default: '',
  },
  sysOrgCode: {
    type: String,
    default: '',
  },
});
const weekComponent = ref(null);
const monthComponent = ref(null);
const templateIndex = ref('0');
const selectTemplate = (val) => {
  console.log('val==>>', val);
  templateIndex.value = val;
  console.log('templateIndex.value==>>', templateIndex.value);
};

const emit = defineEmits(['handleCancel']);
// 存为草稿
const saveTemplate = () => {
  let params = {
    id: props.editRowId,
    reportType: templateIndex.value,
    sysOrgCode: props.sysOrgCode,
  };
  reportManageApi.saveDraft(params).then((res) => {
    emit('handleCancel');
  });
};

const dataInfo = ref({});
// 获取数据
const getData = () => {
  let params = {
    id: props.editRowId,
    sysOrgCode: props.sysOrgCode,
  };
  reportManageApi.get(params).then((res) => {
    // 根据上级类别排序
    res.reportContent.inspectionContents.sort((a, b) => a.parentLabelId - b.parentLabelId);
    // 获取模板类型
    res.reportType ? (templateIndex.value = res.reportType) : '';
    dataInfo.value = res;
  });
};

const PDFExport = () => {
  message.loading('pdf生成中...');
  let grandchildElement = null;
  // 选中周模板还是月模板
  if (templateIndex.value === '0') {
    grandchildElement = weekComponent.value.$refs.targetComponent;
  } else {
    grandchildElement = monthComponent.value.$refs.targetComponent;
  }

  if (grandchildElement) {
    // 保存原始样式
    const originalOverflow = grandchildElement.style.overflow;
    const originalWidth = grandchildElement.style.width;
    const originalHeight = grandchildElement.style.height;

    // 临时调整样式以捕获整个内容
    grandchildElement.style.overflow = 'visible';
    grandchildElement.style.width = `${grandchildElement.scrollWidth}px`;
    grandchildElement.style.height = `${grandchildElement.scrollHeight}px`;
    // 使用 html2canvas 捕获整个滚动区域的内容
    html2canvas(grandchildElement, {
      scrollX: 0,
      scrollY: 0,
      width: grandchildElement.scrollWidth,
      height: grandchildElement.scrollHeight,
      useCORS: true,
      scale: 1.5,
      backgroundColor: null,
    })
      .then((canvas) => {
        // 恢复原始样式
        grandchildElement.style.overflow = originalOverflow;
        grandchildElement.style.width = originalWidth;
        grandchildElement.style.height = originalHeight;

        // 将捕获的画布转换为 PDF
        html2pdf()
          .from(canvas)
          .set({
            margin: [0, 0, 0, 0],
            filename: dataInfo.value.reportName + dataInfo.value.id + '.pdf',
            image: { type: 'jpeg', quality: 0.98 },
            html2canvas: { scale: 1.5, useCORS: true },
            jsPDF: { unit: 'px', format: [544, 740], orientation: 'portrait' },
            pagebreak: {
              mode: ['avoid-all'],
              avoid: ['img', 'table'],
              before: ['.page-break-before'],
              after: ['.page-break-after'],
            },
          })
          .outputPdf('blob') // Change to output the PDF as a Blob
          .then((blob) => {
            // 调用上传PDF的函数，并传递生成的Blob对象
            uploadPDF(dataInfo.value.id, blob);
          });
      })
      .catch((error) => {
        console.error('PDF 下载失败', error);
      });
  } else {
    console.error('无法获取孙组件的 DOM 元素');
  }
};

const uploadPDF = (fileId, blob) => {
  console.log('uploadPDF');
  var formData = new FormData();
  // var blob = pdf.output('blob'); // Get the PDF as a Blob
  formData.append('file', blob, dataInfo.value.reportName + dataInfo.value.id + '.pdf');

  reportManageApi
    .upload(fileId, templateIndex.value, props.sysOrgCode, formData)
    .then((data) => {
      console.log('Success:', data);

      emit('handleCancel');
    })
    .catch((error) => {
      console.error('Error:', error);
    });
};

defineExpose({ templateIndex });

onMounted(() => {
  getData();
});
</script>

<style lang="less" scoped>
.report-template-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .title-box {
    height: 50px;
    line-height: 50px;
    font-weight: 500;
    font-size: 14px;
    color: #333333;
    border-bottom: 1px solid #eaeaea;
  }
  .content-box {
    display: flex;
    flex: 1;
    .select-template {
      width: 195px;
      height: 100%;
      border-right: 1px solid #eaeaea;
      div {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-top: 20px;
        img {
          margin-bottom: 10px;
          &.active {
            border: 2px solid #1777ff;
          }
        }
      }
    }
  }
}

.report-modal-bottom {
  height: 74px;
  .bottom-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 74px;
    border-top: 1px solid #eaeaea;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 0 20px;
  }
  .number {
    color: #1777ff;
    margin: 0 4px;
  }
  .bottom-btn {
    display: flex;
    > button {
      margin-left: 15px;
    }
  }
}
</style>