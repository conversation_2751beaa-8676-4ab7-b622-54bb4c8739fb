<template>
  <a-spin :spinning="loading">
    <!-- <BasicForm @register="registerForm" /> -->
    <a-form ref="formRef" :model="model" :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }" :rules="rules">
      <a-row>
        <a-col :span="24">
          <a-form-item label="部门名称" name="departName"> <a-input v-model:value="model.departName" placeholder="请输入部门名称" /> </a-form-item
        ></a-col>
      </a-row>
      <a-row>
        <a-col :span="24">
          <a-form-item label="上级部门">
            <a-tree-select v-model:value="model.parentId" :tree-data="props.rootTreeData" :disabled="true" style="width: 100%">
            </a-tree-select> </a-form-item
        ></a-col>
      </a-row>
      <a-row>
        <a-col :span="24">
          <a-form-item label="部门编码"> <a-input v-model:value="model.orgCode" :disabled="true" placeholder="无" /> </a-form-item
        ></a-col>
      </a-row>
      <a-row>
        <a-col :span="24">
          <a-form-item label="排序" name="departOrder">
            <a-input-number id="inputNumber" v-model:value="model.departOrder" :min="0" :max="9999" placeholder="请输入排序" /> </a-form-item
        ></a-col>
      </a-row>
      <a-row>
        <a-col :span="24">
          <a-form-item label="部门管理员" name="directorUserIds">
            <a-select
              v-model:value="model.directorUserIds"
              allowClear
              :open="false"
              @click="toggleDepartAdmin"
              @deselect="changeDepartAdmin"
              :options="ueserListOptions"
              mode="multiple"
              placeholder="请选择系统用户或创建新的管理员部门账号"
              style="width: 100%"
            >
            </a-select> </a-form-item
        ></a-col>
      </a-row>
      <div v-if="showDepartAdmin" class="departTable">
        <a-row>
          <a-form-item label="用户账号" :label-col="{ span: 8 }" :wrapper-col="{ span: 15 }">
            <a-input v-model:value="queryParam.username" placeholder="请输入用户账号" />
          </a-form-item>
          <a-form-item label="用户名称" :label-col="{ span: 7 }" :wrapper-col="{ span: 15 }">
            <a-input v-model:value="queryParam.realname" placeholder="请输入用户名称" />
          </a-form-item>
          <a-button type="primary" preIcon="ant-design:search-outlined" @click="searchQuery" class="PM"> 查询</a-button>
          <a-button preIcon="ant-design:reload-outlined" @click="restQuery" class="PM"> 重置</a-button>
          <a-button type="primary" preIcon="ant-design:plus-outlined" @click="handleCreate" class="PM PR"> 新增</a-button>
          <!-- <a-button type="primary" @click="handleOK" class="PM"> 确认</a-button> -->
        </a-row>
        <!--引用表格-->
        <BasicTable @register="registerTable" :rowSelection="rowSelection" @selection-change="changeSelection">
          <!--插槽:table标题-->

          <!--操作栏-->
        </BasicTable>
        <!--用户抽屉-->
        <UserDrawer @register="registerDrawer" @success="handleSuccess" />
      </div>
      <a-row>
        <a-col :span="24">
          <a-form-item label="部门地址" name="address">
            <a-select
              ref="mapSelect"
              v-model:value="model.address"
              show-search
              :placeholder="isInnerNet ? '点击选择部门地址' : '请选择或输入部门地址'"
              :default-active-first-option="false"
              :show-arrow="false"
              :filter-option="false"
              :not-found-content="null"
              :allowClear="false"
              @search="handleSearch"
              @click="toggleMap"
              :disabled="isInnerNet && showMap"
            >
              <a-select-option v-for="item in options" :value="item.id" @click="currentSelect(item)">{{ item.name }}</a-select-option>
            </a-select>
            <!-- <a-input v-model:value="model.installationSite" placeholder="请输入地址" @click="toggleMap" @change="changeVal" /> -->
            <div v-if="showMap" style="display: block; width: 100%; margin-top: 20px">
              <!-- 这里是地图组件或内容 -->
              <div id="MapContainerFrom"></div>
              <div class="input" style="width: 50%; position: absolute; top: 60px; left: 10px" v-if="isInnerNet">
                <a-row>
                  <a-col :span="11" style="margin-right: 10px; height: 32px">
                    <a-form-item name="longitude" style="margin-bottom: 0">
                      <a-input v-model:value="model.longitude" placeholder="请输入经度" @blur="validateMarker" /> </a-form-item
                  ></a-col>
                  <a-col :span="11" style="height: 32px">
                    <a-form-item name="latitude" style="margin-bottom: 0">
                      <a-input v-model:value="model.latitude" placeholder="请输入纬度" @blur="validateMarker" /> </a-form-item
                  ></a-col>
                </a-row>
              </div>
            </div> </a-form-item
        ></a-col>
      </a-row>
    </a-form>
    <div class="j-box-bottom-button offset-20" style="margin-top: 30px">
      <div class="j-box-bottom-button-float" :class="[`${prefixCls}`]">
        <a-button preIcon="ant-design:sync-outlined" @click="onReset" :disabled="model.disableCheckbox">重置</a-button>
        <a-button type="primary" preIcon="ant-design:save-filled" @click="onSubmit" :disabled="model.disableCheckbox">保存</a-button>
      </div>
    </div>
  </a-spin>
</template>

<script lang="ts" setup>
  import { useListPage } from '/@/hooks/system/useListPage';
  import { departColumns, departSearchFormSchema } from '@/views/system/user/user.data';
  import UserDrawer from '@/views/system/user/UserDrawer.vue';
  import { useDrawer } from '/@/components/Drawer';
  import { listNoCareTenant, deleteUser, batchDeleteUser, getImportUrl, getExportUrl, frozenBatch, syncUser } from '@/views/system/user/user.api';
  import { BasicTable, TableAction, ActionItem } from '/@/components/Table';
  import { watch, reactive, computed, inject, ref, unref, onMounted, nextTick, toRaw, markRaw } from 'vue';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { UserList } from '../depart.api';
  import { saveOrUpdateDepart } from '../depart.api';
  import { useBasicFormSchema, orgCategoryOptions } from '../depart.data';
  import { useDesign } from '/@/hooks/web/useDesign';
  import type { SelectProps } from 'ant-design-vue';
  import * as mars3d from 'mars3d';
  import * as Cesium from 'mars3d-cesium';
  import defaultMapConfig from '@/views/mapView/map3d.config.json';
  import { useBasemapConfig } from '@/hooks/web/useBasemapConfig';
  import { getIconUrl } from '/@/utils';
  import { message } from 'ant-design-vue';
  import { validatorLongitude, validatorLatitude, exceedsSixDecimalPlaces } from '/@/utils/validateLngLat';
  const { configMap } = useBasemapConfig();
  let isInnerNet = ref<boolean>(false); // 内外网标识
  const showMap = ref<boolean>(false); //地图显隐
  let mapSelect = ref();
  let map: any = null;
  let mapbacklayer: any = null;
  let currentMarker: any = null;
  let QueryPOI: any = null;
  let options: any = ref([]); // 搜索提示信息
  let ueserListOptions = ref<SelectProps['options']>([]);
  const showDepartAdmin = ref<boolean>(false); //用户表格显隐
  let queryParam = reactive({
    username: '',
    realname: '',
  });
  //注册drawer
  const [registerDrawer, { openDrawer }] = useDrawer();
  // 列表页面公共参数、方法
  const { prefixCls: tablePrefixCls, tableContext } = useListPage({
    designScope: 'user-list',
    tableProps: {
      title: '用户列表',
      api: UserList,
      columns: departColumns,
      size: 'small',
      formConfig: {
        // labelWidth: 200,
        schemas: departSearchFormSchema,
      },
      showActionColumn: false,
      showTableSetting: false,
      useSearchForm: false,
      beforeFetch: (params) => {
        return Object.assign({ column: 'createTime', order: 'desc' }, params);
      },
    },
  });
  //注册table数据
  const [registerTable, { reload, setProps, setSelectedRowKeys, getDataSource }, { rowSelection, selectedRows, selectedRowKeys }] = tableContext;
  function handleCreate() {
    openDrawer(true, {
      isUpdate: false,
      showFooter: true,
      tenantSaas: false,
      departDisabled: true,
    });
  }
  function handleSuccess() {
    reload().then(() => {
      // console.log('111111111111111', getDataSource());
      // 拿到列表第一条数据，并赋值给表单
      selectedRowKeys.value.push(getDataSource()[0].id);
    });
    getUeser();
    // model.value.directorUserIds.push(getDataSource()[0].id);
  }
  function searchQuery() {
    setProps({ searchInfo: toRaw(queryParam) });
    reload();
  }
  function restQuery() {
    queryParam.username = '';
    queryParam.realname = '';
    reload();
  }
  const jobImgUrl = (name: string, suffix = 'svg') => {
    return getIconUrl(name, 'icons/remoteOverTwo/', suffix);
  };
  let formRef: any = ref(null);
  const rules = {
    departName: [
      { required: true, message: '部门名称不能为空', trigger: 'blur' },
      { pattern: new RegExp('^.{1,50}$'), message: '长度必须在1到50个字符之间', trigger: 'blur' },
      // { validator: validateDepartName, trigger: 'blur' },
    ],
    directorUserIds: [{ required: true, message: '部门管理员不能为空', trigger: 'change' }],
    longitude: [{ validator: validatorLongitude, trigger: 'blur' }],
    latitude: [{ validator: validatorLatitude, trigger: 'blur' }],
    address: [{ required: true, message: '安装位置不能为空', trigger: 'change' }],
  };
  const { prefixCls } = useDesign('j-depart-form-content');

  const emit = defineEmits(['success']);
  const props = defineProps({
    data: { type: Object, default: () => ({}) },
    rootTreeData: { type: Array, default: () => [] },
  });
  const loading = ref<boolean>(false);
  // 当前是否是更新模式
  const isUpdate = ref<boolean>(true);
  // 当前的弹窗数据
  const model = ref<any>({});

  onMounted(() => {
    isInnerNet.value = localStorage.getItem('environmentTag') === 'privateCloud' ? true : false;
    getUeser();
  });

  const toggleDepartAdmin = () => {
    showDepartAdmin.value = !showDepartAdmin.value;
    // const departAdminDom = JSON.parse(JSON.stringify(model.value.directorUserIds));
    if (showDepartAdmin.value) {
      nextTick(() => {
        selectedRowKeys.value = model.value.directorUserIds;
        // console.log('22222222222222', selectedRowKeys.value);
      });
    }
  };

  function changeSelection() {
    setTimeout(() => {
      // console.log('3333333333333333', selectedRowKeys.value, selectedRows.value, model.value.directorUserIds);
      if (selectedRowKeys.value.length === 0 && model.value.directorUserIds.length > 0) {
        model.value.directorUserIds = [];
      }
    }, 500);

    if (selectedRowKeys.value.length > 0) {
      model.value.directorUserIds = selectedRowKeys.value;
      // console.log('55555555555555555', selectedRowKeys.value, selectedRows.value, model.value.directorUserIds);
    }
    // console.log('4444444444444', selectedRowKeys.value, selectedRows.value, model.value.directorUserIds);
  }

  function changeDepartAdmin(value, _option) {
    // console.log('1111111111111111', selectedRowKeys.value, model.value.directorUserIds);
    if (model.value.directorUserIds.length === 0) {
      selectedRowKeys.value = [];
    } else {
      // console.log('77777777777777', value, option);
      const idx = selectedRowKeys.value.findIndex((item) => item === value);
      // console.log('888888888888888', idx);

      if (idx > -1) {
        selectedRowKeys.value.splice(idx, 1);
      }
    }
  }

  // data 变化，重填表单
  watch(
    () => props.data,
    async () => {
      // console.log('props.data', props.data);
      if (JSON.stringify(props.data) === '{}' || !props.data) return;
      const newData = JSON.parse(JSON.stringify(props.data));
      // console.log('newData', newData);
      newData.directorUserIds = newData.directorUserIds ? newData.directorUserIds.split(',') : [];
      // 更新 model 数据
      model.value = newData;
      // console.log('newData', model.value);
      // model.value.directorUserIds = newDirectorUserIds;
      // 重置表格显示状态
      showDepartAdmin.value = false;

      await formRef.value?.clearValidate();
      if (showMap.value) {
        markerFun(model.value.longitude, model.value.latitude, true);
      }
    },
    { deep: true, immediate: true }
  );
  // 重置表单 将会重置为原始数据而不是清空
  async function onReset() {
    await formRef.value?.clearValidate();
    model.value = JSON.parse(JSON.stringify(props.data));
    model.value.directorUserIds = model.value.directorUserIds ? model.value.directorUserIds.split(',') : [];
    // await formRef.value?.setFieldsValue({ ...model.value });
    // 重新打点
    if (showMap.value) {
      markerFun(model.value.longitude, model.value.latitude, true);
    }
  }

  // 提交事件
  async function onSubmit() {
    try {
      loading.value = true;
      let values = await formRef.value?.validate();
      values = Object.assign({}, model.value, values);
      // console.log('values', values);
      values.directorUserIds = values.directorUserIds.join(',');
      //提交表单
      await saveOrUpdateDepart(values, isUpdate.value);
      //刷新列表
      emit('success');
      values.directorUserIds = values.directorUserIds.split(',');
      Object.assign(model.value, values);
    } finally {
      loading.value = false;
    }
  }
  // 获取用户列表焦点
  const handleFocus = () => {
    getUeser();
  };

  const getUeser = () => {
    UserList({ pageNo: 1, pageSize: 1000 }).then((res) => {
      ueserListOptions.value = [];
      // console.log('oooo--999', res);
      res?.records.forEach((item: any) => {
        ueserListOptions.value?.push({ label: item.realname, value: item.id });
      });
    });
  };
  // 封装打点方法
  const markerFun = (longitude, latitude, tag) => {
    if (currentMarker) {
      map.graphicLayer.removeGraphic(currentMarker); // 移除之前的标点
    }
    // 添加标点
    currentMarker = new mars3d.graphic.BillboardEntity({
      position: [longitude, latitude, 0], // 位置使用经纬度
      flyTo: tag, // 飞向该点
      style: {
        image: jobImgUrl('点', 'png'),
        clampToGround: true,
        horizontalOrigin: mars3d.Cesium.HorizontalOrigin.CENTER,
        verticalOrigin: mars3d.Cesium.VerticalOrigin.BOTTOM,
        pixelOffsetX: 8,
      },
    });

    map.graphicLayer.addGraphic(currentMarker);
    // 清除经纬度的校验
    if (formRef.value) {
      formRef.value.clearValidate(['longitude', 'latitude', 'address']);
    }
  };
  // 表单打点验证
  const validateMarker = () => {
    console.log(model.value.longitude, model.value.latitude);

    if (
      model.value.longitude &&
      model.value.latitude &&
      !exceedsSixDecimalPlaces(model.value.longitude) &&
      !exceedsSixDecimalPlaces(model.value.latitude)
    ) {
      markerFun(model.value.longitude, model.value.latitude, true);
      model.value.address = model.value.longitude + '，' + model.value.latitude;
    } else {
      if (currentMarker) {
        map.graphicLayer.removeGraphic(currentMarker); // 移除之前的标点
      }
    }
  };
  const currentSelect = (val) => {
    model.value.address = val.address + val.name;
    model.value.longitude = val.lng;
    model.value.latitude = val.lat;
    markerFun(val.lng, val.lat, true);
    options.value = [];
  };
  let timer: any = null;
  // 联网搜素
  const handleSearch = (query: string) => {
    options.value = [];
    if (timer !== null) {
      clearTimeout(timer);
    }

    timer = setTimeout(() => {
      console.log('11111111', query);
      QueryPOI?.queryText({
        text: query,
        success: function (data) {
          console.log('联网搜素', data);

          options.value = data?.list;
        },
        error: function (error) {
          console.log('联网搜素失败', error);
        },
      });
    }, 500);
  };
  // 点击地图
  const handleMapClick = (event: any) => {
    console.log('我点击地图了！', event);
    // map?.clear(true); // 清空地图上的所有标注
    // 获取点击的笛卡尔坐标
    const cartesian = event.cartesian;
    if (!cartesian) {
      console.log('无效的点击位置');
      return;
    }

    // 将笛卡尔坐标转换为经纬度
    const cartographic = Cesium.Cartographic.fromCartesian(cartesian);
    const longitude = Number(Cesium.Math.toDegrees(cartographic.longitude).toFixed(6));
    const latitude = Number(Cesium.Math.toDegrees(cartographic.latitude).toFixed(6));

    // 输出经纬度
    console.log('点击的经度:', longitude);
    console.log('点击的纬度:', latitude);
    markerFun(longitude, latitude, false);
    model.value.longitude = longitude;
    model.value.latitude = latitude;
    // 判断是否是内网部署
    // 不是内网
    console.log('是否内网部署', isInnerNet.value);

    if (!isInnerNet.value) {
      QueryPOI.getAddress({
        location: [longitude, latitude],
        success: function (data) {
          console.log('逆解析', data);
          model.value.address = data.address;
        },
      });
    } else {
      model.value.address = `${longitude}，${latitude}`;
    }
  };
  // 初始化地图
  const toggleMap = async () => {
    options.value = [];
    if (showMap.value) {
      return;
    }

    try {
      if (map) {
        map.destroy();
        map = null;
      }

      showMap.value = true;
      await nextTick(); // 确保DOM更新完成

      const container = document.getElementById('MapContainerFrom');
      if (!container) {
        throw new Error('Map MapContainerFrom not found');
      }
      const mars3dConfig = mars3d.Util.merge(defaultMapConfig.map3d, toRaw(configMap));
      map = new mars3d.Map('MapContainerFrom', mars3dConfig);

      mapbacklayer = new mars3d.layer.GraphicLayer({
        allowDrillPick: true, // 如果存在坐标完全相同的图标点，可以打开该属性，click事件通过graphics判断
      });
      map.addLayer(mapbacklayer);
      map.on(mars3d.EventType.click, handleMapClick);
      // 判断是否是内网部署
      if (!isInnerNet.value) {
        QueryPOI = new mars3d.query.QueryPOI({ service: 'tdt', key: 'dc2ede760d49216f0ab31b140f032ed8' });
      }
      if (model.value.longitude && model.value.latitude && model.value.address) {
        markerFun(model.value.longitude, model.value.latitude, true);
        map.setCameraView({ lng: model.value.longitude, lat: model.value.latitude, alt: 4000 }, { duration: 0.1 });
      } else {
        let latitudeLongitude = localStorage.getItem('latitudeLongitude'),
          center = latitudeLongitude ? JSON.parse(latitudeLongitude) : [116.478935, 39.997761];
        map.setCameraView({ lng: center[0], lat: center[1], alt: 4000 }, { duration: 0.1 });
      }
    } catch (error) {
      console.error('Map initialization failed:', error);
      showMap.value = false;
      message.error('地图初始化失败');
    }
  };

  defineExpose({
    getUeser,
  });
</script>
<style lang="less" scoped>
  :deep(.jeecg-basic-table-header__toolbar) {
    width: 0px !important;
  }
  // update-begin-author:liusq date:20230625 for: [issues/563]暗色主题部分失效

  @prefix-cls: ~'@{namespace}-j-depart-form-content';
  /*begin 兼容暗夜模式*/
  .@{prefix-cls} {
    background: @component-background;
    border-top: 1px solid @border-color-base;
  }
  /*end 兼容暗夜模式*/
  // update-end-author:liusq date:20230625 for: [issues/563]暗色主题部分失效
  .btn {
    margin-bottom: 0px;
  }
  .departTable {
    // width: 50%;
    padding: 0 6%;
    // display: flex;
    // justify-content: center;
    .PM {
      padding: 0 10px;
      margin-right: 10px;
    }
    // .serachPR {
    //   position: absolute;
    //   right: 100px;
    // }
    // .restPR {
    //   position: absolute;
    //   right: 0px;
    // }
    // .addPR {
    //   position: absolute;
    //   right: 0px;
    // }
  }
</style>
