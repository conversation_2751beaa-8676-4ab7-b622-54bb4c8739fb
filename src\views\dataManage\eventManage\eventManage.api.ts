import { defHttp } from '/@/utils/http/axios';

enum Api {
  getList = '/uav/ticket/list', //获取事件工单列表
  upload = '/file/manage/upload', //上传图片
  queryAllUser = '/uav/ticket/queryAllUser', // 查询所有用户
  markCategoryList = '/uav/markCategory/list', // 标记类别列表
  eventDeal = '/uav/ticket/edit', // 事件处理提交
  deleteFile = '/file/manage/delete-file', // 未关联事件工单的文件，删除接口
  deleteEventFile = '/uav/ticket/deleteTicketFile', // 关联事件工单的文件，删除接口
  queryDetail = '/uav/ticket/queryById', // 获取事件工单详情
  queryLocationFileCount = '/skyline-uav/uav/file/query-location-file-count',
  queryStaticImg = '/uav/orthoPhoto/staticImage', // 获取静态图
}
export const eventManageApi = {
  getList: (params) => defHttp.post({ url: Api.getList, params }),
  upload: (sysOrgCode, params) =>
    defHttp.post({ url: Api.upload, params, headers: { 'Content-Type': 'multipart/form-data', 'X-Sys-Org-Code': sysOrgCode } }),
  queryAllUser: (params) => defHttp.post({ url: Api.queryAllUser, params }),
  markCategoryList: (params) => defHttp.get({ url: Api.markCategoryList, params }),
  eventDeal: (params) => defHttp.post({ url: Api.eventDeal, params }),
  deleteFile: (params) => defHttp.delete({ url: `${Api.deleteFile}?id=${params}` }),
  deleteEventFile: (params) => defHttp.delete({ url: `${Api.deleteEventFile}?id=${params.id}&fileId=${params.fileId}` }),
  queryDetail: (params) => defHttp.get({ url: Api.queryDetail, params }),
  queryLocationFileCount: (params) => defHttp.post({ url: Api.queryLocationFileCount, params }),
  queryStaticImg: (params) => defHttp.post({ url: Api.queryStaticImg, params }),
};
